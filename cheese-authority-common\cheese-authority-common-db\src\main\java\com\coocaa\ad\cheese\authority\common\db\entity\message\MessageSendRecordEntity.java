package com.coocaa.ad.cheese.authority.common.db.entity.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "message_send_record")
public class MessageSendRecordEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息接收人
     */
    private Integer receiveUserId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 1:后台消息，2：飞书消息
     */
    private Integer type;

    /**
     * 群组ID
     */
    private String chatId;

    private String uuid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;
}
