package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 登陆日志
 *
 * <AUTHOR>
 * @since 2024-11-19
 */
@Data
@Accessors(chain = true)
@TableName("login_log")
public class LoginLogEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 登录IP
     */
    private String ip;

    /**
     * 登录地点
     */
    private String address;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 用户Agent
     */
    private String userAgent;

    /**
     * 来源（Web、Android等）
     */
    private String source;

    /**
     * 登陆状态 [1:成功, 0:失败]
     */
    private Boolean status;

    /**
     * 登录失败原因
     */
    private String failReason;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 登录时间
     */
    private Date loginTime;
}
