package com.coocaa.ad.cheese.authority.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 日志的实体类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-24
 */
@Getter
@AllArgsConstructor
public enum OperateLogTypeEnum {
    USER("0005-3", "用户"),
    ROLE("0005-1", "角色"),
    RESOURCE("0005-2", "资源"),
    DICT("0005-8", "字典"),
    INDUSTRY("0005-9", "行业"),
    CITY("0005-10", "城市"),
    COUNTY("0005-11", "区县"),
    CITY_GROUP("0005-12", "城市组"),

    ;


    private final static Map<String, OperateLogTypeEnum> BY_CODE_MAP =
            Arrays.stream(OperateLogTypeEnum.values())
                    .collect(Collectors.toMap(type -> type.getCode().toLowerCase(), type -> type));

    private final static Map<String, OperateLogTypeEnum> BY_NAME_MAP
            = Arrays.stream(OperateLogTypeEnum.values())
            .collect(Collectors.toMap(type -> type.name().toLowerCase(), type -> type));

    private final String code;
    private final String name;


    /**
     * 将代码转成枚举
     *
     * @param code 代码
     * @return 转换出来的用户类型
     */
    public static OperateLogTypeEnum parseByCode(String code) {
        return BY_CODE_MAP.get(StringUtils.trimToEmpty(code).toLowerCase());
    }

    /**
     * 将名字转换成枚举
     *
     * @param name 名字
     * @return 转换出来的用户类型
     */
    public static OperateLogTypeEnum parseByName(String name) {
        return BY_NAME_MAP.get(StringUtils.trimToEmpty(name).toLowerCase());
    }

    /**
     * 将编码转换成名字
     *
     * @param code 代码
     * @return 名字
     */
    public static String getName(String code) {
        return Optional.ofNullable(BY_CODE_MAP.get(StringUtils.trimToEmpty(code).toLowerCase()))
                .map(OperateLogTypeEnum::getName)
                .orElse(code);
    }
}
