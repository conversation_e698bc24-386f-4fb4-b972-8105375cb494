package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.service.AiRoleService;
import com.coocaa.ad.cheese.ai.vo.AiRoleVO;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI角色管理Controller
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@RestController
@RequestMapping("/sys/ai/role")
@Tag(name = "AI角色管理", description = "AI角色相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class AiRoleController {

    private final AiRoleService aiRoleService;

    @Operation(
            summary = "获取所有角色列表",
            description = "获取所有可用的AI角色列表"
    )
    @GetMapping("/list")
    public ResultTemplate<List<AiRoleVO>> getAllRoles() {
        log.info("获取所有角色列表请求");
        
        try {
            List<AiRoleVO> roles = aiRoleService.getAllRoles();
            return ResultTemplate.success(roles);
            
        } catch (Exception e) {
            log.error("获取角色列表失败", e);
            return ResultTemplate.fail("获取角色列表失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "根据ID获取角色",
            description = "根据角色ID获取角色详细信息"
    )
    @GetMapping("/{roleId}")
    public ResultTemplate<AiRoleVO> getRoleById(
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long roleId) {
        
        log.info("根据ID获取角色，角色ID: {}", roleId);
        
        try {
            AiRoleVO role = aiRoleService.getRoleById(roleId);
            if (role == null) {
                return ResultTemplate.fail("角色不存在");
            }
            
            return ResultTemplate.success(role);
            
        } catch (Exception e) {
            log.error("获取角色失败，角色ID: {}", roleId, e);
            return ResultTemplate.fail("获取角色失败: " + e.getMessage());
        }
    }
}
