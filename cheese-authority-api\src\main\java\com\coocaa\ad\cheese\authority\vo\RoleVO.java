package com.coocaa.ad.cheese.authority.vo;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-22
 */
@Data
@JsonPropertyOrder({"id", "name", "description", "status", "resources"})
public class RoleVO implements Serializable {
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "角色名称", type = "String", example = "销售")
    private String name;

    @Schema(description = "角色描述", type = "String", example = "主要应用于销售人员")
    private String description;

    @Schema(description = "资源状态 [false:禁用, true:启用]", type = "Boolean", example = "true")
    private Boolean status;

    @Schema(description = "创建时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date createTime;

    @Schema(description = "创建人", type = "Integer", example = "1")
    private Integer creator;

    @Schema(description = "更新时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private Integer operator;

    @Schema(description = "角色拥有的资源", type = "List")
    private List<ResourceVO> resources;

    @Schema(description = "角色拥有的数据权限", type = "List")
    private Set<Integer> dataList;

    @Schema(description = "角色拥有的资源权限", type = "List")
    private Set<Integer> resourceList;
}
