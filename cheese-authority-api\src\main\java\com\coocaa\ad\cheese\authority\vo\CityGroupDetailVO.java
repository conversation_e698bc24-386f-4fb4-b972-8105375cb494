package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/24
 */
@Data
public class CityGroupDetailVO {

    private Integer id;

    //private Integer parentId;
    private String name;

    @Schema(description = "描述")
    private String remark;
    @Schema(description ="可选的城市")
    private List<GroupCitiesVO> groupCities;
}
