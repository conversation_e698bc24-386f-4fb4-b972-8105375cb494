package com.coocaa.ad.cheese.authority.common.db.entity.approve;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("approve_internal_node")
public class ApproveInternalNodeEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 规则编号 */
    private Integer ruleCode;

    /** 审批实例code */
    private String instanceCode;

    /** 规则审批人员表ID */
    private Integer personId;

    /** 审核人员排序 */
    private Integer rank;

    /** 审批人员ID */
    private String userId;

    /** 取消原因（字典0140） */
    private String cancelReason;

    /** 任务状态（字典0139） */
    private String nodeStatus;

    /** 节点开始时间 */
    private LocalDateTime startTime;

    /** 节点结束时间 */
    private LocalDateTime endTime;

    /** 是否为审批节点，0：提交人节点，1：审批节点，2：结束节点 */
    private Integer approvalFlag;

    /** 任务名称 */
    private String taskName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

}
