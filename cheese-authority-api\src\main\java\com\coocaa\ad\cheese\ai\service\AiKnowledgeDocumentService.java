package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeDocumentService;
import com.coocaa.ad.cheese.ai.utils.FileTypeUtils;
import com.coocaa.ad.cheese.ai.vo.AiKnowledgeDocumentUploadReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * AI知识库文档处理服务
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiKnowledgeDocumentService {

    private final IAiKnowledgeDocumentService documentService;
    private final DocumentContentExtractor contentExtractor;

    /**
     * 统计知识库的文档数量
     *
     * @param knowledgeId 知识库ID
     * @return 文档数量
     */
    public Integer countByKnowledgeId(Long knowledgeId) {
        log.info("统计知识库文档数量，知识库ID: {}", knowledgeId);

        LambdaQueryWrapper<AiKnowledgeDocumentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiKnowledgeDocumentEntity::getKnowledgeId, knowledgeId)
               .eq(AiKnowledgeDocumentEntity::getDeleteFlag, 0);

        return Math.toIntExact(documentService.count(wrapper));
    }

    /**
     * 保存文档
     *
     * @param file 文件
     * @param uploadReq 上传请求
     * @return 文档ID
     */
    public Long saveDocument(MultipartFile file, AiKnowledgeDocumentUploadReqVO uploadReq) {
        log.info("保存文档，文件名: {}, 知识库ID: {}", file.getOriginalFilename(), uploadReq.getKnowledgeId());

        try {
            // 1. 提取文档内容
            String content = extractContentFromFile(file);

            // 2. 创建文档实体
            AiKnowledgeDocumentEntity document = new AiKnowledgeDocumentEntity();
            document.setKnowledgeId(uploadReq.getKnowledgeId());
            document.setName(uploadReq.getName() != null ? uploadReq.getName() : file.getOriginalFilename());
            document.setFileName(file.getOriginalFilename());
            document.setFilePath(""); // TODO: 实现文件存储路径
            document.setFileSize(file.getSize());
            document.setFileType(getFileExtension(file.getOriginalFilename()));
            document.setContent(content);
            document.setStatus("uploading");
            document.setChunkCount(0);
            document.setDeleteFlag(0);

            // 3. 保存到数据库
            documentService.save(document);

            // 4. 更新状态为已上传
            document.setStatus("uploaded");
            documentService.updateById(document);

            log.info("文档保存成功，文档ID: {}, 内容长度: {}", document.getId(), content.length());
            return document.getId();

        } catch (Exception e) {
            log.error("保存文档失败，文件名: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("保存文档失败: " + e.getMessage());
        }
    }

    /**
     * 保存文本文档
     *
     * @param uploadReq 上传请求
     * @return 文档ID
     */
    public Long saveTextDocument(AiKnowledgeDocumentUploadReqVO uploadReq) {
        log.info("保存文本文档，知识库ID: {}, 内容长度: {}",
                uploadReq.getKnowledgeId(),
                uploadReq.getContent() != null ? uploadReq.getContent().length() : 0);

        try {
            // 创建文档实体
            AiKnowledgeDocumentEntity document = new AiKnowledgeDocumentEntity();
            document.setKnowledgeId(uploadReq.getKnowledgeId());
            document.setName(uploadReq.getName() != null ? uploadReq.getName() : "文本文档_" + System.currentTimeMillis());
            document.setFileName(document.getName() + ".txt");
            document.setFilePath("");
            document.setFileSize((long) uploadReq.getContent().getBytes(StandardCharsets.UTF_8).length);
            document.setFileType("txt");
            document.setContent(uploadReq.getContent());
            document.setStatus("uploaded");
            document.setChunkCount(0);
            document.setDeleteFlag(0);

            // 保存到数据库
            documentService.save(document);

            log.info("文本文档保存成功，文档ID: {}", document.getId());
            return document.getId();

        } catch (Exception e) {
            log.error("保存文本文档失败", e);
            throw new RuntimeException("保存文本文档失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档状态
     *
     * @param documentId 文档ID
     * @param status 状态
     * @param chunkCount 分块数量
     * @param errorMessage 错误信息
     */
    public void updateDocumentStatus(Long documentId, String status, Integer chunkCount, String errorMessage) {
        log.info("更新文档状态，文档ID: {}, 状态: {}, 分块数量: {}", documentId, status, chunkCount);

        try {
            AiKnowledgeDocumentEntity document = documentService.getById(documentId);
            if (document != null) {
                document.setStatus(status);
                if (chunkCount != null) {
                    document.setChunkCount(chunkCount);
                }
                if (errorMessage != null) {
                    document.setErrorMessage(errorMessage);
                }
                documentService.updateById(document);
                log.info("文档状态更新成功，文档ID: {}", documentId);
            } else {
                log.warn("文档不存在，无法更新状态，文档ID: {}", documentId);
            }
        } catch (Exception e) {
            log.error("更新文档状态失败，文档ID: {}", documentId, e);
        }
    }

    // ========== 私有方法 ==========

    /**
     * 从文件中提取内容
     */
    private String extractContentFromFile(MultipartFile file) throws IOException {
        try {
            // 使用DocumentContentExtractor提取内容
            return contentExtractor.extractContent(file);
        } catch (Exception e) {
            log.error("使用Tika提取内容失败，尝试基本提取方法", e);

            // 如果Tika提取失败，使用基本方法
            String fileName = file.getOriginalFilename();
            String fileExtension = getFileExtension(fileName);

            switch (fileExtension.toLowerCase()) {
                case "txt":
                case "md":
                case "csv":
                case "json":
                case "xml":
                case "html":
                case "htm":
                    return new String(file.getBytes(), StandardCharsets.UTF_8);
                default:
                    // 对于其他类型，返回错误信息
                    log.warn("不支持的文件类型: {}", fileExtension);
                    return "不支持的文件类型: " + fileExtension + "，请上传文本文件或使用文本内容上传。";
            }
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        return FileTypeUtils.getFileExtension(fileName);
    }
}
