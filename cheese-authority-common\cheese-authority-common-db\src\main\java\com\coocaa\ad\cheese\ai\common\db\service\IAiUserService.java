package com.coocaa.ad.cheese.ai.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.ai.common.db.bean.AiUserDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiUserEntity;

/**
 * AI Chat 对话服务接口
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
public interface IAiUserService extends IService<AiUserEntity> {

    /**
     * 分页查询
     *
     * @param aiUser 查询条件
     * @param page   分页对象
     * @return 分页结果
     */
    IPage<AiUserEntity> pageList(IPage<AiUserEntity> page, AiUserDTO aiUser);
}