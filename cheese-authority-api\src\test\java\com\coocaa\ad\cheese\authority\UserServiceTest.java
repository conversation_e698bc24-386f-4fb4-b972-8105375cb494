package com.coocaa.ad.cheese.authority;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.ad.cheese.authority.bean.UserLoginParam;
import com.coocaa.ad.cheese.authority.common.db.entity.UserEntity;
import com.coocaa.ad.cheese.authority.common.db.service.IUserService;
import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import com.coocaa.ad.cheese.authority.convert.UserConvert;
import com.coocaa.ad.cheese.authority.service.UserLoginService;
import com.coocaa.ad.cheese.authority.service.UserSaveService;
import com.coocaa.ad.cheese.authority.vo.LoginResultVO;
import com.coocaa.ad.cheese.authority.vo.UserEditVO;
import com.coocaa.ad.cheese.authority.vo.UserVO;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * 用户管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-28
 */
@DisplayName("用户管理")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class UserServiceTest extends BaseTest {
    @Autowired
    private UserLoginService userLoginService;

    @Autowired
    private UserSaveService userSaveService;

    @Autowired
    private IUserService userService;

    @Autowired
    private UserConvert userConvert;


    @Test
    @DisplayName("用户新增/修改/禁用")
    public void testCreateAndUpdateUser() {
        String mobile = "1" + RandomStringUtils.randomNumeric(10);
        String email = RandomStringUtils.randomAlphabetic(8) + "@coocaa.com";
        UserVO user = generateUser(vo -> {
            vo.setName("用于测试的姓名");
            vo.setWno("CC" + RandomStringUtils.randomNumeric(4));
            vo.setMobile(mobile);
            vo.setEmail(email);
            vo.setUserName(RandomStringUtils.randomAlphabetic(12));
            vo.setPassword(DigestUtils.md5Hex(RandomStringUtils.randomAlphabetic(16)));
        });

        ResultTemplate<Boolean> result = userSaveService.createUser(userConvert.toVo(user));
        Assertions.assertNotNull(result.getSuccess(), "创建成功");
        Assertions.assertTrue(result.getData(), "创建用户");

        // 创建后查出来检查
        UserEntity entity = userService.getOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getWno, user.getWno()));
        Assertions.assertEquals(user.getName(), entity.getName(), "姓名");
        Assertions.assertEquals(user.getWno(), entity.getWno(), "工号");
        //Assertions.assertEquals(mobile, AesUtils.decryptStr(entity.getMobile()), "手机号");
        //Assertions.assertEquals(email, AesUtils.decryptStr(entity.getEmail()), "邮箱");
        Assertions.assertEquals(user.getUserName(), entity.getUserName(), "登陆名");
        Assertions.assertEquals(user.getStatus(), entity.getStatus(), "状态");


        // 修改数据
        String updateMobile = "1" + RandomStringUtils.randomNumeric(10);
        UserEditVO updateUser = new UserEditVO();
        updateUser.setId(entity.getId());
        updateUser.setName("用于测试的用户-修改");
        updateUser.setMobile(updateMobile);
        result = userSaveService.updateUser(updateUser);
        Assertions.assertNotNull(result.getSuccess(), "修改成功");
        Assertions.assertTrue(result.getData(), "修改用户");


        // 将修改后的信息查出来检查
        entity = userService.getById(entity.getId());
        Assertions.assertEquals(updateUser.getName(), entity.getName(), "姓名");
        Assertions.assertEquals(user.getWno(), entity.getWno(), "工号");
        // Assertions.assertEquals(updateMobile, AesUtils.decryptStr(entity.getMobile()), "手机号");
        // Assertions.assertEquals(email, AesUtils.decryptStr(entity.getEmail()), "邮箱");
        Assertions.assertEquals(user.getUserName(), entity.getUserName(), "登陆名");
        Assertions.assertEquals(user.getStatus(), entity.getStatus(), "状态");


        // 禁用角色
        UserVO statusUser = new UserVO();
        statusUser.setId(entity.getId());
        statusUser.setStatus(Boolean.FALSE);
        result = userSaveService.changeStatus(statusUser);
        Assertions.assertNotNull(result.getSuccess(), "禁用成功");
        Assertions.assertTrue(result.getData(), "禁用用户");

        // 删除测试数据
        userService.removeById(entity.getId());
    }


    @Test
    @DisplayName("用户登陆")
    public void testLogin() {
        /*String mobile = "1" + RandomStringUtils.randomNumeric(10);
        String email = RandomStringUtils.randomAlphabetic(8) + "@coocaa.com";
        UserVO user = generateUser(vo -> {
            vo.setName("用于测试的姓名");
            vo.setWno("CC" + RandomStringUtils.randomNumeric(4));
            vo.setMobile(mobile);
            vo.setEmail(email);
            vo.setUserName(RandomStringUtils.randomAlphabetic(12));
            vo.setPassword(DigestUtils.md5Hex(RandomStringUtils.randomAlphabetic(16)));
        });

        ResultTemplate<Boolean> result = userSaveService.createUser(userConvert.toVo(user));
        Assertions.assertNotNull(result.getSuccess(), "创建成功");
        Assertions.assertTrue(result.getData(), "创建用户");


        // 登陆（成功）
        UserLoginParam loginParam = new UserLoginParam();
        loginParam.setName(user.getWno() );
        loginParam.setPwd(user.getPassword());
        LoginResultVO token = userLoginService.login(loginParam);
        Assertions.assertTrue(StringUtils.isNotBlank(token.getToken()), "登陆成功");

        // 测试登陆5次失败，禁用用户
        for (int i = 0; i < 5; i++) {
            loginParam.setPwd(DigestUtils.md5Hex(RandomStringUtils.randomAlphabetic(16)));
            CommonException exception = Assertions.assertThrows(CommonException.class,
                    () -> userLoginService.login(loginParam), "登陆失败");
            Assertions.assertEquals("连续重试错误5次，导致账号锁定！", exception.getMsg());
        }

        // 检查用户禁用标记
        UserEntity entity = userService.getOne(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getWno, user.getWno()));
        Assertions.assertEquals(entity.getStatus(), Boolean.FALSE, "禁用状态");

        // 启用用户
        user.setId(entity.getId());
        user.setStatus(Boolean.TRUE);
        result = userSaveService.changeStatus(user);
        Assertions.assertNotNull(result.getSuccess(), "修改成功");
        Assertions.assertTrue(result.getData(), "修改用户");

        // 检查用户启用标记
        entity = userService.getById(entity.getId());
        Assertions.assertEquals(entity.getStatus(), Boolean.TRUE, "启用状态");

        // 删除测试数据
        userService.removeById(entity.getId());*/
    }

    /**
     * 创建用户，自定义属性优先
     */
    private UserVO generateUser(Consumer<UserVO> consumer) {
        UserVO vo = new UserVO();
        vo.setStatus(true);
        vo.setCreateTime(NOW);
        vo.setCreator(USER_ID);
        vo.setUpdateTime(NOW);
        vo.setOperator(USER_ID);

        // 可自定义一些属性
        Optional.ofNullable(consumer).ifPresent(c -> c.accept(vo));

        return vo;
    }
}
