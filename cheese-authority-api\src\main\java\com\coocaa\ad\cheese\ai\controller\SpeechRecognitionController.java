package com.coocaa.ad.cheese.ai.controller;

import cn.hutool.core.lang.UUID;
import com.coocaa.ad.cheese.ai.service.WechatSpeechSdkService;
import com.coocaa.ad.cheese.ai.vo.SpeechRecognitionVO;
import com.coocaa.ad.cheese.authority.common.tools.cos.ObjectUtils;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import com.qcloud.cos.transfer.Download;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * 语音识别控制器
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Slf4j
@RestController
@RequestMapping("/sys/speech")
@Tag(name = "语音识别", description = "语音转文字服务")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SpeechRecognitionController {

    private final WechatSpeechSdkService wechatSpeechSdkService;

    /**
     * 语音转文字（使用官方SDK，通过COS URL）
     */
    @Operation(
            summary = "语音转文字（SDK版本，COS URL）",
            description = "通过COS URL下载语音文件，使用微信官方SDK进行语音识别，返回识别的文字内容",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "识别成功",
                            content = @Content(schema = @Schema(implementation = SpeechRecognitionVO.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "请求参数错误"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "服务器内部错误"
                    )
            }
    )
    @PostMapping("/recognize-sdk")
    public ResultTemplate<SpeechRecognitionVO> recognizeSpeechWithSdk(
            @Parameter(
                    description = "COS语音文件URL（支持mp3等音频格式）",
                    required = true
            )
            @RequestParam("cosUrl") String cosUrl) {

        log.info("收到语音识别请求（SDK版本），COS URL: {}", cosUrl);

        // 验证URL
        if (cosUrl == null || cosUrl.trim().isEmpty()) {
            log.warn("COS URL为空");
            return ResultTemplate.success(SpeechRecognitionVO.error("COS URL不能为空"));
        }

        File tempFile = null;
        try {
            // 1. 创建临时文件
            String fileExtension = getFileExtensionFromUrl(cosUrl);
            tempFile = File.createTempFile(UUID.fastUUID() + "_speech_", fileExtension);

            log.info("创建临时文件: {}", tempFile.getAbsolutePath());

            // 2. 从COS下载文件到临时文件
            Download download = ObjectUtils.downloadFile(cosUrl, tempFile);
            download.waitForCompletion();

            log.info("文件下载完成，文件大小: {} bytes", tempFile.length());

            // 3. 验证下载的文件
            if (!tempFile.exists() || tempFile.length() == 0) {
                log.warn("下载的文件为空或不存在");
                return ResultTemplate.success(SpeechRecognitionVO.error("下载的语音文件为空"));
            }

            // 4. 调用语音识别服务
            String recognizedText = wechatSpeechSdkService.recognizeSpeechFromFile(tempFile);

            if (recognizedText == null || recognizedText.trim().isEmpty()) {
                log.warn("语音识别结果为空");
                return ResultTemplate.success(SpeechRecognitionVO.error("语音识别结果为空，请检查音频质量"));
            }

            log.info("语音识别成功（SDK版本），结果: {}", recognizedText);
            return ResultTemplate.success(SpeechRecognitionVO.success(recognizedText));

        } catch (Exception e) {
            log.error("语音识别失败（SDK版本），COS URL: {}", cosUrl, e);
            return ResultTemplate.success(SpeechRecognitionVO.error("语音识别失败: " + e.getMessage()));
        } finally {
            // 5. 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                boolean deleted = tempFile.delete();
                log.info("临时文件清理{}: {}", deleted ? "成功" : "失败", tempFile.getAbsolutePath());
            }
        }
    }

    /**
     * 从URL中提取文件扩展名
     *
     * @param url 文件URL
     * @return 文件扩展名（包含点号，如.mp3）
     */
    private String getFileExtensionFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return ".tmp";
        }

        // 移除查询参数
        int queryIndex = url.indexOf('?');
        if (queryIndex != -1) {
            url = url.substring(0, queryIndex);
        }

        // 提取扩展名
        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex != -1 && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex);
        }
        // 默认为mp3格式
        return ".mp3";
    }
}
