package com.coocaa.ad.cheese.ai.controller;

import cn.hutool.core.lang.UUID;
import com.coocaa.ad.cheese.ai.service.WechatSpeechSdkService;
import com.coocaa.ad.cheese.ai.vo.SpeechRecognitionVO;
import com.coocaa.ad.cheese.authority.common.tools.cos.ObjectUtils;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import com.qcloud.cos.transfer.Download;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/**
 * 语音识别控制器
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Slf4j
@RestController
@RequestMapping("/sys/speech")
@Tag(name = "语音识别", description = "语音转文字服务")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class SpeechRecognitionController {

    private final WechatSpeechSdkService wechatSpeechSdkService;

    /**
     * 语音转文字（使用官方SDK）
     */
    @Operation(
            summary = "语音转文字（SDK版本）",
            description = "使用微信官方SDK上传语音文件，返回识别的文字内容",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "识别成功",
                            content = @Content(schema = @Schema(implementation = SpeechRecognitionVO.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "请求参数错误"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "服务器内部错误"
                    )
            }
    )
    @PostMapping(value = "/recognize-sdk", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResultTemplate<SpeechRecognitionVO> recognizeSpeechWithSdk(
            @Parameter(
                    description = "语音文件（支持mp3格式）",
                    required = true,
                    content = @Content(mediaType = MediaType.MULTIPART_FORM_DATA_VALUE)
            )
            @RequestParam("url") String url) {
        // 创建临时文件
        File templateFile = File.createTempFile(UUID.fastUUID() + "_tpl_", ".pdf");
        Download download = ObjectUtils.downloadFile(renderTemplate, templateFile);
        download.waitForCompletion();

        log.info("收到语音识别请求（SDK版本），文件名: {}, 文件大小: {} bytes",
                file.getOriginalFilename(), file.getSize());

        // 验证文件
        if (file.isEmpty()) {
            log.warn("上传的文件为空");
            return ResultTemplate.success(SpeechRecognitionVO.error("上传的文件为空"));
        }

        // 验证文件类型（可选，根据需要调整）
        String contentType = file.getContentType();
        if (contentType != null && !contentType.startsWith("audio/")) {
            log.warn("不支持的文件类型: {}", contentType);
            return ResultTemplate.success(SpeechRecognitionVO.error("不支持的文件类型，请上传音频文件"));
        }

        try {
            String recognizedText = wechatSpeechSdkService.recognizeSpeech(file);

            if (recognizedText == null || recognizedText.trim().isEmpty()) {
                log.warn("语音识别结果为空");
                return ResultTemplate.success(SpeechRecognitionVO.error("语音识别结果为空，请检查音频质量"));
            }

            log.info("语音识别成功（SDK版本），结果: {}", recognizedText);
            return ResultTemplate.success(SpeechRecognitionVO.success(recognizedText));

        } catch (Exception e) {
            log.error("语音识别失败（SDK版本）", e);
            return ResultTemplate.success(SpeechRecognitionVO.error("语音识别失败: " + e.getMessage()));
        }
    }
}
