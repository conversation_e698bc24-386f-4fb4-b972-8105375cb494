package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.message.MessageInternalEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.message.MessageInternalMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IMessageInternalService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
public class MessageInternalServiceImpl extends ServiceImpl<MessageInternalMapper, MessageInternalEntity> implements IMessageInternalService {
}
