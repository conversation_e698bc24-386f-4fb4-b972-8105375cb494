package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.service.MultimodalEmbeddingService;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 多模态知识库控制器
 * 
 * 支持文本、图像、视频的向量化和搜索
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@RestController
@RequestMapping("/sys/ai/multimodal")
@Tag(name = "多模态知识库", description = "支持文本、图像、视频的向量化处理")
@Slf4j
@RequiredArgsConstructor
public class MultimodalKnowledgeController {

    private final MultimodalEmbeddingService multimodalEmbeddingService;

    @Operation(
            summary = "文本向量化",
            description = "将文本内容转换为向量表示"
    )
    @PostMapping("/embed/text")
    public ResultTemplate<Map<String, Object>> embedText(
            @Parameter(description = "文本内容", required = true)
            @RequestParam String text) {
        
        log.info("文本向量化请求，文本长度: {}", text.length());
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedText(text);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "text");
            result.put("content", text.length() > 100 ? text.substring(0, 100) + "..." : text);
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("文本向量化失败", e);
            return ResultTemplate.fail("文本向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "图像URL向量化",
            description = "将图像URL转换为向量表示"
    )
    @PostMapping("/embed/image-url")
    public ResultTemplate<Map<String, Object>> embedImageUrl(
            @Parameter(description = "图像URL", required = true)
            @RequestParam String imageUrl) {
        
        log.info("图像URL向量化请求: {}", imageUrl);
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedImageUrl(imageUrl);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "image_url");
            result.put("content", imageUrl);
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("图像URL向量化失败", e);
            return ResultTemplate.fail("图像URL向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "图像文件向量化",
            description = "上传图像文件并转换为向量表示"
    )
    @PostMapping("/embed/image-file")
    public ResultTemplate<Map<String, Object>> embedImageFile(
            @Parameter(description = "图像文件", required = true)
            @RequestParam("file") MultipartFile imageFile) {
        
        log.info("图像文件向量化请求，文件名: {}, 大小: {} bytes", 
                imageFile.getOriginalFilename(), imageFile.getSize());
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedImageFile(imageFile);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "image_file");
            result.put("content", imageFile.getOriginalFilename());
            result.put("size", imageFile.getSize());
            result.put("contentType", imageFile.getContentType());
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("图像文件向量化失败", e);
            return ResultTemplate.fail("图像文件向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "视频URL向量化",
            description = "将视频URL转换为向量表示"
    )
    @PostMapping("/embed/video-url")
    public ResultTemplate<Map<String, Object>> embedVideoUrl(
            @Parameter(description = "视频URL", required = true)
            @RequestParam String videoUrl) {
        
        log.info("视频URL向量化请求: {}", videoUrl);
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedVideoUrl(videoUrl);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "video_url");
            result.put("content", videoUrl);
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("视频URL向量化失败", e);
            return ResultTemplate.fail("视频URL向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "视频文件向量化",
            description = "上传视频文件并转换为向量表示"
    )
    @PostMapping("/embed/video-file")
    public ResultTemplate<Map<String, Object>> embedVideoFile(
            @Parameter(description = "视频文件", required = true)
            @RequestParam("file") MultipartFile videoFile) {
        
        log.info("视频文件向量化请求，文件名: {}, 大小: {} bytes", 
                videoFile.getOriginalFilename(), videoFile.getSize());
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedVideoFile(videoFile);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "video_file");
            result.put("content", videoFile.getOriginalFilename());
            result.put("size", videoFile.getSize());
            result.put("contentType", videoFile.getContentType());
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("视频文件向量化失败", e);
            return ResultTemplate.fail("视频文件向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "图文混合向量化",
            description = "将文本和图像组合转换为向量表示"
    )
    @PostMapping("/embed/text-image")
    public ResultTemplate<Map<String, Object>> embedTextAndImage(
            @Parameter(description = "文本内容", required = true)
            @RequestParam String text,
            @Parameter(description = "图像URL", required = true)
            @RequestParam String imageUrl) {
        
        log.info("图文混合向量化请求，文本长度: {}, 图像URL: {}", text.length(), imageUrl);
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedTextAndImage(text, imageUrl);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "text_image");
            result.put("text", text.length() > 100 ? text.substring(0, 100) + "..." : text);
            result.put("imageUrl", imageUrl);
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("图文混合向量化失败", e);
            return ResultTemplate.fail("图文混合向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "多模态混合向量化",
            description = "将文本、图像、视频组合转换为向量表示"
    )
    @PostMapping("/embed/multimodal")
    public ResultTemplate<Map<String, Object>> embedMultimodal(
            @Parameter(description = "文本内容")
            @RequestParam(required = false) String text,
            @Parameter(description = "图像URL")
            @RequestParam(required = false) String imageUrl,
            @Parameter(description = "视频URL")
            @RequestParam(required = false) String videoUrl) {
        
        log.info("多模态混合向量化请求，文本: {}, 图像: {}, 视频: {}", 
                text != null, imageUrl != null, videoUrl != null);
        
        try {
            List<Double> vector = multimodalEmbeddingService.embedMultimodal(text, imageUrl, videoUrl);
            
            Map<String, Object> result = new HashMap<>();
            result.put("vector", vector);
            result.put("dimension", vector.size());
            result.put("type", "multimodal");
            
            Map<String, Object> inputs = new HashMap<>();
            if (text != null) {
                inputs.put("text", text.length() > 100 ? text.substring(0, 100) + "..." : text);
            }
            if (imageUrl != null) {
                inputs.put("imageUrl", imageUrl);
            }
            if (videoUrl != null) {
                inputs.put("videoUrl", videoUrl);
            }
            result.put("inputs", inputs);
            
            return ResultTemplate.success(result);
            
        } catch (Exception e) {
            log.error("多模态混合向量化失败", e);
            return ResultTemplate.fail("多模态混合向量化失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "获取支持的模态类型",
            description = "查询当前模型支持的输入模态类型"
    )
    @GetMapping("/capabilities")
    public ResultTemplate<Map<String, Object>> getCapabilities() {
        log.info("获取多模态能力请求");
        
        try {
            List<String> supportedModalities = multimodalEmbeddingService.getSupportedModalities();
            boolean multimodalSupported = multimodalEmbeddingService.isMultimodalSupported();
            
            Map<String, Object> capabilities = new HashMap<>();
            capabilities.put("supportedModalities", supportedModalities);
            capabilities.put("multimodalSupported", multimodalSupported);
            capabilities.put("supportedImageFormats", List.of("jpeg", "png", "gif", "webp", "bmp", "tiff", "ico"));
            capabilities.put("supportedVideoFormats", List.of("mp4", "avi", "mov"));
            capabilities.put("maxVideoSize", "50MB");
            capabilities.put("maxImagePixels", "36M");
            
            return ResultTemplate.success(capabilities);
            
        } catch (Exception e) {
            log.error("获取多模态能力失败", e);
            return ResultTemplate.fail("获取多模态能力失败: " + e.getMessage());
        }
    }
}
