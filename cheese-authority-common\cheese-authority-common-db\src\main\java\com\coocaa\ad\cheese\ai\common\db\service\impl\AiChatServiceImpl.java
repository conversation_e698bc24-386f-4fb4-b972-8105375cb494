package com.coocaa.ad.cheese.ai.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.ai.common.db.bean.AiChatDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiChatEntity;
import com.coocaa.ad.cheese.ai.common.db.mapper.AiChatMapper;
import com.coocaa.ad.cheese.ai.common.db.service.IAiChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * AI Chat 消息 服务实现类
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiChatServiceImpl extends ServiceImpl<AiChatMapper, AiChatEntity> implements IAiChatService {

    @Override
    public IPage<AiChatEntity> pageList(IPage<AiChatEntity> page, AiChatDTO aiChat) {
        return baseMapper.pageList(page, aiChat);
    }
}