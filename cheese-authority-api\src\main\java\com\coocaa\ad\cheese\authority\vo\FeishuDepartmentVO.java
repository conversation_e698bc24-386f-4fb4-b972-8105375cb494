package com.coocaa.ad.cheese.authority.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年12月06 10:05
 */
@Data
public class FeishuDepartmentVO {

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 父部门ID
     */
    private String parentDepartmentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门主管的用户 ID
     */
    private String leaderUserId;

    /**
     * 部门的排序,取值越小排序越靠前。
     */
    private String order;

    /**
     * 是否被删除:true-是；false-否
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;
}
