package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.service.AiConversationService;
import com.coocaa.ad.cheese.ai.vo.AiChatMessageSendReqVO;
import com.coocaa.ad.cheese.ai.vo.AiChatMessageSendRespVO;
import io.reactivex.Flowable;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * AI聊天流式接口Controller
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@RestController
@RequestMapping("/sys/ai/chat/stream")
@Tag(name = "AI聊天流式接口", description = "AI聊天流式响应相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class AiChatStreamController {

    private final AiConversationService conversationService;

    @Operation(
            summary = "发送聊天消息（流式V2）",
            description = "发送聊天消息并返回流式响应，支持动态模型配置和最大上下文限制"
    )
    @PostMapping(value = "/send", produces = MediaType.APPLICATION_NDJSON_VALUE)
    public Flowable<AiChatMessageSendRespVO> sendChatMessageStreamV2(
            @Parameter(description = "聊天消息发送请求", required = true)
            @RequestBody @Validated AiChatMessageSendReqVO sendReqVO) {

        log.info("收到流式聊天消息请求V2，对话ID: {}, 内容长度: {}, 角色ID: {}", 
                sendReqVO.getConversationId(), 
                sendReqVO.getContent() != null ? sendReqVO.getContent().length() : 0,
                sendReqVO.getRoleId());

        try {
            return conversationService.sendChatMessageStreamV2(sendReqVO)
                    .doOnSubscribe(disposable -> {
                        log.info("开始流式响应，对话ID: {}", sendReqVO.getConversationId());
                    })
                    .doOnComplete(() -> {
                        log.info("流式响应完成，对话ID: {}", sendReqVO.getConversationId());
                    })
                    .doOnError(throwable -> {
                        log.error("流式响应异常，对话ID: {}", sendReqVO.getConversationId(), throwable);
                    });

        } catch (Exception e) {
            log.error("发送聊天消息失败，对话ID: {}", sendReqVO.getConversationId(), e);
            return Flowable.error(e);
        }
    }
}
