package com.coocaa.ad.cheese.ai.common.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI知识库文档分块实体
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@TableName("ai_knowledge_segment")
public class AiKnowledgeSegmentEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 分块内容
     */
    private String content;

    /**
     * 字数
     */
    private Integer wordCount;

    /**
     * 在文档中的位置
     */
    private Integer position;

    /**
     * 向量数据库中的ID
     */
    private String vectorId;

    /**
     * 状态（pending-待处理，vectorized-已向量化，failed-失败）
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 删除标记（0-正常，1-删除）
     */
    private Integer deleteFlag;
}
