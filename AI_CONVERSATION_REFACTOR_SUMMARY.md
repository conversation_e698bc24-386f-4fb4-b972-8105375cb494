# AI对话表重构总结

## 概述

本次重构将AI对话逻辑从直接使用 `ai_user` 表作为对话标识，改为使用专门的 `ai_conversation` 表来管理对话会话，实现了用户信息与对话会话的分离。

## 主要变更

### 1. 新增 `ai_conversation` 表

**表结构：**
```sql
CREATE TABLE `ai_conversation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（关联ai_user表的id）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI对话表';
```

### 2. 新增实体类和服务

#### AiConversationEntity
- 对话实体类，映射 `ai_conversation` 表
- 包含对话ID、用户ID、创建时间等字段

#### AiConversationEntityService
- 对话服务接口，提供对话管理功能
- `createConversationForUser()`: 为用户创建新对话
- `getOrCreateConversationByUserId()`: 获取或创建用户对话
- `validateConversationOwnership()`: 验证对话所有权

#### AiConversationEntityServiceImpl
- 对话服务实现类
- 实现对话的创建、查询、验证等功能

### 3. 用户信息管理接口

#### 新增接口

**获取用户信息：**
```
GET /sys/wechat/user/{userId}
```

**更新用户信息：**
```
PUT /sys/wechat/user
```

#### 相关VO类

**AiUserInfoVO：** 用户信息响应VO
```java
@Data
@Builder
public class AiUserInfoVO {
    private Long id;
    private String openId;
    private String nickName;
    private String avatarUrl;
    private Integer gender;
    private String country;
    private String province;
    private String city;
    private String language;
    private String unionId;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
```

**AiUserUpdateReqVO：** 用户信息更新请求VO
```java
@Data
public class AiUserUpdateReqVO {
    @NotNull
    private Long id;
    private String nickName;
    private String avatarUrl;
    private Integer gender;
    private String country;
    private String province;
    private String city;
    private String language;
}
```

### 4. 核心逻辑变更

#### 对话创建逻辑

**原逻辑：**
- 每次创建对话时都创建新的用户记录
- 使用用户ID作为对话ID

**新逻辑：**
- 根据OpenID查找现有用户，不存在则创建
- 为用户创建新的对话记录
- 使用对话ID作为真正的对话标识

#### 用户查询逻辑

**原逻辑：**
```java
// conversationId就是ai_user表的id，直接查询
return aiUserService.getById(conversationId);
```

**新逻辑：**
```java
// 通过对话表查找用户ID，再查询用户信息
AiConversationEntity conversation = aiConversationEntityService.getById(conversationId);
if (conversation == null) {
    return null;
}
return aiUserService.getById(conversation.getUserId());
```

## 数据库设计优势

### 1. 数据结构清晰
- **ai_user表**：专门存储用户基本信息
- **ai_conversation表**：专门管理对话会话
- **ai_chat表**：存储具体的聊天消息

### 2. 支持多对话
- 一个用户可以有多个对话会话
- 每个对话有独立的ID和生命周期

### 3. 数据一致性
- 用户信息不会重复创建
- 对话与用户的关联关系明确

### 4. 扩展性强
- 可以为对话添加更多属性（标题、状态等）
- 支持对话的分组、归档等功能

## API接口变更

### 1. 对话创建接口
- 接口路径不变：`POST /sys/ai-conversations/create`
- 响应中的 `conversationId` 现在是真正的对话ID

### 2. 消息发送接口
- 接口路径不变：`POST /sys/ai-conversations/send-stream`
- `conversationId` 参数现在指向对话表的ID

### 3. 新增用户管理接口
- `GET /sys/wechat/user/{userId}`: 获取用户信息
- `PUT /sys/wechat/user`: 更新用户信息

## 使用示例

### 1. 创建对话
```bash
curl -X POST http://localhost:8001/sys/ai-conversations/create \
  -H "Content-Type: application/json" \
  -d '{
    "openId": "wx_user_123",
    "name": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "title": "新的对话"
  }'
```

**响应：**
```json
{
  "code": 200,
  "data": {
    "conversationId": 1001,  // 对话ID
    "title": "新的对话"
  }
}
```

### 2. 发送消息
```bash
curl -X POST http://localhost:8001/sys/ai-conversations/send-stream \
  -H "Content-Type: application/json" \
  -d '{
    "conversationId": 1001,  // 使用对话ID
    "content": "你好",
    "useContext": true
  }'
```

### 3. 获取用户信息
```bash
curl -X GET http://localhost:8001/sys/wechat/user/1
```

### 4. 更新用户信息
```bash
curl -X PUT http://localhost:8001/sys/wechat/user \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "nickName": "新昵称",
    "avatarUrl": "https://example.com/new_avatar.jpg"
  }'
```

## 数据迁移建议

### 1. 现有数据处理
如果系统中已有数据，需要进行数据迁移：

```sql
-- 为现有用户创建对话记录
INSERT INTO ai_conversation (user_id, create_time, update_time)
SELECT id, create_time, update_time 
FROM ai_user 
WHERE delete_flag = 0;

-- 更新ai_chat表中的conversation_id
-- 注意：这需要根据具体的数据关联关系来调整
```

### 2. 兼容性处理
在过渡期间，可以保持向后兼容：
- 检查传入的conversationId是否存在于对话表
- 如果不存在，可以尝试作为用户ID处理（兼容旧逻辑）

## 总结

本次重构实现了：

✅ **数据结构优化**：用户信息与对话会话分离  
✅ **功能完善**：支持用户信息管理  
✅ **扩展性提升**：支持一用户多对话  
✅ **代码清晰**：逻辑更加明确  
✅ **向后兼容**：保持现有接口不变  

重构后的系统更加符合实际业务需求，为后续功能扩展奠定了良好基础。
