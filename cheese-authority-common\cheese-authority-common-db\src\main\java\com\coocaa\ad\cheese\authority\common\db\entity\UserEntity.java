package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.ad.cheese.authority.common.tools.utils.ChangeFormatter;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.ChangeExtract;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user")
public class UserEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 姓名
     */
    @ChangeExtract(readableName = "姓名")
    private String name;

    /**
     * 工号
     */
    @ChangeExtract(readableName = "工号")
    private String wno;

    /**
     * 手机号
     */
    @ChangeExtract(readableName = "手机")
    private String mobile;

    /**
     * 邮箱
     */
    @ChangeExtract(readableName = "邮箱")
    private String email;

    /**
     * 登陆名
     */
    @ChangeExtract(readableName = "登陆名")
    private String userName;

    /**
     * 密码;使用MD5加密
     */
    @ChangeExtract(readableName = "密码")
    private String password;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    @ChangeExtract(readableName = "状态", formatter = ChangeFormatter.StatusFormatter.class)
    private Boolean status;

    /**
     * 最后修改密码时间
     */
    private Date changePasswordTime;

    /**
     * 用户被锁 [0:未锁, 1:已锁]
     */
    private Boolean locked;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 用户类型：1-内部用户；2-外部代理商
     */
    private Integer type;


}
