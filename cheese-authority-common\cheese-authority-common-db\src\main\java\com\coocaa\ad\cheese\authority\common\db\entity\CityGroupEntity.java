package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.CompareField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 城市组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("city_group")
public class CityGroupEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父ID
     */
    private Integer parentId;

    /**
     * 组名称
     */
    @CompareField(description = "城市组名称")
    private String name;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    private Boolean status;

    /**
     * 排序(自然顺序，越小越靠前)
     */
    private Integer rank;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 城市组描述
     * */
    @CompareField(description = "城市组描述")
    private String remark;
}
