package com.coocaa.ad.cheese.authority.common.db.mapper.approve;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleCarbonEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRulePersonEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Mapper
public interface ApproveInternalRuleCarbonMapper extends BaseMapper<ApproveInternalRuleCarbonEntity> {
}
