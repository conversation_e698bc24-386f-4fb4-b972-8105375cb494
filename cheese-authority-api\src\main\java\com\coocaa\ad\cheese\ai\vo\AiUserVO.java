
package com.coocaa.ad.cheese.ai.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AI Chat 对话VO
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiUserVO", description = "AI Chat 对话VO")
public class AiUserVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户OpenID", maxLength = 32)
    private String openId;

    @Schema(description = "手机号", maxLength = 20)
    private String mobile;

    @Schema(description = "昵称", maxLength = 100)
    private String nickName;

    @Schema(description = "性别", maxLength = 10)
    private String gender;

    @Schema(description = "语言", maxLength = 20)
    private String language;

    @Schema(description = "城市", maxLength = 50)
    private String city;

    @Schema(description = "省份", maxLength = 50)
    private String province;

    @Schema(description = "国家", maxLength = 50)
    private String country;

    @Schema(description = "头像URL", maxLength = 500)
    private String avatarUrl;

    @Schema(description = "微信UnionID", maxLength = 50)
    private String unionId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    @Schema(description = "操作人")
    private Integer operator;

    @Schema(description = "删除标记 0正常 1删除")
    private Integer deleteFlag;
}