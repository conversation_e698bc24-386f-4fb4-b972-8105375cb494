
package com.coocaa.ad.cheese.ai.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AI Chat 对话VO
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiUserVO", description = "AI Chat 对话VO")
public class AiUserVO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "用户ID", maxLength = 32)
    private String openId;

    @Schema(description = "名称", maxLength = 32)
    private String name;

    @Schema(description = "昵称", maxLength = 100)
    private String nickName;

    @Schema(description = "头像", maxLength = 200)
    private String avatar;

    @Schema(description = "手机", maxLength = 200)
    private String mobile;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    @Schema(description = "操作人")
    private Integer operator;

    @Schema(description = "删除标记 0正常 1删除")
    private Integer deleteFlag;
}