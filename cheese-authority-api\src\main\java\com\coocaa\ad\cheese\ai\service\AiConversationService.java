package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiChatEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiConversationEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiRoleEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiUserEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiChatService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiConversationService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiUserService;
import com.coocaa.ad.cheese.ai.enums.MessageType;
import com.coocaa.ad.cheese.ai.service.AiRoleService;
import com.coocaa.ad.cheese.ai.service.AiUserService;
import com.coocaa.ad.cheese.ai.vo.*;
import com.coocaa.ad.cheese.ai.vo.AiChatMessageSendReqVO;
import com.coocaa.ad.cheese.ai.vo.AiConversationCreateReqVO;
import com.coocaa.ad.cheese.ai.vo.AiConversationCreateRespVO;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;

/**
 * AI 对话管理服务
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiConversationService {

    private final IAiChatService aiChatService;
    private final IAiUserService iAiUserService;
    private final AiUserService aiUserService;
    private final IAiConversationService aiConversationService;
    private final AiRoleService aiRoleService;
    private final DoubaoAiService doubaoAiService;

    /**
     * 创建对话
     *
     * @param reqVO 创建请求
     * @return 创建响应
     */
    @Transactional(rollbackFor = Exception.class)
    public AiConversationCreateRespVO createConversation(AiConversationCreateReqVO reqVO) {
        // 1. 构建用户信息请求
        AiUserInfoReqVO userInfoReq = new AiUserInfoReqVO();
        userInfoReq.setOpenId(reqVO.getOpenId());
        userInfoReq.setNickName(reqVO.getName());
        userInfoReq.setAvatarUrl(reqVO.getAvatar());

        // 2. 通过AiUserService查找或创建用户
        AiUserVO userInfoVO = aiUserService.findOrCreateUser(userInfoReq);

        // 3. 创建对话记录
        AiConversationEntity conversationEntity = createConversationForUser(userInfoVO.getId(), reqVO.getRoleId());
        String title = StringUtils.hasText(reqVO.getTitle()) ? reqVO.getTitle() : "新的对话";

        // 4. 创建对话初始记录
        createConversationRecord(conversationEntity.getId(), userInfoVO.getOpenId(), title);

        // 5. 构建响应
        AiConversationCreateRespVO respVO = new AiConversationCreateRespVO();
        respVO.setConversationId(conversationEntity.getId());
        respVO.setTitle(title);
        respVO.setRoleId(reqVO.getRoleId());

        log.info("创建对话成功，对话ID: {}, 用户ID: {}, 用户: {}",
                conversationEntity.getId(), userInfoVO.getId(), reqVO.getOpenId());
        return respVO;
    }



    /**
     * 为用户创建新的对话
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 对话实体
     */
    private AiConversationEntity createConversationForUser(Long userId, Long roleId) {
        log.info("为用户创建新对话，用户ID: {}, 角色ID: {}", userId, roleId);

        AiConversationEntity conversation = new AiConversationEntity();
        conversation.setUserId(userId);
        conversation.setRoleId(roleId);
        conversation.setCreateTime(LocalDateTime.now());
        conversation.setUpdateTime(LocalDateTime.now());
        conversation.setCreateBy("system");
        conversation.setUpdateBy("system");
        conversation.setDeleteFlag(0);

        aiConversationService.save(conversation);

        log.info("对话创建成功，对话ID: {}, 用户ID: {}, 角色ID: {}", conversation.getId(), userId, roleId);
        return conversation;
    }

    /**
     * 创建对话记录，建立conversationId和用户的关联
     *
     * @param conversationId 对话ID
     * @param openId         用户openId
     * @param title          对话标题
     */
    private void createConversationRecord(Long conversationId, String openId, String title) {
        // 插入一条系统消息作为对话开始的标记
        AiChatEntity conversationStart = new AiChatEntity();
        conversationStart.setConversationId(conversationId);
        conversationStart.setOpenId(openId);
        conversationStart.setMessageType(MessageType.SYSTEM.getCode());
        conversationStart.setContent("对话开始: " + title);
        conversationStart.setUserContext(0);
        conversationStart.setDeleteFlag(0);
        conversationStart.setCreateTime(LocalDateTime.now());
        conversationStart.setUpdateTime(LocalDateTime.now());

        aiChatService.save(conversationStart);
        log.info("创建对话记录: conversationId={}, openId={}", conversationId, openId);
    }

    /**
     * 发送聊天消息（流式）
     *
     * @param sendReqVO 发送请求
     * @return 流式响应
     */
    @Transactional(rollbackFor = Exception.class)
    public Flowable<String> sendChatMessageStream(AiChatMessageSendReqVO sendReqVO) {
        // 1. 校验对话ID是否有效并获取用户信息
        if (sendReqVO.getConversationId() == null || sendReqVO.getConversationId() <= 0) {
            return Flowable.error(new RuntimeException("对话ID无效"));
        }

        // 2. 根据conversationId查询用户信息
        AiUserEntity userInfo = getUserByConversationId(sendReqVO.getConversationId());
        if (userInfo == null) {
            return Flowable.error(new RuntimeException("未找到对话对应的用户信息"));
        }

        // 3. 插入用户发送消息（统一处理）
        AiChatEntity userMessage = new AiChatEntity();
        userMessage.setConversationId(sendReqVO.getConversationId());
        userMessage.setOpenId(userInfo.getOpenId());
        userMessage.setMessageType(MessageType.USER.getCode());
        userMessage.setContent(sendReqVO.getContent());
        userMessage.setUserContext(Boolean.TRUE.equals(sendReqVO.getUseContext()) ? 1 : 0);
        userMessage.setDeleteFlag(0);
        userMessage.setCreateTime(LocalDateTime.now());
        userMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.save(userMessage);

        // 4. 创建AI助手回复消息（初始为空）
        AiChatEntity assistantMessage = new AiChatEntity();
        assistantMessage.setConversationId(sendReqVO.getConversationId());
        assistantMessage.setReplyId(userMessage.getId());
        assistantMessage.setOpenId(userInfo.getOpenId());
        assistantMessage.setMessageType(MessageType.ASSISTANT.getCode());
        // 初始为空，后续更新
        assistantMessage.setContent("");
        assistantMessage.setUserContext(0);
        assistantMessage.setDeleteFlag(0);
        assistantMessage.setCreateTime(LocalDateTime.now());
        assistantMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.save(assistantMessage);

        // 5. 检查是否包含视频或素材关键词
        String userContent = sendReqVO.getContent();
        if (containsVideoKeywords(userContent)) {
            return handleVideoRequest(assistantMessage);
        }

        // 6. 获取角色提示词
        String rolePrompt = getRolePrompt(sendReqVO.getRoleId());

        // 7. 获取上下文消息（如果需要）
        List<ChatMessage> contextMessages = new ArrayList<>();
        if (Boolean.TRUE.equals(sendReqVO.getUseContext())) {
            contextMessages = getContextMessages(sendReqVO.getConversationId());
        }

        // 8. 构建消息列表并调用豆包AI（包含角色提示词）
        List<ChatMessage> messages = doubaoAiService.buildMessageList(sendReqVO.getContent(), contextMessages, rolePrompt);

        // 7. 流式处理AI响应
        AtomicReference<StringBuilder> contentBuilder = new AtomicReference<>(new StringBuilder());

        return doubaoAiService.getBotChatCompletionChunk(messages)
                .doOnNext(response -> log.info("AiConversationService收到chunk: {}", response))
                .<String>map(response -> {
                    try {
                        // 处理流式响应
                        if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                            var choice = response.getChoices().get(0);
                            log.info("Choice对象: {}", choice);

                            String content = null;

                            // 尝试从message获取内容
                            try {
                                if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                                    Object contentObj = choice.getMessage().getContent();
                                    content = contentObj.toString();
                                    log.info("从message获取到内容: {}", content);
                                }
                            } catch (Exception msgEx) {
                                log.debug("从message获取内容失败: {}", msgEx.getMessage());
                            }

                            // 返回内容
                            if (content != null && !content.isEmpty()) {
                                contentBuilder.get().append(content);
                                return contentBuilder.toString();
                            } else {
                                log.info("未获取到有效内容，返回空字符串");
                                return "";
                            }
                        } else {
                            log.info("响应中没有choices或choices为空");
                            return "";
                        }
                    } catch (Exception e) {
                        log.error("处理流式响应时出错: {}", e.getMessage(), e);
                        return "";
                    }
                })
                .filter(content -> !content.isEmpty())
                .doOnComplete(() -> {
                    log.info("流式响应完成，最终内容: {}", contentBuilder.get().toString());
                    // 更新AI助手消息内容
                    assistantMessage.setContent(contentBuilder.get().toString());
                    assistantMessage.setUpdateTime(LocalDateTime.now());
                    aiChatService.updateById(assistantMessage);

                    log.info("AI消息发送完成，对话ID: {}, 用户消息ID: {}, AI消息ID: {}",
                            sendReqVO.getConversationId(), userMessage.getId(), assistantMessage.getId());
                }).doOnError(error -> log.error("豆包AI流式响应出错", error));

    }

    /**
     * 获取上下文消息
     *
     * @param conversationId 对话ID
     * @return 上下文消息列表
     */
    private List<ChatMessage> getContextMessages(Long conversationId) {
        // 获取最近的10条消息作为上下文
        LambdaQueryWrapper<AiChatEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiChatEntity::getConversationId, conversationId)
                .eq(AiChatEntity::getDeleteFlag, 0)
                .orderByDesc(AiChatEntity::getCreateTime)
                .last("LIMIT 10");

        List<AiChatEntity> chatMessages = aiChatService.list(wrapper);
        List<ChatMessage> contextMessages = new ArrayList<>();

        // 按时间正序排列，构建上下文
        chatMessages.stream()
                .sorted((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()))
                .forEach(msg -> {
                    if (StringUtils.hasText(msg.getContent())) {
                        contextMessages.add(doubaoAiService.buildChatMessage(msg.getMessageType(), msg.getContent()));
                    }
                });

        return contextMessages;
    }

    /**
     * 根据对话ID获取用户信息
     *
     * @param conversationId 对话ID
     * @return 用户信息
     */
    private AiUserEntity getUserByConversationId(Long conversationId) {
        // 通过对话表查找用户ID，再查询用户信息
        AiConversationEntity conversation = aiConversationService.getById(conversationId);
        if (conversation == null) {
            return null;
        }

        return iAiUserService.getById(conversation.getUserId());
    }

    /**
     * 检查消息是否包含视频或素材关键词
     *
     * @param message 用户消息
     * @return 是否包含关键词
     */
    private boolean containsVideoKeywords(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("视频") || lowerMessage.contains("素材");
    }

    /**
     * 处理视频请求，返回随机视频链接
     *
     * @param assistantMessage 已创建的AI助手消息
     * @return 流式响应
     */
    private Flowable<String> handleVideoRequest(AiChatEntity assistantMessage) {
        log.info("检测到视频/素材关键词，返回随机视频链接");

        // 1. 生成随机视频链接
        String videoUrl = generateRandomVideoUrl();

        // 2. 更新AI助手回复消息内容
        assistantMessage.setContent(videoUrl);
        assistantMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.updateById(assistantMessage);

        log.info("视频请求处理完成，返回视频链接: {}", videoUrl);

        // 3. 返回流式响应
        return Flowable.just(videoUrl);
    }

    /**
     * 生成随机视频URL
     *
     * @return 随机视频链接
     */
    private String generateRandomVideoUrl() {
        Random random = new Random();
        // 生成1-20的随机数
        int videoNumber = random.nextInt(20) + 1;
        // 格式化为两位数
        String formattedNumber = String.format("%02d", videoNumber);
        return "https://test-1330579985.cos.ap-guangzhou.myqcloud.com/video/" + formattedNumber + ".mp4";
    }

    /**
     * 获取角色提示词
     *
     * @param roleId 角色ID
     * @return 角色提示词，如果角色不存在则返回null
     */
    private String getRolePrompt(Long roleId) {
        return aiRoleService.getRolePrompt(roleId);
    }
}
