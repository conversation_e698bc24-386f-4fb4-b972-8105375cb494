package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiChatEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiConversationEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiRoleEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiUserEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiChatService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiConversationService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiUserService;
import com.coocaa.ad.cheese.ai.enums.MessageType;
import com.coocaa.ad.cheese.ai.convert.AiUserConvert;
import com.coocaa.ad.cheese.ai.service.AiModelService;
import com.coocaa.ad.cheese.ai.service.AiRoleService;
import com.coocaa.ad.cheese.ai.service.AiUserService;
import com.coocaa.ad.cheese.ai.vo.AiModelVO;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionChunk;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChunk;
import io.reactivex.Flowable;
import com.coocaa.ad.cheese.ai.vo.*;
import com.coocaa.ad.cheese.ai.vo.AiChatMessageSendReqVO;
import com.coocaa.ad.cheese.ai.vo.AiChatMessageSendRespVO;
import com.coocaa.ad.cheese.ai.vo.AiConversationCreateReqVO;
import com.coocaa.ad.cheese.ai.vo.AiConversationCreateRespVO;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import io.reactivex.Flowable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;

/**
 * AI 对话管理服务
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiConversationService {

    private final IAiChatService aiChatService;
    private final IAiUserService iAiUserService;
    private final AiUserService aiUserService;
    private final IAiConversationService aiConversationService;
    private final AiRoleService aiRoleService;
    private final AiModelService aiModelService;
    private final DoubaoAiService doubaoAiService;

    /**
     * 创建对话
     *
     * @param reqVO 创建请求
     * @return 创建响应
     */
    @Transactional(rollbackFor = Exception.class)
    public AiConversationCreateRespVO createConversation(AiConversationCreateReqVO reqVO) {
        // 1. 构建用户信息请求
        AiUserInfoReqVO userInfoReq = new AiUserInfoReqVO();
        userInfoReq.setOpenId(reqVO.getOpenId());
        userInfoReq.setNickName(reqVO.getName());
        userInfoReq.setAvatarUrl(reqVO.getAvatar());

        // 2. 通过AiUserService查找或创建用户
        AiUserVO userInfoVO = aiUserService.findOrCreateUser(userInfoReq);

        // 3. 验证角色是否存在（如果指定了角色）
        if (reqVO.getRoleId() != null) {
            String rolePrompt = aiRoleService.getRoleById(reqVO.getRoleId()).getRolePrompt();
            if (rolePrompt == null) {
                throw new RuntimeException("指定的角色不存在或已禁用");
            }
        }

        // 4. 创建对话记录
        AiConversationEntity conversationEntity = createConversationForUser(userInfoVO.getId(), reqVO.getRoleId());
        String title = StringUtils.hasText(reqVO.getTitle()) ? reqVO.getTitle() : "新的对话";

        // 4. 创建对话初始记录
        createConversationRecord(conversationEntity.getId(), userInfoVO.getId(), title);

        // 5. 构建响应
        AiConversationCreateRespVO respVO = new AiConversationCreateRespVO();
        respVO.setConversationId(conversationEntity.getId());
        respVO.setTitle(title);
        respVO.setRoleId(reqVO.getRoleId());

        log.info("创建对话成功，对话ID: {}, 用户ID: {}, 用户: {}",
                conversationEntity.getId(), userInfoVO.getId(), reqVO.getOpenId());
        return respVO;
    }



    /**
     * 为用户创建新的对话
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 对话实体
     */
    private AiConversationEntity createConversationForUser(Long userId, Long roleId) {
        log.info("为用户创建新对话，用户ID: {}, 角色ID: {}", userId, roleId);

        AiConversationEntity conversation = new AiConversationEntity();
        conversation.setUserId(userId);
        conversation.setRoleId(roleId);
        conversation.setCreateTime(LocalDateTime.now());
        conversation.setUpdateTime(LocalDateTime.now());
        conversation.setCreateBy("system");
        conversation.setUpdateBy("system");
        conversation.setDeleteFlag(0);

        aiConversationService.save(conversation);

        log.info("对话创建成功，对话ID: {}, 用户ID: {}, 角色ID: {}", conversation.getId(), userId, roleId);
        return conversation;
    }

    /**
     * 创建对话记录，建立conversationId和用户的关联
     *
     * @param conversationId 对话ID
     * @param userId         用户ID
     * @param title          对话标题
     */
    private void createConversationRecord(Long conversationId, Long userId, String title) {
        // 插入一条系统消息作为对话开始的标记
        AiChatEntity conversationStart = new AiChatEntity();
        conversationStart.setConversationId(conversationId);
        conversationStart.setUserId(userId);
        conversationStart.setMessageType(MessageType.SYSTEM.getCode());
        conversationStart.setContent("对话开始: " + title);
        conversationStart.setUserContext(0);
        conversationStart.setDeleteFlag(0);
        conversationStart.setCreateTime(LocalDateTime.now());
        conversationStart.setUpdateTime(LocalDateTime.now());

        aiChatService.save(conversationStart);
        log.info("创建对话记录: conversationId={}, userId={}", conversationId, userId);
    }

    /**
     * 发送聊天消息（流式）
     *
     * @param sendReqVO 发送请求
     * @return 流式响应
     */
    @Transactional(rollbackFor = Exception.class)
    public Flowable<String> sendChatMessageStream(AiChatMessageSendReqVO sendReqVO) {
        // 1. 校验对话ID是否有效并获取用户信息
        if (sendReqVO.getConversationId() == null || sendReqVO.getConversationId() <= 0) {
            return Flowable.error(new RuntimeException("对话ID无效"));
        }

        // 2. 根据conversationId查询用户信息
        AiUserEntity userInfo = getUserByConversationId(sendReqVO.getConversationId());
        if (userInfo == null) {
            return Flowable.error(new RuntimeException("未找到对话对应的用户信息"));
        }

        // 3. 插入用户发送消息（统一处理）
        AiChatEntity userMessage = new AiChatEntity();
        userMessage.setConversationId(sendReqVO.getConversationId());
        userMessage.setUserId(userInfo.getId());
        userMessage.setMessageType(MessageType.USER.getCode());
        userMessage.setContent(sendReqVO.getContent());
        userMessage.setUserContext(Boolean.TRUE.equals(sendReqVO.getUseContext()) ? 1 : 0);
        userMessage.setDeleteFlag(0);
        userMessage.setCreateTime(LocalDateTime.now());
        userMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.save(userMessage);

        // 4. 创建AI助手回复消息（初始为空）
        AiChatEntity assistantMessage = new AiChatEntity();
        assistantMessage.setConversationId(sendReqVO.getConversationId());
        assistantMessage.setReplyId(userMessage.getId());
        assistantMessage.setUserId(userInfo.getId());
        assistantMessage.setMessageType(MessageType.ASSISTANT.getCode());
        // 初始为空，后续更新
        assistantMessage.setContent("");
        assistantMessage.setUserContext(0);
        assistantMessage.setDeleteFlag(0);
        assistantMessage.setCreateTime(LocalDateTime.now());
        assistantMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.save(assistantMessage);

        // 5. 检查是否包含视频或素材关键词
        String userContent = sendReqVO.getContent();
        if (containsVideoKeywords(userContent)) {
            return handleVideoRequest(assistantMessage);
        }

        // 6. 获取角色提示词
        String rolePrompt = getRolePrompt(sendReqVO.getRoleId());

        // 7. 获取上下文消息（如果需要）
        List<ChatMessage> contextMessages = new ArrayList<>();
        if (Boolean.TRUE.equals(sendReqVO.getUseContext())) {
            contextMessages = getContextMessages(sendReqVO.getConversationId());
        }

        // 8. 构建消息列表并调用豆包AI（包含角色提示词）
        List<ChatMessage> messages = doubaoAiService.buildMessageList(sendReqVO.getContent(), contextMessages, rolePrompt);

        // 7. 流式处理AI响应
        AtomicReference<StringBuilder> contentBuilder = new AtomicReference<>(new StringBuilder());

        return doubaoAiService.getBotChatCompletionChunk(messages)
                .doOnNext(response -> log.info("AiConversationService收到chunk: {}", response))
                .<String>map(response -> {
                    try {
                        // 处理流式响应
                        if (response.getChoices() != null && !response.getChoices().isEmpty()) {
                            var choice = response.getChoices().get(0);
                            log.info("Choice对象: {}", choice);

                            String content = null;

                            // 尝试从message获取内容
                            try {
                                if (choice.getMessage() != null && choice.getMessage().getContent() != null) {
                                    Object contentObj = choice.getMessage().getContent();
                                    content = contentObj.toString();
                                    log.info("从message获取到内容: {}", content);
                                }
                            } catch (Exception msgEx) {
                                log.debug("从message获取内容失败: {}", msgEx.getMessage());
                            }

                            // 返回内容
                            if (content != null && !content.isEmpty()) {
                                contentBuilder.get().append(content);
                                return contentBuilder.toString();
                            } else {
                                log.info("未获取到有效内容，返回空字符串");
                                return "";
                            }
                        } else {
                            log.info("响应中没有choices或choices为空");
                            return "";
                        }
                    } catch (Exception e) {
                        log.error("处理流式响应时出错: {}", e.getMessage(), e);
                        return "";
                    }
                })
                .filter(content -> !content.isEmpty())
                .doOnComplete(() -> {
                    log.info("流式响应完成，最终内容: {}", contentBuilder.get().toString());
                    // 更新AI助手消息内容
                    assistantMessage.setContent(contentBuilder.get().toString());
                    assistantMessage.setUpdateTime(LocalDateTime.now());
                    aiChatService.updateById(assistantMessage);

                    log.info("AI消息发送完成，对话ID: {}, 用户消息ID: {}, AI消息ID: {}",
                            sendReqVO.getConversationId(), userMessage.getId(), assistantMessage.getId());
                }).doOnError(error -> log.error("豆包AI流式响应出错", error));

    }

    /**
     * 获取上下文消息
     *
     * @param conversationId 对话ID
     * @return 上下文消息列表
     */
    private List<ChatMessage> getContextMessages(Long conversationId) {
        // 获取最近的10条消息作为上下文
        LambdaQueryWrapper<AiChatEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiChatEntity::getConversationId, conversationId)
                .eq(AiChatEntity::getDeleteFlag, 0)
                .orderByDesc(AiChatEntity::getCreateTime)
                .last("LIMIT 10");

        List<AiChatEntity> chatMessages = aiChatService.list(wrapper);
        List<ChatMessage> contextMessages = new ArrayList<>();

        // 按时间正序排列，构建上下文
        chatMessages.stream()
                .sorted((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()))
                .forEach(msg -> {
                    if (StringUtils.hasText(msg.getContent())) {
                        contextMessages.add(doubaoAiService.buildChatMessage(msg.getMessageType(), msg.getContent()));
                    }
                });

        return contextMessages;
    }

    /**
     * 根据对话ID获取用户信息
     *
     * @param conversationId 对话ID
     * @return 用户信息
     */
    private AiUserEntity getUserByConversationId(Long conversationId) {
        // 通过对话表查找用户ID，再查询用户信息
        AiConversationEntity conversation = aiConversationService.getById(conversationId);
        if (conversation == null) {
            return null;
        }

        return iAiUserService.getById(conversation.getUserId());
    }

    /**
     * 检查消息是否包含视频或素材关键词
     *
     * @param message 用户消息
     * @return 是否包含关键词
     */
    private boolean containsVideoKeywords(String message) {
        if (message == null || message.trim().isEmpty()) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("视频") || lowerMessage.contains("素材");
    }

    /**
     * 处理视频请求，返回随机视频链接
     *
     * @param assistantMessage 已创建的AI助手消息
     * @return 流式响应
     */
    private Flowable<String> handleVideoRequest(AiChatEntity assistantMessage) {
        log.info("检测到视频/素材关键词，返回随机视频链接");

        // 1. 生成随机视频链接
        String videoUrl = generateRandomVideoUrl();

        // 2. 更新AI助手回复消息内容
        assistantMessage.setContent(videoUrl);
        assistantMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.updateById(assistantMessage);

        log.info("视频请求处理完成，返回视频链接: {}", videoUrl);

        // 3. 返回流式响应
        return Flowable.just(videoUrl);
    }

    /**
     * 生成随机视频URL
     *
     * @return 随机视频链接
     */
    private String generateRandomVideoUrl() {
        Random random = new Random();
        // 生成1-20的随机数
        int videoNumber = random.nextInt(20) + 1;
        // 格式化为两位数
        String formattedNumber = String.format("%02d", videoNumber);
        return "https://test-1330579985.cos.ap-guangzhou.myqcloud.com/video/" + formattedNumber + ".mp4";
    }

    /**
     * 获取角色提示词
     *
     * @param roleId 角色ID
     * @return 角色提示词，如果角色不存在则返回null
     */
    private String getRolePrompt(Long roleId) {
        return aiRoleService.getRoleById(roleId).getRolePrompt();
    }

    /**
     * 发送聊天消息（流式，支持动态模型和最大上下文限制）
     *
     * @param sendReqVO 发送请求
     * @return 流式响应
     */
    public Flowable<AiChatMessageSendRespVO> sendChatMessageStreamV2(AiChatMessageSendReqVO sendReqVO) {
        log.info("收到流式聊天消息请求V2，对话ID: {}, 内容: {}, 角色ID: {}",
                sendReqVO.getConversationId(), sendReqVO.getContent(), sendReqVO.getRoleId());

        try {
            // 1. 验证对话是否存在
            AiConversationEntity conversation = aiConversationService.getById(sendReqVO.getConversationId());
            if (conversation == null) {
                return Flowable.error(new RuntimeException("对话不存在"));
            }

            // 2. 获取用户信息
            AiUserEntity userEntity = getUserByConversationId(sendReqVO.getConversationId());
            if (userEntity == null) {
                return Flowable.error(new RuntimeException("用户信息不存在"));
            }
            AiUserVO userInfo = AiUserConvert.INSTANCE.toVo(userEntity);
            if (userInfo == null) {
                return Flowable.error(new RuntimeException("用户信息不存在"));
            }

            // 3. 获取角色信息和模型配置
            Long roleId = sendReqVO.getRoleId() != null ? sendReqVO.getRoleId() : conversation.getRoleId();
            String rolePrompt = getRolePrompt(roleId);
            AiModelVO model = getModelByRoleId(roleId);

            if (model == null) {
                return Flowable.error(new RuntimeException("未找到可用的模型配置"));
            }

            // 4. 插入用户发送消息
            AiChatEntity userMessage = createUserMessage(sendReqVO, userInfo);

            // 5. 创建AI助手回复消息（初始为空）
            AiChatEntity assistantMessage = createAssistantMessage(sendReqVO, userInfo, userMessage.getId());

            // 6. 获取上下文消息（带最大限制）
            List<ChatMessage> contextMessages = getContextMessagesWithLimit(
                    sendReqVO.getConversationId(),
                    sendReqVO.getUseContext(),
                    model.getMaxTokens()
            );

            // 7. 构建消息列表
            List<ChatMessage> messages = doubaoAiService.buildMessageList(
                    sendReqVO.getContent(),
                    contextMessages,
                    rolePrompt
            );

            // 8. 调用AI服务（使用动态模型配置）
            Flowable<ChatCompletionChunk> aiResponse = doubaoAiService.getChatCompletionChunk(
                    messages,
                    model.getModelKey()
            );

            // 9. 处理流式响应
            StringBuilder contentBuffer = new StringBuilder();
            return aiResponse
                    .map(chunk -> {
                        String deltaContent = extractDeltaContent(chunk);
                        if (deltaContent != null) {
                            contentBuffer.append(deltaContent);
                        }

                        // 构建响应
                        AiChatMessageSendRespVO respVO = new AiChatMessageSendRespVO();
                        respVO.setUserMessageId(userMessage.getId());
                        respVO.setAssistantMessageId(assistantMessage.getId());
                        respVO.setContent(deltaContent != null ? deltaContent : "");
                        respVO.setFullContent(contentBuffer.toString());
                        respVO.setConversationId(sendReqVO.getConversationId());
                        respVO.setModelName(model.getModelName());

                        return respVO;
                    })
                    .doOnComplete(() -> {
                        // 更新助手消息内容
                        updateAssistantMessageContent(assistantMessage.getId(), contentBuffer.toString());
                        log.info("流式响应完成，对话ID: {}, 最终内容长度: {}",
                                sendReqVO.getConversationId(), contentBuffer.length());
                    })
                    .doOnError(throwable -> {
                        log.error("流式响应异常，对话ID: {}", sendReqVO.getConversationId(), throwable);
                        updateAssistantMessageContent(assistantMessage.getId(), "AI响应异常: " + throwable.getMessage());
                    });

        } catch (Exception e) {
            log.error("发送聊天消息失败，对话ID: {}", sendReqVO.getConversationId(), e);
            return Flowable.error(e);
        }
    }

    /**
     * 获取上下文消息（带最大Token限制）
     *
     * @param conversationId 对话ID
     * @param useContext 是否使用上下文
     * @param maxTokens 最大Token数
     * @return 上下文消息列表
     */
    private List<ChatMessage> getContextMessagesWithLimit(Long conversationId, Boolean useContext, Integer maxTokens) {
        if (!Boolean.TRUE.equals(useContext) || maxTokens == null) {
            return new ArrayList<>();
        }

        // 获取历史消息
        List<ChatMessage> contextMessages = getContextMessages(conversationId);

        // 如果没有设置最大Token限制，直接返回
        if (maxTokens <= 0) {
            return contextMessages;
        }

        // 简单的Token估算：1个中文字符约等于2个Token，1个英文单词约等于1个Token
        // 为了安全起见，预留30%的Token给系统提示词和当前消息
        int availableTokens = (int) (maxTokens * 0.7);
        int currentTokens = 0;
        List<ChatMessage> limitedMessages = new ArrayList<>();

        // 从最新的消息开始计算，确保最近的对话优先保留
        for (int i = contextMessages.size() - 1; i >= 0; i--) {
            ChatMessage message = contextMessages.get(i);
            int messageTokens = estimateTokens(String.valueOf(message.getContent()));

            if (currentTokens + messageTokens > availableTokens) {
                log.info("达到最大上下文限制，停止添加历史消息。当前Token: {}, 最大Token: {}",
                        currentTokens, availableTokens);
                break;
            }

            currentTokens += messageTokens;
            limitedMessages.add(0, message); // 插入到开头，保持时间顺序
        }

        log.info("上下文消息限制完成，原始消息数: {}, 限制后消息数: {}, 预估Token数: {}",
                contextMessages.size(), limitedMessages.size(), currentTokens);

        return limitedMessages;
    }

    /**
     * 估算文本的Token数量
     *
     * @param text 文本内容
     * @return 估算的Token数量
     */
    private int estimateTokens(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 简单估算：中文字符*2 + 英文单词*1
        int chineseChars = 0;
        int englishWords = 0;

        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fff) { // 中文字符范围
                chineseChars++;
            }
        }

        // 英文单词数量估算（按空格分割）
        String[] words = text.replaceAll("[^a-zA-Z\\s]", "").split("\\s+");
        for (String word : words) {
            if (!word.trim().isEmpty()) {
                englishWords++;
            }
        }

        return chineseChars * 2 + englishWords;
    }

    /**
     * 根据角色ID获取模型配置
     *
     * @param roleId 角色ID
     * @return 模型配置
     */
    private AiModelVO getModelByRoleId(Long roleId) {
        if (roleId == null) {
            // 如果没有指定角色，返回默认模型
            return getDefaultModel();
        }

        Long modelId = aiRoleService.getRoleModelId(roleId);
        if (modelId == null) {
            log.warn("角色{}未配置模型，使用默认模型", roleId);
            return getDefaultModel();
        }

        AiModelVO model = aiModelService.getModelById(modelId);
        if (model == null) {
            log.warn("角色{}配置的模型{}不存在，使用默认模型", roleId, modelId);
            return getDefaultModel();
        }

        return model;
    }

    /**
     * 获取默认模型
     *
     * @return 默认模型配置
     */
    private AiModelVO getDefaultModel() {
        // 获取第一个可用的聊天模型作为默认模型
        List<AiModelVO> chatModels = aiModelService.getModelsByType("chat");
        if (!chatModels.isEmpty()) {
            return chatModels.get(0);
        }

        // 如果没有聊天模型，创建一个默认配置
        AiModelVO defaultModel = new AiModelVO();
        defaultModel.setModelKey("doubao-pro-4o");
        defaultModel.setModelName("豆包-4o");
        defaultModel.setMaxTokens(32768);
        return defaultModel;
    }

    /**
     * 创建用户消息
     */
    private AiChatEntity createUserMessage(AiChatMessageSendReqVO sendReqVO, AiUserVO userInfo) {
        AiChatEntity userMessage = new AiChatEntity();
        userMessage.setConversationId(sendReqVO.getConversationId());
        userMessage.setUserId(userInfo.getId());
        userMessage.setMessageType(MessageType.USER.getCode());
        userMessage.setContent(sendReqVO.getContent());
        userMessage.setUserContext(Boolean.TRUE.equals(sendReqVO.getUseContext()) ? 1 : 0);
        userMessage.setDeleteFlag(0);
        userMessage.setCreateTime(LocalDateTime.now());
        userMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.save(userMessage);
        return userMessage;
    }

    /**
     * 创建助手消息
     */
    private AiChatEntity createAssistantMessage(AiChatMessageSendReqVO sendReqVO, AiUserVO userInfo, Long replyId) {
        AiChatEntity assistantMessage = new AiChatEntity();
        assistantMessage.setConversationId(sendReqVO.getConversationId());
        assistantMessage.setReplyId(replyId);
        assistantMessage.setUserId(userInfo.getId());
        assistantMessage.setMessageType(MessageType.ASSISTANT.getCode());
        // 初始为空
        assistantMessage.setContent("");
        assistantMessage.setUserContext(0);
        assistantMessage.setDeleteFlag(0);
        assistantMessage.setCreateTime(LocalDateTime.now());
        assistantMessage.setUpdateTime(LocalDateTime.now());
        aiChatService.save(assistantMessage);
        return assistantMessage;
    }

    /**
     * 更新助手消息内容
     */
    private void updateAssistantMessageContent(Long messageId, String content) {
        AiChatEntity updateEntity = new AiChatEntity();
        updateEntity.setId(messageId);
        updateEntity.setContent(content);
        updateEntity.setUpdateTime(LocalDateTime.now());
        aiChatService.updateById(updateEntity);
    }

    /**
     * 从BotChatCompletionChunk中提取增量内容
     */
    private String extractDeltaContent(ChatCompletionChunk chunk) {
        try {
            if (chunk != null && chunk.getChoices() != null && !chunk.getChoices().isEmpty()) {
                var choice = chunk.getChoices().get(0);
                if (choice != null && choice.getMessage() != null) {
                    return String.valueOf(choice.getMessage().getContent());
                }
            }
        } catch (Exception e) {
            log.warn("提取增量内容失败", e);
        }
        return null;
    }
}
