package com.coocaa.ad.cheese.authority.common.tools.query.params;


import com.coocaa.ad.cheese.authority.common.tools.query.enums.FilterType;
import com.coocaa.ad.cheese.authority.common.tools.query.enums.ICondition;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 类IFilter实现描述:
 *
 * 查询过滤条件
 *
 * <AUTHOR>
 * @date 2022年05月08 12:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class IFilter {

    /**
     * 字段名
     */
    private String filterName;

    /**
     * 时间格式化 dateFormat 不为空则是时间类型
     */
    private String dateFormat;

    /**
     * 过滤条件 枚举 FilterType
     */
    private FilterType filterType;

    /**
     * 表达式
     */
    private ICondition condition;

    /**
     * 当过滤filterName多个结果
     */
    private List<Object> filterValue;

    private List<IFilter> children;

}
