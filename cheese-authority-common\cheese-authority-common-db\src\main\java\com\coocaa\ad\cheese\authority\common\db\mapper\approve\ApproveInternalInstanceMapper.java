package com.coocaa.ad.cheese.authority.common.db.mapper.approve;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.authority.common.db.bean.InstanceSearchDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalInstanceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Mapper
public interface ApproveInternalInstanceMapper extends BaseMapper<ApproveInternalInstanceEntity> {

    IPage<ApproveInternalInstanceEntity> listForTask(@Param("page") IPage<ApproveInternalInstanceEntity> page, @Param("dto") InstanceSearchDTO dto);
}
