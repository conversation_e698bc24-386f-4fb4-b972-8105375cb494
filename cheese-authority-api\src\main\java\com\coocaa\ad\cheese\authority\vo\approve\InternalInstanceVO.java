package com.coocaa.ad.cheese.authority.vo.approve;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InternalInstanceVO {

    private Integer id;

    /** 规则表id */
    @Schema(description = "规则表id")
    private Integer ruleId;

    /** 规则编号 */
    @Schema(description = "规则编号")
    private Integer ruleCode;

    /** 审批实例code */
    @Schema(description = "审批实例code")
    private String instanceCode;

    /** 审批任务名称 */
    @Schema(description = "审批任务名称")
    private String approvalName;

    /** 审批结果，字典0138 */
    @Schema(description = "审批结果，字典0138")
    private String approvalResult;

    @Schema(description = "审批结果")
    private String approvalResultName;

    /** 审批状态，字典0141 */
    @Schema(description = "审批状态，字典0141")
    private String approvalStatus;

    @Schema(description = "审批状态")
    private String approvalStatusName;

    /** 取消原因，字典0140 */
    @Schema(description = "取消原因，字典0140")
    private String cancelReason;

    @Schema(description = "取消原因")
    private String cancelReasonName;

    /** 审批单提交人ID */
    @Schema(description = "审批单提交人ID")
    private Integer userId;

    @Schema(description = "抄送人")
    private Integer ccReviewer;

    @Schema(description = "审批单提交人(申请人)")
    private String userName;

    /** 审批单结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审批单结束时间")
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "审批单创建时间")
    private LocalDateTime createTime;

    @Schema(description = "审批节点")
    private List<InstanceNodeVO> nodes = Collections.emptyList();
}
