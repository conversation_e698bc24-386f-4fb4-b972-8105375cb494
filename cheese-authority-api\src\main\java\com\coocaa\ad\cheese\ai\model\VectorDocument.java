package com.coocaa.ad.cheese.ai.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 向量文档模型
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
public class VectorDocument {

    /**
     * 向量ID
     */
    private String vectorId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 分块ID
     */
    private Long segmentId;

    /**
     * 文本内容
     */
    private String content;

    /**
     * 向量数据
     */
    private float[] vector;

    /**
     * 元数据
     */
    private String metadata;

    /**
     * 字数
     */
    private Integer wordCount;

    /**
     * 文档位置
     */
    private Integer position;

    /**
     * 创建时间戳
     */
    private Long createTime;
}
