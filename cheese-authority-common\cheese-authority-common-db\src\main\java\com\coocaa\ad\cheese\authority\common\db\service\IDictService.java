package com.coocaa.ad.cheese.authority.common.db.service;

import com.coocaa.ad.cheese.authority.common.db.entity.DictEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 字典 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IDictService extends IService<DictEntity> {

    Integer getFirstLevelMaxCode();

    DictEntity queryByName(String name);

    List<DictEntity> listFirstLevelsRank(String param);

    /**
     * @deprecated Use listSecondByParentCode instead
     */
    @Deprecated
    List<DictEntity> listSecondByParent(Integer parentId, String name);

    /**
     * @deprecated Use querySecondLevelByCode instead
     */
    @Deprecated
    DictEntity querySecondLevel(String name, Integer parentId);

    /**
     * @deprecated Use countSecondByParentCode instead
     */
    @Deprecated
    int countSecondByParentId(Integer parentId);

    List<DictEntity> listSecondByParentCode(String code);
    
    /**
     * 通过父级code和名称返回二级字典列表
     * @param parentCode 父级编码
     * @param name 名称，可以为空
     * @return 二级字典列表
     */
    List<DictEntity> listSecondByParentCodeAndName(String parentCode, String name);
    
    /**
     * 根据名称和父级code查询二级字典
     * @param name 字典名称
     * @param parentCode 父级编码
     * @return 字典实体
     */
    DictEntity querySecondLevelByCode(String name, String parentCode);
    
    /**
     * 根据父级code统计二级字典数量
     * @param parentCode 父级编码
     * @return 数量
     */
    int countSecondByParentCode(String parentCode);

    List<DictEntity> listByCodes(List<String> codes);

    DictEntity getBySecondName(String name, String parentCode);
}
