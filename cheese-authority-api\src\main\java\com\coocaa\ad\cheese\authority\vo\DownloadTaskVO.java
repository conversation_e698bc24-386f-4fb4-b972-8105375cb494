package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.DownloadConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 下载任务表VO
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Data
@Accessors(chain = true)
@Schema(name = "DownloadTaskVO", description = "下载任务表VO")
public class DownloadTaskVO {

    List<DownloadAttachmentVO> attachments;

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "系统类型")
    private String sysType;
    private String sysTypeName;

    @Schema(description = "任务类型")
    private String type;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务类别", maxLength = 50)
    private String category;
    private String categoryName;

    @Schema(description = "状态", maxLength = 20)
    private String status;
    private String statusName;

    @Schema(description = "执行时的参数", maxLength = 2000)
    private String executeParams;

    @Schema(description = "失败原因", maxLength = 2000)
    private String failMsg;

    @Schema(description = "删除标记  [0:否, 1:是]")
    private Integer deleteFlag;

    @Schema(description = "创建人")
    private Integer creator;
    private String creatorName;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "下载次数")
    private Integer count;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DownloadConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DownloadConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;
}