package com.coocaa.ad.cheese.authority.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.Accessors;

//@HeadFontHeight(14)
//@HeadFontStyle(name = "Arial", bold = true)
@Data
@Accessors(chain = true)
public class ExportUserVO implements java.io.Serializable {


    @ExcelIgnore
    @ColumnWidth(20)
    private Integer userId;

    @ExcelProperty(value = "用户姓名", index = 0)
    @ColumnWidth(20)
    private String userName;

    @ExcelProperty(value = "工号", index = 1)
    @ColumnWidth(20)
    private String wno;

    @ExcelProperty(value = "上级姓名", index = 2)
    @ColumnWidth(40)
    private String leaderName;

    @ExcelProperty(value = "所属部门", index = 3)
    @ColumnWidth(60)
    private String departmentName;

    // Getters and Setters
}
