
package com.coocaa.ad.cheese.authority.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 下载附件
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Data
@Accessors(chain = true)
public class DownloadAttachmentDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 下载任务ID
     */
    private Long taskId;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 附件全路径
     */
    private String url;

    /**
     * 大小(字节)
     */
    private String size;

    /**
     * 文件类型 (pdf, doc,...)
     */
    private String fileType;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}