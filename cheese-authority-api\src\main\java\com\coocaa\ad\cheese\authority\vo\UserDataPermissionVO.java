package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class UserDataPermissionVO {
    /**
     * 数据权限类型
     */
    @Schema(description = "数据权限类型：1-个人;2-自己及下属；3-城市所有;4-全部", type = "String", example = "1")
    private Integer permissionType;
    /**
     * 数据权限类型
     */
    @Schema(description = "权限数据", type = "List", example = "")
    private List<Integer> data;
}

