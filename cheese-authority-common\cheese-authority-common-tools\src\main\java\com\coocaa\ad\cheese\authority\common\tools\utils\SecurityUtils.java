package com.coocaa.ad.cheese.authority.common.tools.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.RandomUtil;
import de.mkammerer.argon2.Argon2;
import de.mkammerer.argon2.Argon2Factory;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/09/09 11:43
 * @desc <p>安全工具类</p>
 */
public final class SecurityUtils {
    /**
     * 加密字段
     *
     * @param rsaParam 前端传输的值
     * @return 加密值
     * @throws Exception 异常
     */
    public static String encryptParam(String rsaParam) throws Exception {
        String paramValue = RsaUtils.decryptByPrivateKey(RsaUtils.privateKey, rsaParam);
        return AesUtils.encryptHex(paramValue);
    }

    /**
     * 加密返回给前端
     *
     * @param resParam 数据库加密字段
     * @return 加密返回给前端
     * @throws Exception 异常
     */
    public static String frontParam(String resParam) throws Exception {
        String param = AesUtils.decryptStr(resParam);
        return RsaUtils.encryptByPublicKey(RsaUtils.publicKey, param);
    }

    /**
     * 对密码进行加密
     *
     * @param inputPassword 用户输入的密码 (前端已经对明文进行过MD5)
     * @return 加盐的密码
     */
    public static String generatePassword(String inputPassword) {
        Objects.requireNonNull(inputPassword, "密码不能为空");

        Argon2 argon2 = Argon2Factory.create();
        try {
            return Base64.encode(argon2.hash(5, 65536, 1, inputPassword.getBytes()));
        } finally {
            argon2.wipeArray(inputPassword.toCharArray());
        }
    }

    /**
     * 生成随机密码
     *
     * @return 随机密码
     */
    public static String generateRandomPassword() {
        return generateRandomPassword(16);
    }

    /**
     * 生成随机密码
     *
     * @param count 密码位数
     * @return 随机密码
     */
    public static String generateRandomPassword(int count) {
        String pwd = RandomUtil.randomString(count);
        // 在密码中随机插入一个特殊字符（@、#、$、%、^、&、*、（、））
        String specialChars = "@#$%^&*()";
        int index = RandomUtil.randomInt(0, pwd.length());
        return pwd.substring(0, index) + RandomUtil.randomChar(specialChars) + pwd.substring(index);
    }


    /**
     * 密码验证
     *
     * @param storedPassword 存储在DB中的密码
     * @param inputPassword  用户输入的密码
     * @return true: 密码正确，false:密码错误
     */
    public static boolean verifyPassword(String storedPassword, String inputPassword) {
        Objects.requireNonNull(storedPassword, "密码不能为空");
        Objects.requireNonNull(inputPassword, "密码不能为空");
        return Argon2Factory.create().verify(Base64.decodeStr(storedPassword), inputPassword.getBytes());
    }


    public static void main(String[] args) {
        String plainPassword = generateRandomPassword();
        String inputPassword = DigestUtils.md5Hex(plainPassword);
        System.out.println("密码: " + plainPassword + " | " + inputPassword);

        for (int i = 0; i < 10; i++) {
            // 注册流程
            String storedPassword = generatePassword(inputPassword);
            System.out.println("生成的哈希值: " + storedPassword);

            // 登录流程
            boolean isPasswordValid = verifyPassword(storedPassword, inputPassword);
            System.out.println("密码验证结果: " + isPasswordValid);
        }
    }
}
