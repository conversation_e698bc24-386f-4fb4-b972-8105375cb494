package com.coocaa.ad.cheese.authority.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.coocaa.ad.cheese.authority.vo.serializer.EncryptDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 用户创建信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-23
 */
@Data
@Schema(name = "UserAddVO", description = "新增用户信息")
public class UserAddVO {
    @NotBlank(message = "姓名不能为空")
    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "CC2362")
    private String wno;

    @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "手机号", type = "String", example = "13012345678")
    private String mobile;

    @JSONField(deserializeUsing = EncryptDeserializer.class)
    @Schema(description = "邮箱", type = "String", example = "<EMAIL>")
    private String email;

    @Schema(description = "登陆名", type = "String", example = "admin")
    private String userName;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码, 需要对明文进行MD5", type = "String")
    private String password;

    @Schema(description = "角色信息", type = "List")
    private List<Integer> roles;

    @Schema(description = "渠道信息", type = "List")
    private List<Integer> channels;

    @Schema(description = "城市组信息", type = "List")
    private List<Integer> cityGroups;

    @Schema(description = "用户类型：1-内部用户；2-外部代理商", type = "int", example = "1")
    @NotNull(message = "用户类型不能为空")
    private Integer type;
    @Schema(description = "数据权限类型：1-个人;2-自己及下属；3-城市所有;4-全部", type = "String", example = "1")
    private Integer permissionType;

    @Schema(description = "所属部门openId", type = "String")
    private String departmentOpenId;
}
