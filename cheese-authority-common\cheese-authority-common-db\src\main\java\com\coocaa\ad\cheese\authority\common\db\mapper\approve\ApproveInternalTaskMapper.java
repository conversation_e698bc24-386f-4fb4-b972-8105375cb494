package com.coocaa.ad.cheese.authority.common.db.mapper.approve;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.authority.common.db.bean.TaskSearchDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalTaskEntity;
import com.coocaa.ad.cheese.authority.common.db.vo.approve.TaskDealCountSelectVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Mapper
public interface ApproveInternalTaskMapper extends BaseMapper<ApproveInternalTaskEntity> {

    IPage<ApproveInternalTaskEntity> listForTask(@Param("page") IPage<ApproveInternalTaskEntity> page, @Param("dto") TaskSearchDTO dto);

    List<TaskDealCountSelectVO> getUserDealCount(@Param("userId") Integer userId, @Param("codes") List<Long> codes);
}
