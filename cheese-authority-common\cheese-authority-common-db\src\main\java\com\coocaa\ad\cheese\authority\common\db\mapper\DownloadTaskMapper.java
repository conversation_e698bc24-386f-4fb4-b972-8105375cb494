package com.coocaa.ad.cheese.authority.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.authority.common.db.bean.DownloadTaskDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.DownloadTaskEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 下载任务表接口
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
public interface DownloadTaskMapper extends BaseMapper<DownloadTaskEntity> {

    /**
     * 按条件查询下载任务表列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 下载任务表列表
     */
    IPage<DownloadTaskEntity> pageList(@Param("page") IPage<DownloadTaskEntity> page, @Param("condition") DownloadTaskDTO condition);
}