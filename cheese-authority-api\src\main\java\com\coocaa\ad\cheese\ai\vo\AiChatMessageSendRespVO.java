package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * AI聊天消息发送响应VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiChatMessageSendRespVO", description = "AI聊天消息发送响应VO")
public class AiChatMessageSendRespVO {

    @Schema(description = "用户消息ID")
    private Long userMessageId;

    @Schema(description = "助手消息ID")
    private Long assistantMessageId;

    @Schema(description = "增量内容")
    private String content;

    @Schema(description = "完整内容")
    private String fullContent;

    @Schema(description = "对话ID")
    private Long conversationId;

    @Schema(description = "模型名称")
    private String modelName;

    @Schema(description = "是否完成")
    private Boolean finished;

    @Schema(description = "错误信息")
    private String error;
}
