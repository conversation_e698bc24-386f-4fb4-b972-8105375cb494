package com.coocaa.ad.cheese.authority.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/23
 */
@Data
public class DictSecondLevelVO {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer parentId;

    private String name;

    private String code;
    @Schema(description = "值越小越靠前")
    private Integer rank;

    private Boolean status;


}
