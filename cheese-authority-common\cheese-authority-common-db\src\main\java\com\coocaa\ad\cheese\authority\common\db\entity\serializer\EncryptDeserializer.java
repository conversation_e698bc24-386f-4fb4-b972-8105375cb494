package com.coocaa.ad.cheese.authority.common.db.entity.serializer;

import com.alibaba.fastjson2.J<PERSON><PERSON>eader;
import com.alibaba.fastjson2.reader.ObjectReader;
import com.coocaa.ad.cheese.authority.common.tools.utils.AesUtils;
import com.coocaa.ad.cheese.authority.common.tools.utils.RsaUtils;
import com.coocaa.ad.cheese.authority.common.tools.utils.StringUtils;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;

/**
 * 对手机号或邮箱进行加密
 * <p>
 * 步骤:
 * 1. 先对 RSA 解密
 * 2. 再使用 AES 加密
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-06
 */
@Slf4j
public class EncryptDeserializer extends JsonDeserializer<String> implements ObjectReader<String> {
    @Override
    public String readObject(JSONReader jsonReader, Type fieldType, Object fieldName, long features) {
        return getText(jsonReader.readString());
    }

    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        return getText(p.getValueAsString());
    }


    /**
     * 解析成数据库存储的密文
     */
    private String getText(String value) {
        try {
            String plainText = RsaUtils.decryptByPrivateKey(RsaUtils.privateKey, value);
            log.info("对参数[{}]解密结果:[{}]", value, plainText);

            String encryptedText = AesUtils.encryptHex(plainText);
            log.info("对参数[{}]加密结果:[{}]", plainText, encryptedText);

            return encryptedText;
        } catch (Exception e) {
            log.warn("解密/加密文本({})失败", value);
            return StringUtils.EMPTY;
        }
    }
}
