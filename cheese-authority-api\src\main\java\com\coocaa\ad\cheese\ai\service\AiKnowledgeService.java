package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeSegmentEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeService;
import com.coocaa.ad.cheese.ai.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * AI知识库业务服务
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiKnowledgeService {

    private final IAiKnowledgeService aiKnowledgeService;
    private final AiKnowledgeDocumentService documentService;
    private final AiKnowledgeSegmentService segmentService;
    private final AiVectorService vectorService;

    /**
     * 获取所有知识库列表
     *
     * @return 知识库列表
     */
    public List<AiKnowledgeVO> getAllKnowledgeBases() {
        log.info("获取所有知识库列表");
        
        LambdaQueryWrapper<AiKnowledgeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiKnowledgeEntity::getDeleteFlag, 0)
               .orderByDesc(AiKnowledgeEntity::getCreateTime);
        
        List<AiKnowledgeEntity> entities = aiKnowledgeService.list(wrapper);
        List<AiKnowledgeVO> knowledgeBases = new ArrayList<>();
        
        for (AiKnowledgeEntity entity : entities) {
            AiKnowledgeVO vo = convertToVO(entity);
            // 统计文档和分块数量
            vo.setDocumentCount(documentService.countByKnowledgeId(entity.getId()));
            vo.setSegmentCount(segmentService.countByKnowledgeId(entity.getId()));
            knowledgeBases.add(vo);
        }
        
        log.info("获取知识库列表成功，共{}个知识库", knowledgeBases.size());
        return knowledgeBases;
    }

    /**
     * 根据ID获取知识库
     *
     * @param knowledgeId 知识库ID
     * @return 知识库信息
     */
    public AiKnowledgeVO getKnowledgeBaseById(Long knowledgeId) {
        log.info("根据ID获取知识库，知识库ID: {}", knowledgeId);
        
        AiKnowledgeEntity entity = aiKnowledgeService.getById(knowledgeId);
        if (entity == null || entity.getDeleteFlag() == 1) {
            throw new RuntimeException("知识库不存在");
        }
        
        AiKnowledgeVO vo = convertToVO(entity);
        vo.setDocumentCount(documentService.countByKnowledgeId(knowledgeId));
        vo.setSegmentCount(segmentService.countByKnowledgeId(knowledgeId));
        
        return vo;
    }

    /**
     * 上传文档到知识库
     *
     * @param file 文件
     * @param uploadReq 上传请求
     * @return 文档ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long uploadDocument(MultipartFile file, AiKnowledgeDocumentUploadReqVO uploadReq) {
        log.info("上传文档到知识库，知识库ID: {}, 文件名: {}", uploadReq.getKnowledgeId(), file.getOriginalFilename());
        
        // 1. 验证知识库存在
        AiKnowledgeEntity knowledge = aiKnowledgeService.getById(uploadReq.getKnowledgeId());
        if (knowledge == null || knowledge.getDeleteFlag() == 1) {
            throw new RuntimeException("知识库不存在");
        }
        
        // 2. 保存文档记录
        Long documentId = documentService.saveDocument(file, uploadReq);
        
        // 3. 如果需要立即处理，则进行文档分块和向量化
        if (Boolean.TRUE.equals(uploadReq.getProcessImmediately())) {
            processDocument(documentId);
        }
        
        log.info("文档上传成功，文档ID: {}", documentId);
        return documentId;
    }

    /**
     * 上传文本内容到知识库
     *
     * @param uploadReq 上传请求
     * @return 文档ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long uploadTextContent(AiKnowledgeDocumentUploadReqVO uploadReq) {
        log.info("上传文本内容到知识库，知识库ID: {}, 内容长度: {}", 
                uploadReq.getKnowledgeId(), 
                uploadReq.getContent() != null ? uploadReq.getContent().length() : 0);
        
        // 1. 验证知识库存在
        AiKnowledgeEntity knowledge = aiKnowledgeService.getById(uploadReq.getKnowledgeId());
        if (knowledge == null || knowledge.getDeleteFlag() == 1) {
            throw new RuntimeException("知识库不存在");
        }
        
        // 2. 保存文档记录
        Long documentId = documentService.saveTextDocument(uploadReq);
        
        // 3. 如果需要立即处理，则进行文档分块和向量化
        if (Boolean.TRUE.equals(uploadReq.getProcessImmediately())) {
            processDocument(documentId);
        }
        
        log.info("文本内容上传成功，文档ID: {}", documentId);
        return documentId;
    }

    /**
     * 在知识库中搜索
     *
     * @param searchReq 搜索请求
     * @return 搜索结果
     */
    public AiKnowledgeSearchRespVO searchKnowledge(AiKnowledgeSearchReqVO searchReq) {
        log.info("在知识库中搜索，知识库ID: {}, 查询: {}", searchReq.getKnowledgeId(), searchReq.getQuery());
        
        long startTime = System.currentTimeMillis();
        
        // 1. 验证知识库存在
        AiKnowledgeEntity knowledge = aiKnowledgeService.getById(searchReq.getKnowledgeId());
        if (knowledge == null || knowledge.getDeleteFlag() == 1) {
            throw new RuntimeException("知识库不存在");
        }
        
        // 2. 向量搜索
        List<AiKnowledgeSearchRespVO.SearchResult> results = vectorService.searchSimilarSegments(searchReq);
        
        long searchTime = System.currentTimeMillis() - startTime;
        
        AiKnowledgeSearchRespVO response = new AiKnowledgeSearchRespVO();
        response.setResults(results);
        response.setSearchTime(searchTime);
        response.setTotalCount(results.size());
        
        log.info("知识库搜索完成，耗时: {}ms, 结果数量: {}", searchTime, results.size());
        return response;
    }

    /**
     * 处理文档（分块和向量化）
     *
     * @param documentId 文档ID
     */
    private void processDocument(Long documentId) {
        try {
            log.info("开始处理文档，文档ID: {}", documentId);
            
            // 1. 文档分块
            List<AiKnowledgeSegmentEntity> segments = segmentService.chunkDocument(documentId);
            
            // 2. 向量化
            vectorService.vectorizeSegments(segments);
            
            // 3. 更新文档状态
            documentService.updateDocumentStatus(documentId, "completed", segments.size(), null);
            
            log.info("文档处理完成，文档ID: {}, 分块数量: {}", documentId, segments.size());
            
        } catch (Exception e) {
            log.error("文档处理失败，文档ID: {}", documentId, e);
            documentService.updateDocumentStatus(documentId, "failed", 0, e.getMessage());
            throw new RuntimeException("文档处理失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO
     */
    private AiKnowledgeVO convertToVO(AiKnowledgeEntity entity) {
        AiKnowledgeVO vo = new AiKnowledgeVO();
        vo.setId(entity.getId());
        vo.setName(entity.getName());
        vo.setDescription(entity.getDescription());
        vo.setEmbeddingModel(entity.getEmbeddingModel());
        vo.setVectorDimension(entity.getVectorDimension());
        vo.setChunkSize(entity.getChunkSize());
        vo.setChunkOverlap(entity.getChunkOverlap());
        vo.setStatus(entity.getStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setCreator(entity.getCreator());
        vo.setUpdateTime(entity.getUpdateTime());
        vo.setOperator(entity.getOperator());
        return vo;
    }
}
