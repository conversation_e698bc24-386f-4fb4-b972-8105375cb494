-- AI用户表重建SQL
-- 执行时间：2025-07-17
-- 说明：重建ai_user表，只保留必要字段并新增微信用户相关字段

-- 1. 备份现有数据（可选，根据需要执行）
-- CREATE TABLE `ai_user_backup` AS SELECT * FROM `ai_user`;

-- 2. 删除现有表
DROP TABLE IF EXISTS `ai_user`;

-- 3. 重新创建ai_user表
CREATE TABLE `ai_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(50) NOT NULL COMMENT '用户OpenID',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `nick_name` varchar(100) DEFAULT NULL COMMENT '昵称',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `language` varchar(20) DEFAULT NULL COMMENT '语言',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `union_id` varchar(50) DEFAULT NULL COMMENT '微信UnionID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '操作人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI用户表';

-- 4. 插入示例数据（可选）
-- INSERT INTO `ai_user` (`open_id`, `mobile`, `nick_name`, `gender`, `language`, `city`, `province`, `country`, `avatar_url`, `union_id`) VALUES 
-- ('wx_test_user_001', '13800138000', '测试用户1', '1', 'zh_CN', '深圳市', '广东省', '中国', 'https://example.com/avatar1.jpg', 'union_test_001'),
-- ('wx_test_user_002', '13800138001', '测试用户2', '2', 'zh_CN', '广州市', '广东省', '中国', 'https://example.com/avatar2.jpg', 'union_test_002');

-- 表结构说明：
-- id: 主键ID，自增
-- open_id: 用户OpenID，微信用户唯一标识，设置唯一索引
-- mobile: 手机号，可为空
-- nick_name: 用户昵称，从微信获取
-- gender: 性别，从微信获取（0-未知，1-男，2-女）
-- language: 语言，从微信获取（如：zh_CN）
-- city: 城市，从微信获取
-- province: 省份，从微信获取
-- country: 国家，从微信获取
-- avatar_url: 头像URL，从微信获取
-- union_id: 微信UnionID，用于关联不同应用的同一用户
-- create_time: 创建时间，自动填充
-- creator: 创建人ID，默认为0
-- update_time: 更新时间，自动更新
-- operator: 操作人ID，默认为0
-- delete_flag: 软删除标记，0-正常，1-已删除

-- 索引说明：
-- uk_open_id: open_id唯一索引，确保同一OpenID只有一条记录
-- idx_mobile: 手机号索引，用于手机号查询
-- idx_union_id: UnionID索引，用于跨应用用户关联
-- idx_create_time: 创建时间索引，用于时间范围查询
-- idx_delete_flag: 删除标记索引，用于过滤已删除记录

-- 使用场景：
-- 1. 微信登录时，根据OpenID查找或创建用户
-- 2. 获取用户信息时，从微信API获取详细信息并更新到表中
-- 3. 用户管理和统计分析

-- 查询示例：
-- 1. 根据OpenID查找用户：
-- SELECT * FROM ai_user WHERE open_id = 'wx_user_123' AND delete_flag = 0;

-- 2. 根据手机号查找用户：
-- SELECT * FROM ai_user WHERE mobile = '13800138000' AND delete_flag = 0;

-- 3. 根据UnionID查找用户：
-- SELECT * FROM ai_user WHERE union_id = 'union_123' AND delete_flag = 0;

-- 4. 查询最近注册的用户：
-- SELECT * FROM ai_user WHERE delete_flag = 0 ORDER BY create_time DESC LIMIT 10;
