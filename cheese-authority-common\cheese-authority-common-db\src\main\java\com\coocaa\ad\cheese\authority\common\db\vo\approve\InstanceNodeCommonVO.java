package com.coocaa.ad.cheese.authority.common.db.vo.approve;



import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InstanceNodeCommonVO {

    /** 主键ID */
    private Integer id;

    /** 规则编号 */
    private Integer ruleCode;

    /** 审批实例code */
    private String instanceCode;

    /** 规则审批人员表ID */
    private Integer personId;

    /** 审核人员排序 */
    private Integer rank;

    /** 审批人员ID */
    private String userId;

    /** 取消原因（字典0140） */
    private String cancelReason;

    /** 任务状态（字典0139） */
    private String nodeStatus;

    /** 节点开始时间 */
    private LocalDateTime startTime;

    /** 节点结束时间 */
    private LocalDateTime endTime;

    /** 是否为审批节点，0：审批节点，1：提交人节点，2：结束节点 */
    private Integer approvalFlag;

    /** 审批结果，字典0138 */
    private String approvalResult;

    /** 审批意见 */
    private String comment;

    private String taskName;
}
