package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.UserCompanyDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentRelationEntity;

import java.util.List;

/**
 * <p>
 * 用户部门关联信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface IUserDepartmentRelationService extends IService<UserDepartmentRelationEntity> {
    /**
     * 获取用户公司部门信息
     * @param userId
     * @return
     */
    List<UserCompanyDepartmentEntity> getUserCompanyDepartmentList(Integer userId);
}
