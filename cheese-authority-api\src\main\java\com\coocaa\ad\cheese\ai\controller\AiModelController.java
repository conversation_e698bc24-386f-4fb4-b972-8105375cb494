package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.service.AiModelService;
import com.coocaa.ad.cheese.ai.vo.AiModelVO;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI模型管理Controller
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@RestController
@RequestMapping("/sys/ai/model")
@Tag(name = "AI模型管理", description = "AI模型相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class AiModelController {

    private final AiModelService aiModelService;

    @Operation(
            summary = "获取所有模型列表",
            description = "获取所有可用的AI模型列表"
    )
    @GetMapping("/list")
    public ResultTemplate<List<AiModelVO>> getAllModels() {
        log.info("获取所有模型列表请求");
        
        try {
            List<AiModelVO> models = aiModelService.getAllModels();
            return ResultTemplate.success(models);
            
        } catch (Exception e) {
            log.error("获取模型列表失败", e);
            return ResultTemplate.fail("获取模型列表失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "根据ID获取模型",
            description = "根据模型ID获取模型详细信息"
    )
    @GetMapping("/{modelId}")
    public ResultTemplate<AiModelVO> getModelById(
            @Parameter(description = "模型ID", required = true)
            @PathVariable Long modelId) {
        
        log.info("根据ID获取模型，模型ID: {}", modelId);
        
        try {
            AiModelVO model = aiModelService.getModelById(modelId);
            if (model == null) {
                return ResultTemplate.fail("模型不存在");
            }
            
            return ResultTemplate.success(model);
            
        } catch (Exception e) {
            log.error("获取模型失败，模型ID: {}", modelId, e);
            return ResultTemplate.fail("获取模型失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "根据类型获取模型列表",
            description = "根据模型类型获取模型列表"
    )
    @GetMapping("/type/{modelType}")
    public ResultTemplate<List<AiModelVO>> getModelsByType(
            @Parameter(description = "模型类型", required = true)
            @PathVariable String modelType) {
        
        log.info("根据类型获取模型列表，模型类型: {}", modelType);
        
        try {
            List<AiModelVO> models = aiModelService.getModelsByType(modelType);
            return ResultTemplate.success(models);
            
        } catch (Exception e) {
            log.error("获取模型列表失败，模型类型: {}", modelType, e);
            return ResultTemplate.fail("获取模型列表失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "根据标识符获取模型",
            description = "根据模型标识符获取模型详细信息"
    )
    @GetMapping("/key/{modelKey}")
    public ResultTemplate<AiModelVO> getModelByKey(
            @Parameter(description = "模型标识符", required = true)
            @PathVariable String modelKey) {
        
        log.info("根据标识符获取模型，模型标识符: {}", modelKey);
        
        try {
            AiModelVO model = aiModelService.getModelByKey(modelKey);
            if (model == null) {
                return ResultTemplate.fail("模型不存在");
            }
            
            return ResultTemplate.success(model);
            
        } catch (Exception e) {
            log.error("获取模型失败，模型标识符: {}", modelKey, e);
            return ResultTemplate.fail("获取模型失败: " + e.getMessage());
        }
    }
}
