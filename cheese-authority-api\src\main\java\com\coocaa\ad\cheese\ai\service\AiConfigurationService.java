package com.coocaa.ad.cheese.ai.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.redis.autoconfigure.RedisVectorStoreProperties;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

/**
 * AI配置验证服务
 * 
 * 在应用启动后验证Spring AI配置是否正确
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiConfigurationService {

    private final RedisVectorStoreProperties vectorStoreProperties;
    private final VectorStore vectorStore;
    private final EmbeddingModel embeddingModel;

    /**
     * 应用启动后验证配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateConfiguration() {
        log.info("=== AI配置验证开始 ===");
        
        // 验证RedisVectorStoreProperties
        validateVectorStoreProperties();
        
        // 验证VectorStore
        validateVectorStore();
        
        // 验证EmbeddingModel
        validateEmbeddingModel();
        
        log.info("=== AI配置验证完成 ===");
    }

    /**
     * 验证向量存储属性配置
     */
    private void validateVectorStoreProperties() {
        if (vectorStoreProperties != null) {
            log.info("✅ RedisVectorStoreProperties配置已加载:");
            log.info("   - 索引名称: {}", vectorStoreProperties.getIndexName());
            log.info("   - 前缀: {}", vectorStoreProperties.getPrefix());
            log.info("   - 初始化Schema: {}", vectorStoreProperties.isInitializeSchema());
        } else {
            log.warn("❌ RedisVectorStoreProperties配置未加载");
        }
    }

    /**
     * 验证向量存储
     */
    private void validateVectorStore() {
        if (vectorStore != null) {
            log.info("✅ VectorStore Bean已创建: {}", vectorStore.getClass().getSimpleName());
            try {
                // 可以在这里测试VectorStore的基本功能
                log.info("   - VectorStore功能正常");
            } catch (Exception e) {
                log.warn("   - VectorStore测试失败: {}", e.getMessage());
            }
        } else {
            log.info("ℹ️ VectorStore Bean未创建，将使用自定义Redis实现");
        }
    }

    /**
     * 验证嵌入模型
     */
    private void validateEmbeddingModel() {
        if (embeddingModel != null) {
            log.info("✅ EmbeddingModel Bean已创建: {}", embeddingModel.getClass().getSimpleName());
            try {
                // 可以在这里测试EmbeddingModel的基本功能
                log.info("   - EmbeddingModel功能正常");
            } catch (Exception e) {
                log.warn("   - EmbeddingModel测试失败: {}", e.getMessage());
            }
        } else {
            log.info("ℹ️ EmbeddingModel Bean未创建，将使用自定义嵌入模型工厂");
        }
    }

    /**
     * 获取配置信息
     */
    public ConfigurationInfo getConfigurationInfo() {
        ConfigurationInfo info = new ConfigurationInfo();
        
        // 向量存储配置
        if (vectorStoreProperties != null) {
            info.setVectorStoreConfigured(true);
            info.setIndexName(vectorStoreProperties.getIndexName());
            info.setPrefix(vectorStoreProperties.getPrefix());
            info.setInitializeSchema(vectorStoreProperties.isInitializeSchema());
        }
        
        // Spring AI组件状态
        info.setVectorStoreAvailable(vectorStore != null);
        info.setEmbeddingModelAvailable(embeddingModel != null);
        
        return info;
    }

    /**
     * 配置信息DTO
     */
    public static class ConfigurationInfo {
        private boolean vectorStoreConfigured;
        private String indexName;
        private String prefix;
        private boolean initializeSchema;
        private boolean vectorStoreAvailable;
        private boolean embeddingModelAvailable;

        // Getters and Setters
        public boolean isVectorStoreConfigured() { return vectorStoreConfigured; }
        public void setVectorStoreConfigured(boolean vectorStoreConfigured) { this.vectorStoreConfigured = vectorStoreConfigured; }
        public String getIndexName() { return indexName; }
        public void setIndexName(String indexName) { this.indexName = indexName; }
        public String getPrefix() { return prefix; }
        public void setPrefix(String prefix) { this.prefix = prefix; }
        public boolean isInitializeSchema() { return initializeSchema; }
        public void setInitializeSchema(boolean initializeSchema) { this.initializeSchema = initializeSchema; }
        public boolean isVectorStoreAvailable() { return vectorStoreAvailable; }
        public void setVectorStoreAvailable(boolean vectorStoreAvailable) { this.vectorStoreAvailable = vectorStoreAvailable; }
        public boolean isEmbeddingModelAvailable() { return embeddingModelAvailable; }
        public void setEmbeddingModelAvailable(boolean embeddingModelAvailable) { this.embeddingModelAvailable = embeddingModelAvailable; }
    }
}
