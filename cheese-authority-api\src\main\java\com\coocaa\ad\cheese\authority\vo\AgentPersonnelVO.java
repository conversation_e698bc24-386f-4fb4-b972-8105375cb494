package com.coocaa.ad.cheese.authority.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class AgentPersonnelVO {

    private Long id;

    @NotBlank(message = "员工编码不能为空")
    private String empCode;     //员工编码

    @NotBlank(message = "员工姓名不能为空")
    private String empName;     //员工姓名

    @NotBlank(message = "员工手机号码不能为空")
    private String empMobile;   //员工手机号码

    @NotBlank(message = "代理商账号不能为空")
    private String agentCode;   //代理商账号

    @NotBlank(message = "代理商名称不能为空")
    private String agentName;   //代理商名称

    @TableLogic(value = "0", delval = "1")
    private Integer status;

    private String createBy;

    private String updateBy;
}
