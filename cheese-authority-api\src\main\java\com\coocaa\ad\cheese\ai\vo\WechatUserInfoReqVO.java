package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 微信小程序获取用户信息请求VO
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Data
@Schema(description = "微信小程序获取用户信息请求")
public class WechatUserInfoReqVO {

    @Schema(description = "会话密钥", required = true)
    @NotBlank(message = "会话密钥不能为空")
    private String sessionKey;

    @Schema(description = "加密数据", required = true)
    @NotBlank(message = "加密数据不能为空")
    private String encryptedData;

    @Schema(description = "初始向量", required = true)
    @NotBlank(message = "初始向量不能为空")
    private String iv;

    @Schema(description = "用户OpenID", required = true)
    @NotBlank(message = "用户OpenID不能为空")
    private String openId;
}
