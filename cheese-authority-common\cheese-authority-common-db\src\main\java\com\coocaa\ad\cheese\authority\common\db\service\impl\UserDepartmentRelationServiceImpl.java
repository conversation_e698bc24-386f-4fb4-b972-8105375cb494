package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.coocaa.ad.cheese.authority.common.db.entity.UserCompanyDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentRelationEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserDepartmentRelationMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IUserDepartmentRelationService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户部门关联信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Service
public class UserDepartmentRelationServiceImpl extends ServiceImpl<UserDepartmentRelationMapper, UserDepartmentRelationEntity> implements IUserDepartmentRelationService {

    @Override
    public List<UserCompanyDepartmentEntity> getUserCompanyDepartmentList(Integer userId) {
        return getBaseMapper().getUserCompanyDepartmentList( userId);
    }
}
