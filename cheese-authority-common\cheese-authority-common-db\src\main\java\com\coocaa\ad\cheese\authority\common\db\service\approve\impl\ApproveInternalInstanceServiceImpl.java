package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.bean.InstanceSearchDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalInstanceEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalInstanceMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalInstanceService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalInstanceServiceImpl extends ServiceImpl<ApproveInternalInstanceMapper, ApproveInternalInstanceEntity> implements IApproveInternalInstanceService {

    @Override
    public IPage<ApproveInternalInstanceEntity> listForTask(IPage<ApproveInternalInstanceEntity> page, InstanceSearchDTO dto) {
        return this.baseMapper.listForTask(page, dto);
    }
}
