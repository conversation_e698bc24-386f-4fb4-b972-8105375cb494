package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("dict")
public class DictEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 父编码，用于替代parentId，实现跨环境一致性
     */
    private String parentCode;

    /**
     * 字典名称
     */
    private String name;

    /**
     * 字典编码
     */
    private String code;

   /* *//**
     * 字典值
     *//*
    private String value;
*/
    /**
     * 排序(自然顺序，越小越靠前)
     */
    private Integer rank;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    private Boolean status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;


}
