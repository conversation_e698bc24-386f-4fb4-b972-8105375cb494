package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeSegmentEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * AI文档处理器
 * 
 * 参考Spring AI Document的设计，处理文档向量化
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiDocumentProcessor {

    private final IAiKnowledgeService knowledgeService;
    private final AiVectorService vectorService;

    /**
     * 处理文档并向量化
     *
     * @param document 知识库文档
     * @param segments 文档分块列表
     * @return 处理结果
     */
    public DocumentProcessResult processDocument(AiKnowledgeDocumentEntity document, 
                                               List<AiKnowledgeSegmentEntity> segments) {
        log.info("开始处理文档，文档ID: {}, 分块数量: {}", document.getId(), segments.size());
        
        try {
            // 1. 获取知识库配置
            AiKnowledgeEntity knowledge = knowledgeService.getById(document.getKnowledgeId());
            if (knowledge == null) {
                throw new RuntimeException("知识库不存在: " + document.getKnowledgeId());
            }

            // 2. 创建Spring AI Document对象列表
            List<SpringAiDocument> aiDocuments = createSpringAiDocuments(document, segments);

            // 3. 向量化处理
            List<VectorizedDocument> vectorizedDocs = vectorizeDocuments(aiDocuments);

            // 4. 存储向量
            storeVectors(vectorizedDocs);

            // 5. 更新分块状态
            updateSegmentStatus(segments, "vectorized");

            DocumentProcessResult result = new DocumentProcessResult();
            result.setSuccess(true);
            result.setDocumentId(document.getId());
            result.setProcessedSegments(segments.size());
            result.setVectorizedCount(vectorizedDocs.size());
            result.setMessage("文档处理成功");

            log.info("文档处理完成，文档ID: {}, 向量化数量: {}", document.getId(), vectorizedDocs.size());
            return result;

        } catch (Exception e) {
            log.error("文档处理失败，文档ID: {}", document.getId(), e);
            
            // 更新分块状态为失败
            updateSegmentStatus(segments, "failed");
            
            DocumentProcessResult result = new DocumentProcessResult();
            result.setSuccess(false);
            result.setDocumentId(document.getId());
            result.setMessage("文档处理失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 创建Spring AI Document对象
     */
    private List<SpringAiDocument> createSpringAiDocuments(AiKnowledgeDocumentEntity document, 
                                                          List<AiKnowledgeSegmentEntity> segments) {
        List<SpringAiDocument> aiDocuments = new ArrayList<>();
        
        for (AiKnowledgeSegmentEntity segment : segments) {
            // 创建元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("knowledgeId", segment.getKnowledgeId());
            metadata.put("documentId", segment.getDocumentId());
            metadata.put("segmentId", segment.getId());
            metadata.put("documentName", document.getName());
            metadata.put("fileName", document.getFileName());
            metadata.put("fileType", document.getFileType());
            metadata.put("wordCount", segment.getWordCount());
            metadata.put("position", segment.getPosition());
            metadata.put("createTime", System.currentTimeMillis());

            // 创建Spring AI Document
            SpringAiDocument aiDoc = new SpringAiDocument();
            aiDoc.setId(generateDocumentId(segment));
            aiDoc.setContent(segment.getContent());
            aiDoc.setMetadata(metadata);
            
            aiDocuments.add(aiDoc);
        }
        
        return aiDocuments;
    }

    /**
     * 向量化文档
     */
    private List<VectorizedDocument> vectorizeDocuments(List<SpringAiDocument> documents) {
        List<VectorizedDocument> vectorizedDocs = new ArrayList<>();
        
        for (SpringAiDocument doc : documents) {
            try {
                // 生成向量
                float[] vector = vectorService.embedText(doc.getContent());
                
                VectorizedDocument vectorizedDoc = new VectorizedDocument();
                vectorizedDoc.setDocument(doc);
                vectorizedDoc.setVector(vector);
                
                vectorizedDocs.add(vectorizedDoc);
                
            } catch (Exception e) {
                log.error("文档向量化失败，文档ID: {}", doc.getId(), e);
            }
        }
        
        return vectorizedDocs;
    }

    /**
     * 存储向量到Redis
     */
    private void storeVectors(List<VectorizedDocument> vectorizedDocs) {
        for (VectorizedDocument vectorizedDoc : vectorizedDocs) {
            try {
                // 使用现有的向量存储逻辑
                vectorService.storeVector(vectorizedDoc.getDocument().getId(), 
                                        vectorizedDoc.getVector(), 
                                        vectorizedDoc.getDocument().getMetadata());
                
            } catch (Exception e) {
                log.error("向量存储失败，文档ID: {}", vectorizedDoc.getDocument().getId(), e);
            }
        }
    }

    /**
     * 更新分块状态
     */
    private void updateSegmentStatus(List<AiKnowledgeSegmentEntity> segments, String status) {
        // 这个方法应该在AiKnowledgeSegmentService中实现
        // 这里只是示例
        for (AiKnowledgeSegmentEntity segment : segments) {
            segment.setStatus(status);
            // 实际应该调用service更新
        }
    }

    /**
     * 生成文档ID
     */
    private String generateDocumentId(AiKnowledgeSegmentEntity segment) {
        return String.format("kb_%d_doc_%d_seg_%d", 
                segment.getKnowledgeId(), 
                segment.getDocumentId(), 
                segment.getId());
    }

    /**
     * Spring AI Document包装类
     */
    public static class SpringAiDocument {
        private String id;
        private String content;
        private Map<String, Object> metadata;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 向量化文档
     */
    public static class VectorizedDocument {
        private SpringAiDocument document;
        private float[] vector;

        // Getters and Setters
        public SpringAiDocument getDocument() { return document; }
        public void setDocument(SpringAiDocument document) { this.document = document; }
        public float[] getVector() { return vector; }
        public void setVector(float[] vector) { this.vector = vector; }
    }

    /**
     * 文档处理结果
     */
    public static class DocumentProcessResult {
        private boolean success;
        private Long documentId;
        private int processedSegments;
        private int vectorizedCount;
        private String message;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public Long getDocumentId() { return documentId; }
        public void setDocumentId(Long documentId) { this.documentId = documentId; }
        public int getProcessedSegments() { return processedSegments; }
        public void setProcessedSegments(int processedSegments) { this.processedSegments = processedSegments; }
        public int getVectorizedCount() { return vectorizedCount; }
        public void setVectorizedCount(int vectorizedCount) { this.vectorizedCount = vectorizedCount; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
