package com.coocaa.ad.cheese.authority.common.db.entity.sms;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @program: cheese-authority
 * @ClassName SmsSendRecord
 * @description: 短信发送记录表
 * @author: zhangbinxian
 * @create: 2025-05-08 09:46
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "sms_send_record")
public class SmsSendRecord implements Serializable {

    @Serial
    private static final long serialVersionUID = -936902248439312693L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 短信模板ID
     */
    private String templateId;

    /**
     * 接收手机号
     */
    private String receiverPhone;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 发送人
     */
    private Integer sender;

    /**
     * 发送结果，0 失败，1 成功
     */
    private Integer sendResult;

    /**
     * 错误信息
     */
    private String errorMessage;
}
