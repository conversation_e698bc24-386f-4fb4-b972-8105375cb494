package com.coocaa.ad.cheese.authority.common.tools.result;

import java.util.HashMap;

/**
 * 类ResultHeader实现描述:
 *
 * <AUTHOR>
 * @date 2021年09月18 11:13 上午
 */
public class ResultHeader extends HashMap<String,Object> {

    /** 追溯ID */
    private static final String TRACE_ID = "traceId";

    public void setTraceId(String traceId) {
        this.put(TRACE_ID, traceId);
    }

    public String getTraceId() {
        return (String) this.get(TRACE_ID);
    }
}
