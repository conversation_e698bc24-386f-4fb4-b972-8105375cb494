<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalNodeMapper">


    <select id="getInstanceNode"
            resultType="com.coocaa.ad.cheese.authority.common.db.vo.approve.InstanceNodeCommonVO">
        select n.id as id,n.rule_code as ruleCode,n.instance_code as instanceCode,n.person_id as personId,n.rank as rank
        ,n.user_id as userId,n.cancel_reason as cancelReason,n.node_status as nodeStatus
        ,n.start_time,n.end_time,n.approval_flag,t.approval_result as approvalResult,t.comment as comment
        ,n.task_name as taskName
        from approve_internal_node n left join
        approve_internal_task t on n.id = t.node_id
        where n.instance_code = #{instanceCode}
        order by n.approval_flag asc,n.rank asc
    </select>

    <select id="getInstanceNodeBatch"
            resultType="com.coocaa.ad.cheese.authority.common.db.vo.approve.InstanceNodeCommonVO">
        select n.id as id,n.rule_code as ruleCode,n.instance_code as instanceCode,n.person_id as personId,n.rank as rank
        ,n.user_id as userId,n.cancel_reason as cancelReason,n.node_status as nodeStatus
        ,n.start_time,n.end_time,n.approval_flag,t.approval_result as approvalResult,t.comment as comment
        ,n.task_name as taskName
        from approve_internal_instance d
        inner join approve_internal_node n on d.instance_code = n.instance_code
        left join approve_internal_task t on n.id = t.node_id
        where n.instance_code in
        <foreach collection="instanceCodes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        order by d.create_time asc,n.approval_flag asc,n.rank asc
    </select>
</mapper>
