package com.coocaa.ad.cheese.authority.common.db.service;

import com.coocaa.ad.cheese.authority.common.db.entity.CityGroupEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 城市组 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface ICityGroupService extends IService<CityGroupEntity> {

    int groupCount();

    CityGroupEntity queryByName(String name);

    List<CityGroupEntity> listByParentId(Integer parentId);


    List<CityGroupEntity> listEnableCityGroup();

    List<CityGroupEntity> listByIdsEnable(Set<Integer> groupIds);

    List<CityGroupEntity> listByParentIds(List<Integer> list);
}
