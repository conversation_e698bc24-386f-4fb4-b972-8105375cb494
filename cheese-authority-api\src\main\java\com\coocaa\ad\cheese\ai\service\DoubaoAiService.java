package com.coocaa.ad.cheese.ai.service;

import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionChunk;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionChunk;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 豆包AI服务
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
public class DoubaoAiService {

    @Value("${doubao.api-key}")
    private String apiKey;

    @Value("${doubao.base-url}")
    private String baseUrl;

    @Value("${doubao.model}")
    private String model;

    @Value("${doubao.embedding.model:doubao-embedding-vision-250615}")
    private String embeddingModel;

    private ArkService arkService;

    @PostConstruct
    public void init() {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            apiKey = System.getenv("ARK_API_KEY");
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.warn("豆包API Key未配置，请设置doubao.api.key配置项或ARK_API_KEY环境变量");
            return;
        }

        arkService = ArkService.builder()
                .baseUrl(baseUrl)
                .apiKey(apiKey)
                .build();

        log.info("豆包AI服务初始化完成，模型: {}", model);
    }

    /**
     * 发送聊天消息（流式，直接返回Flowable）
     *
     * @param messages 消息列表
     * @return 流式响应
     */
    public Flowable<BotChatCompletionChunk> getBotChatCompletionChunk(List<ChatMessage> messages) {
        if (arkService == null) {
            return Flowable.error(new RuntimeException("豆包AI服务未初始化，请检查API Key配置"));
        }

        try {
            log.info("构建豆包AI请求，模型: {}, 消息数量: {}", model, messages.size());

            BotChatCompletionRequest request = BotChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .maxTokens(1000)
                    .temperature(0.7)
                    .build();

            log.info("豆包AI请求构建完成");

            // 直接返回豆包的Flowable
            return arkService.streamBotChatCompletion(request);

        } catch (Exception e) {
            log.error("创建豆包AI请求失败", e);
            return Flowable.error(e);
        }
    }

    /**
     * 发送聊天消息（非流式）
     *
     * @param messages 消息列表
     * @return 响应内容
     */
    public String sendChatMessage(List<ChatMessage> messages) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            BotChatCompletionRequest request = BotChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .build();

            return arkService.createBotChatCompletion(request).getChoices().get(0).getMessage().getContent().toString();
        } catch (Exception e) {
            log.error("发送豆包AI消息失败", e);
            throw new RuntimeException("发送豆包AI消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建聊天消息
     *
     * @param role    角色
     * @param content 内容
     * @return 聊天消息
     */
    public ChatMessage buildChatMessage(String role, String content) {
        ChatMessageRole messageRole = "user".equals(role) ? ChatMessageRole.USER : ChatMessageRole.ASSISTANT;
        return ChatMessage.builder()
                .role(messageRole)
                .content(content)
                .build();
    }

    /**
     * 构建消息列表
     *
     * @param userMessage     用户消息
     * @param contextMessages 上下文消息（可选）
     * @return 消息列表
     */
    public List<ChatMessage> buildMessageList(String userMessage, List<ChatMessage> contextMessages) {
        List<ChatMessage> messages = new ArrayList<>();

        // 添加上下文消息
        if (contextMessages != null && !contextMessages.isEmpty()) {
            messages.addAll(contextMessages);
        }

        // 添加当前用户消息
        messages.add(buildChatMessage("user", userMessage));

        return messages;
    }

    /**
     * 构建消息列表（包含角色提示词）
     *
     * @param userMessage     用户消息
     * @param contextMessages 上下文消息（可选）
     * @param rolePrompt      角色提示词（可选）
     * @return 消息列表
     */
    public List<ChatMessage> buildMessageList(String userMessage, List<ChatMessage> contextMessages, String rolePrompt) {
        List<ChatMessage> messages = new ArrayList<>();

        // 添加角色提示词作为系统消息（如果存在）
        if (rolePrompt != null && !rolePrompt.trim().isEmpty()) {
            messages.add(buildChatMessage("system", rolePrompt));
            log.info("添加角色提示词: {}", rolePrompt);
        }

        // 添加上下文消息
        if (contextMessages != null && !contextMessages.isEmpty()) {
            messages.addAll(contextMessages);
        }

        // 添加当前用户消息
        messages.add(buildChatMessage("user", userMessage));

        return messages;
    }

    /**
     * 发送聊天消息（流式，支持动态模型配置）
     *
     * @param messages 消息列表
     * @param modelKey 模型标识符
     * @param apiKey API密钥（可选，为空时使用默认配置）
     * @param baseUrl 基础URL（可选，为空时使用默认配置）
     * @return 流式响应
     */
    public Flowable<ChatCompletionChunk> getChatCompletionChunk(List<ChatMessage> messages, String modelKey, String apiKey, String baseUrl) {
        try {
            log.info("构建豆包AI请求，模型: {}, 消息数量: {}", modelKey, messages.size());

            // 动态创建ArkService实例
            ArkService dynamicArkService = ArkService.builder()
                    .baseUrl(baseUrl != null ? baseUrl : this.baseUrl)
                    .apiKey(apiKey != null ? apiKey : this.apiKey)
                    .build();

            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(modelKey)
                    .messages(messages)
                    .maxTokens(1000)
                    .temperature(0.7)
                    .build();

            log.info("豆包AI请求构建完成，使用模型: {}", modelKey);

            // 直接返回豆包的Flowable
            return dynamicArkService.streamChatCompletion(request);

        } catch (Exception e) {
            log.error("豆包AI请求失败，模型: {}", modelKey, e);
            return Flowable.error(e);
        }
    }

    /**
     * 发送聊天消息（流式，使用模型配置）
     *
     * @param messages 消息列表
     * @param modelKey 模型标识符
     * @return 流式响应
     */
    public Flowable<ChatCompletionChunk> getChatCompletionChunk(List<ChatMessage> messages, String modelKey) {
        return getChatCompletionChunk(messages, modelKey, null, null);
    }

    // ==================== 图像向量化功能 ====================

    /**
     * 文本向量化
     *
     * @param text 文本内容
     * @return 向量数组
     */
    public float[] embedText(String text) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            log.info("豆包文本向量化，文本长度: {}", text.length());

            // 构建多模态嵌入请求
            List<Object> inputs = new ArrayList<>();
            inputs.add(createTextInput(text));

            return callMultimodalEmbedding(inputs);

        } catch (Exception e) {
            log.error("豆包文本向量化失败", e);
            throw new RuntimeException("文本向量化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 图像URL向量化
     *
     * @param imageUrl 图像URL
     * @return 向量数组
     */
    public float[] embedImageUrl(String imageUrl) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            log.info("豆包图像URL向量化: {}", imageUrl);

            // 构建多模态嵌入请求
            List<Object> inputs = new ArrayList<>();
            inputs.add(createImageUrlInput(imageUrl));

            return callMultimodalEmbedding(inputs);

        } catch (Exception e) {
            log.error("豆包图像URL向量化失败", e);
            throw new RuntimeException("图像URL向量化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 图文混合向量化
     *
     * @param text     文本内容
     * @param imageUrl 图像URL
     * @return 向量数组
     */
    public float[] embedTextAndImage(String text, String imageUrl) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            log.info("豆包图文混合向量化，文本长度: {}, 图像URL: {}", text.length(), imageUrl);

            // 构建多模态嵌入请求
            List<Object> inputs = new ArrayList<>();
            inputs.add(createTextInput(text));
            inputs.add(createImageUrlInput(imageUrl));

            return callMultimodalEmbedding(inputs);

        } catch (Exception e) {
            log.error("豆包图文混合向量化失败", e);
            throw new RuntimeException("图文混合向量化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 视频URL向量化
     *
     * @param videoUrl 视频URL
     * @return 向量数组
     */
    public float[] embedVideoUrl(String videoUrl) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            log.info("豆包视频URL向量化: {}", videoUrl);

            // 构建多模态嵌入请求
            List<Object> inputs = new ArrayList<>();
            inputs.add(createVideoUrlInput(videoUrl));

            return callMultimodalEmbedding(inputs);

        } catch (Exception e) {
            log.error("豆包视频URL向量化失败", e);
            throw new RuntimeException("视频URL向量化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 多模态混合向量化
     *
     * @param text     文本内容（可选）
     * @param imageUrl 图像URL（可选）
     * @param videoUrl 视频URL（可选）
     * @return 向量数组
     */
    public float[] embedMultimodal(String text, String imageUrl, String videoUrl) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            log.info("豆包多模态向量化，文本: {}, 图像: {}, 视频: {}",
                    text != null, imageUrl != null, videoUrl != null);

            // 构建多模态嵌入请求
            List<Object> inputs = new ArrayList<>();

            if (text != null && !text.trim().isEmpty()) {
                inputs.add(createTextInput(text));
            }
            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                inputs.add(createImageUrlInput(imageUrl));
            }
            if (videoUrl != null && !videoUrl.trim().isEmpty()) {
                inputs.add(createVideoUrlInput(videoUrl));
            }

            if (inputs.isEmpty()) {
                throw new IllegalArgumentException("至少需要提供一种模态的输入");
            }

            return callMultimodalEmbedding(inputs);

        } catch (Exception e) {
            log.error("豆包多模态向量化失败", e);
            throw new RuntimeException("多模态向量化失败: " + e.getMessage(), e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 调用多模态嵌入API
     */
    private float[] callMultimodalEmbedding(List<Object> inputs) {
        try {
            // 注意：这里需要根据实际的豆包SDK API来实现
            // 目前豆包Java SDK可能还没有多模态嵌入的直接支持
            // 这里提供一个框架，实际实现需要根据SDK更新

            log.warn("豆包多模态嵌入API暂未完全集成，返回模拟向量");

            // 生成模拟向量（3072维，对应豆包多模态模型）
            return generateMockVector(3072, inputs.toString());

        } catch (Exception e) {
            log.error("调用豆包多模态嵌入API失败", e);
            throw new RuntimeException("多模态嵌入API调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建文本输入
     */
    private Object createTextInput(String text) {
        // 根据豆包API文档格式创建文本输入
        return new Object() {
            public String type = "text";
            public String text_content = text;
        };
    }

    /**
     * 创建图像URL输入
     */
    private Object createImageUrlInput(String imageUrl) {
        // 根据豆包API文档格式创建图像URL输入
        return new Object() {
            public String type = "image_url";
            public Object image_url = new Object() {
                public String url = imageUrl;
            };
        };
    }

    /**
     * 创建视频URL输入
     */
    private Object createVideoUrlInput(String videoUrl) {
        // 根据豆包API文档格式创建视频URL输入
        return new Object() {
            public String type = "video_url";
            public Object video_url = new Object() {
                public String url = videoUrl;
            };
        };
    }

    /**
     * 生成模拟向量（用于演示）
     */
    private float[] generateMockVector(int dimension, String seed) {
        int hash = seed.hashCode();
        java.util.Random random = new java.util.Random(hash);

        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float) (random.nextGaussian() * 0.1);
        }

        // 归一化
        float norm = 0.0f;
        for (float v : vector) {
            norm += v * v;
        }
        norm = (float) Math.sqrt(norm);

        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }

        return vector;
    }

    /**
     * 获取嵌入模型名称
     */
    public String getEmbeddingModel() {
        return embeddingModel;
    }

    /**
     * 检查是否支持多模态
     */
    public boolean isMultimodalSupported() {
        // 检查模型版本是否支持多模态
        return embeddingModel.contains("vision") || embeddingModel.contains("250615");
    }

    /**
     * 获取支持的模态类型
     */
    public List<String> getSupportedModalities() {
        List<String> modalities = new ArrayList<>();
        modalities.add("text");

        if (isMultimodalSupported()) {
            modalities.add("image_url");

            // 检查是否支持视频（仅特定版本支持）
            if (embeddingModel.contains("250615") || embeddingModel.compareTo("doubao-embedding-vision-250615") >= 0) {
                modalities.add("video_url");
            }
        }

        return modalities;
    }

    /**
     * 获取ArkService实例（用于Spring AI集成）
     */
    public ArkService getArkService() {
        return arkService;
    }

}
