package com.coocaa.ad.cheese.ai.service;

import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionChunk;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessage;
import com.volcengine.ark.runtime.model.completion.chat.ChatMessageRole;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 豆包AI服务
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
public class DoubaoAiService {

    @Value("${doubao.api-key}")
    private String apiKey;

    @Value("${doubao.base-url}")
    private String baseUrl;

    @Value("${doubao.model}")
    private String model;

    private ArkService arkService;

    @PostConstruct
    public void init() {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            apiKey = System.getenv("ARK_API_KEY");
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.warn("豆包API Key未配置，请设置doubao.api.key配置项或ARK_API_KEY环境变量");
            return;
        }

        arkService = ArkService.builder()
                .baseUrl(baseUrl)
                .apiKey(apiKey)
                .build();

        log.info("豆包AI服务初始化完成，模型: {}", model);
    }

    /**
     * 发送聊天消息（流式，直接返回Flowable）
     *
     * @param messages 消息列表
     * @return 流式响应
     */
    public Flowable<BotChatCompletionChunk> getBotChatCompletionChunk(List<ChatMessage> messages) {
        if (arkService == null) {
            return Flowable.error(new RuntimeException("豆包AI服务未初始化，请检查API Key配置"));
        }

        try {
            log.info("构建豆包AI请求，模型: {}, 消息数量: {}", model, messages.size());

            BotChatCompletionRequest request = BotChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .maxTokens(1000)
                    .temperature(0.7)
                    .build();

            log.info("豆包AI请求构建完成");

            // 直接返回豆包的Flowable
            return arkService.streamBotChatCompletion(request);

        } catch (Exception e) {
            log.error("创建豆包AI请求失败", e);
            return Flowable.error(e);
        }
    }

    /**
     * 发送聊天消息（非流式）
     *
     * @param messages 消息列表
     * @return 响应内容
     */
    public String sendChatMessage(List<ChatMessage> messages) {
        if (arkService == null) {
            throw new RuntimeException("豆包AI服务未初始化，请检查API Key配置");
        }

        try {
            BotChatCompletionRequest request = BotChatCompletionRequest.builder()
                    .model(model)
                    .messages(messages)
                    .build();

            return arkService.createBotChatCompletion(request).getChoices().get(0).getMessage().getContent().toString();
        } catch (Exception e) {
            log.error("发送豆包AI消息失败", e);
            throw new RuntimeException("发送豆包AI消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建聊天消息
     *
     * @param role    角色
     * @param content 内容
     * @return 聊天消息
     */
    public ChatMessage buildChatMessage(String role, String content) {
        ChatMessageRole messageRole = "user".equals(role) ? ChatMessageRole.USER : ChatMessageRole.ASSISTANT;
        return ChatMessage.builder()
                .role(messageRole)
                .content(content)
                .build();
    }

    /**
     * 构建消息列表
     *
     * @param userMessage     用户消息
     * @param contextMessages 上下文消息（可选）
     * @return 消息列表
     */
    public List<ChatMessage> buildMessageList(String userMessage, List<ChatMessage> contextMessages) {
        List<ChatMessage> messages = new ArrayList<>();

        // 添加上下文消息
        if (contextMessages != null && !contextMessages.isEmpty()) {
            messages.addAll(contextMessages);
        }

        // 添加当前用户消息
        messages.add(buildChatMessage("user", userMessage));

        return messages;
    }

    /**
     * 构建消息列表（包含角色提示词）
     *
     * @param userMessage     用户消息
     * @param contextMessages 上下文消息（可选）
     * @param rolePrompt      角色提示词（可选）
     * @return 消息列表
     */
    public List<ChatMessage> buildMessageList(String userMessage, List<ChatMessage> contextMessages, String rolePrompt) {
        List<ChatMessage> messages = new ArrayList<>();

        // 添加角色提示词作为系统消息（如果存在）
        if (rolePrompt != null && !rolePrompt.trim().isEmpty()) {
            messages.add(buildChatMessage("system", rolePrompt));
            log.info("添加角色提示词: {}", rolePrompt);
        }

        // 添加上下文消息
        if (contextMessages != null && !contextMessages.isEmpty()) {
            messages.addAll(contextMessages);
        }

        // 添加当前用户消息
        messages.add(buildChatMessage("user", userMessage));

        return messages;
    }
}
