package com.coocaa.ad.cheese.authority.common.tools.cos;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.*;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoRequest;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoResponse;
import com.qcloud.cos.transfer.Copy;
import com.qcloud.cos.transfer.Download;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.Upload;
import com.qcloud.cos.model.ciModel.job.FileProcessJobResponse;
import com.qcloud.cos.model.ciModel.job.FileProcessRequest;
import com.qcloud.cos.model.ciModel.job.FileCompressConfig;
import com.qcloud.cos.model.ciModel.job.FileProcessJobType;
import com.qcloud.cos.model.ciModel.snapshot.SnapshotRequest;
import com.qcloud.cos.model.ciModel.snapshot.SnapshotResponse;
import com.qcloud.cos.model.ciModel.job.FileHashCodeConfig;
import com.qcloud.cos.model.ciModel.job.FileHashCodeResult;

import java.io.File;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ObjectUtils {

    public static String suffix;

    private static COSClient cosClient;

    @Autowired
    public void setCosClient(COSClient cosClient) {
        ObjectUtils.cosClient = cosClient;
    }

    @Value("${app.code-name:default}")
    public void setSuffix(String suffix) {
        ObjectUtils.suffix = suffix;
    }

    public static String region;

    @Value("${cos.region:default}")
    public void setRegion(String region) {
        ObjectUtils.region = region;
    }

    public static String bucketName;
    
    @Value("${cos.bucket-name:default}")
    public void setBucketName(String bucketName) {
        ObjectUtils.bucketName = bucketName;
    }

    public static String domain;

    @Value("${cos.domain:default}")
    public void setDomain(String domain) {
        ObjectUtils.domain = domain;
    }

    public static String getCosFileName(String feature, String filename) {
        // 字符串格式为：ssp/feature/yyyy/MM/dd/filename
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return suffix + "/" + feature + "/" + date + "/" + filename;
    }
    
    /**
     * 上传文件到COS
     */
    public static PutObjectResult uploadFile(String key, File localFile) {
        try {
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, localFile);
            return cosClient.putObject(putObjectRequest);
        } catch (Exception e) {
            throw new RuntimeException("Upload file failed: " + e.getMessage(), e);
        }
    }

    /**
     * 使用TransferManager上传大文件
     */
    public static Upload uploadLargeFile(String key, File localFile) {
        TransferManager transferManager = new TransferManager(cosClient);
        try {
            return transferManager.upload(bucketName, key, localFile);
        } finally {
            transferManager.shutdownNow(false);
        }
    }

    /**
     * 下载COS对象到本地文件
     */
    public static Download downloadFile(String key, File localFile) {
        key = extractObjectKey(key);
        TransferManager transferManager = new TransferManager(cosClient);
        try {
            return transferManager.download(bucketName, key, localFile);
        } catch (Exception e) {
            throw new RuntimeException("Download file failed: " + e.getMessage(), e);
        }
    }

    /**
     * 复制COS对象
     */
    public static Copy copyObject(String sourceKey, String destinationKey) {
        sourceKey = extractObjectKey(sourceKey);
        TransferManager transferManager = new TransferManager(cosClient);
        try {
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(
                bucketName, sourceKey, bucketName, destinationKey);
            return transferManager.copy(copyObjectRequest);
        } finally {
            transferManager.shutdownNow(false);
        }
    }

    /**
     * 删除COS对象
     */
    public static void deleteObject(String key) {
        key = extractObjectKey(key);
        try {
            cosClient.deleteObject(bucketName, key);
        } catch (Exception e) {
            throw new RuntimeException("Delete object failed: " + e.getMessage(), e);
        }
    }

    /**
     * 批量删除COS对象
     */
    public static DeleteObjectsResult deleteObjects(List<String> keys) {
        // 处理keys中的URL，移除前缀
        List<String> processedKeys = keys.stream()
                .map(ObjectUtils::extractObjectKey)
                .collect(Collectors.toList());
        keys = processedKeys;
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(bucketName);
        List<DeleteObjectsRequest.KeyVersion> keyVersions = new ArrayList<>();
        for (String key : keys) {
            keyVersions.add(new DeleteObjectsRequest.KeyVersion(key));
        }
        deleteObjectsRequest.setKeys(keyVersions);
        return cosClient.deleteObjects(deleteObjectsRequest);
    }

    /**
     * 列出COS对象
     */
    public static ObjectListing listObjects(String prefix) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest();
        listObjectsRequest.setBucketName(bucketName);
        if (prefix != null) {
            listObjectsRequest.setPrefix(prefix);
        }
        return cosClient.listObjects(listObjectsRequest);
    }

    /**
     * 判断对象是否存在
     */
    public static boolean doesObjectExist(String key) {
        key = extractObjectKey(key);
        try {
            return cosClient.doesObjectExist(bucketName, key);
        } catch (Exception e) {
            throw new RuntimeException("Check object existence failed: " + e.getMessage(), e);
        }
    }

    /**
     * 获取对象元数据
     */
    public static ObjectMetadata getObjectMetadata(String key) {
        key = extractObjectKey(key);
        try {
            return cosClient.getObjectMetadata(bucketName, key);
        } catch (Exception e) {
            throw new RuntimeException("Get object metadata failed: " + e.getMessage(), e);
        }
    }

    /**
     * 获取对象访问URL
     */
    public static String getAccessUrl(String feature, String filename) {
        if (StringUtils.isNotBlank(domain) && domain.equals("default")) {
            return String.format("https://%s.cos.%s.myqcloud.com/%s", ObjectUtils.bucketName, region, ObjectUtils.getCosFileName(feature, filename));
        }
        return String.format("%s/%s", domain, ObjectUtils.getCosFileName(feature, filename));
    }

    /**
     * 获取对象的媒体信息
     */
    public static MediaInfoResponse getMediaInfo(String key) {
        key = extractObjectKey(key);
        MediaInfoRequest mediaInfoRequest = new MediaInfoRequest();
        mediaInfoRequest.setBucketName(bucketName);
        mediaInfoRequest.getInput().setObject(key);
        return cosClient.generateMediainfo(mediaInfoRequest);
    }

    /**
     * 从URL或完整路径中提取对象键值
     * 示例:
     * http://www.cshimedia.com/ssp/point/2024/11/13/test.xml -> ssp/point/2024/11/13/test.xml
     * 
     * @param path URL或完整路径
     * @return 提取的对象键值
     */
    public static String extractObjectKey(String path) {
        if (path == null || path.trim().isEmpty()) {
            return path;
        }
        
        // 如果是HTTP URL，提取路径部分
        if (path.toLowerCase().startsWith("http")) {
            try {
                // 找到第三个斜杠的位置（http://之后的第一个斜杠）
                int startIndex = path.indexOf("/", path.indexOf("/") + 2);
                if (startIndex != -1) {
                    return path.substring(startIndex + 1);
                }
            } catch (Exception e) {
                // 果解析失败，返回原始路径
                return path;
            }
        }
        return path;
    }

    /**
     * 提交文件压缩任务
     * @param sourceKeys 需要压缩的文件key列表，支持带HTTP的全路径
     * @param targetKey 压缩后的文件存储key
     * @param format 压缩格式，支持zip、tar、tar.gz
     * @return 压缩任务的响应结果
     */
    public static FileProcessJobResponse compressFiles(List<String> sourceKeys, String targetKey, 
        String format) {        
        String flatten = "1";
        try {
            // 1. 创建压缩任务请求
            FileProcessRequest request = new FileProcessRequest();
            request.setBucketName(bucketName);
            request.setTag(FileProcessJobType.FileCompress);

            // 2. 设置压缩配置
            FileCompressConfig compressConfig = request.getOperation().getFileCompressConfig();
            compressConfig.setFormat(format);  // 设置压缩格式
            compressConfig.setFlatten(flatten); // 设置是否扁平化目录
            
            // 处理源文件路径，移除HTTP前缀
            List<String> processedKeys = sourceKeys.stream()
                .map(ObjectUtils::extractObjectKey)
                .collect(Collectors.toList());
            compressConfig.getKey().addAll(processedKeys); // 添加要压缩的文件列表

            // 3. 设置输出配置
            com.qcloud.cos.model.ciModel.common.MediaOutputObject output = request.getOperation().getOutput();
            output.setBucket(bucketName);
            output.setRegion(region);
            output.setObject(targetKey);

            // 4. 提交压缩任务
            return cosClient.createFileProcessJob(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to compress files: " + e.getMessage(), e);
        }
    }

    /**
     * 查询文件处理任务结果
     * @param jobId 任务ID
     * @return 任务的详细信息
     */
    public static FileProcessJobResponse queryFileProcessJobResult(String jobId) {
        try {
            FileProcessRequest request = new FileProcessRequest();
            request.setBucketName(bucketName);
            request.setJobId(jobId);
            
            return cosClient.describeFileProcessJob(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to query file process job result: " + e.getMessage(), e);
        }
    }

    /**
     * 获取视频某个时间点的截图
     * @param sourceKey 源视频文件的key
     * @param targetKey 截图保存的key
     * @param time 截取视频第几秒的画面
     * @param width 截图宽度，0表示按视频比例自适应
     * @param height 截图高度，0表示按视频比例自适应
     * @param format 截图格式，支持jpg和png，默认jpg
     * @return 截图任务的响应结果
     */
    public static SnapshotResponse generateVideoSnapshot(String sourceKey, String targetKey, 
            float time, int width, int height, String format) {
        try {
            // 1. 创建截图请求对象
            SnapshotRequest request = new SnapshotRequest();
            request.setBucketName(bucketName);

            // 2. 设置输入视频信息
            sourceKey = extractObjectKey(sourceKey);
            request.getInput().setObject(sourceKey);

            // 3. 设置输出信息
            request.getOutput().setBucket(bucketName);
            request.getOutput().setRegion(region);
            request.getOutput().setObject(targetKey);

            // 4. 设置截图参数
            request.setTime(String.valueOf(time));  // 设置截取时间点
            if (width > 0) {
                request.setWidth(String.valueOf(width));  // 设置截图宽度
            }
            if (height > 0) {
                request.setHeight(String.valueOf(height));  // 设置截图高度
            }
            if (format != null && !format.isEmpty()) {
                request.setFormat(format);  // 设置截图格式
            }
            
            // 设置截帧方式为精确帧
            request.setMode("exactframe");
            // 设置自动旋转
            request.setRotate("auto");

            // 5. 调用接口获取截图
            return cosClient.generateSnapshot(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate video snapshot: " + e.getMessage(), e);
        }
    }

    /**
     * 获取视频某个时间点的截图（使用默认参数）
     * @param sourceKey 源视频文件的key
     * @param targetKey 截图保存的key
     * @param time 截取视频第几秒的画面
     * @return 截图任务的响应结果
     */
    public static SnapshotResponse generateVideoSnapshot(String sourceKey, String targetKey, float time) {
        return generateVideoSnapshot(sourceKey, targetKey, time, 0, 0, "jpg");
    }

    /**
     * 提交文件哈希值计算任务
     * @param sourceKey 需要计算哈希值的文件key，支持全路径（如：http://xxx.com/path/to/file.txt）
     * @param hashType 哈希算法类型，支持：MD5、SHA1、SHA256
     * @param addToHeader 是否将计算得到的哈希值添加至文件自定义header
     * @return 哈希值计算任务的响应结果
     */
    public static FileProcessJobResponse calculateFileHash(String sourceKey, String hashType, 
            boolean addToHeader) {
        try {
            // 1. 创建任务请求对象
            FileProcessRequest request = new FileProcessRequest();
            request.setBucketName(bucketName);
            request.setTag(FileProcessJobType.FileHashCode);

            // 2. 设置输入文件，处理全路径
            sourceKey = extractObjectKey(sourceKey);
            request.getInput().setObject(sourceKey);

            // 3. 设置哈希值计算配置
            FileHashCodeConfig hashCodeConfig = request.getOperation().getFileHashCodeConfig();
            hashCodeConfig.setType(hashType);
            hashCodeConfig.setAddToHeader(String.valueOf(addToHeader));

            // 4. 提交哈希值计算任务
            return cosClient.createFileProcessJob(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate file hash: " + e.getMessage(), e);
        }
    }

    /**
     * 计算文件哈希值（同步方法）
     * @param sourceKey 需要计算哈希值的文件key
     * @param hashType 哈希算法类型，支持：MD5、SHA1、SHA256
     * @param maxWaitSeconds 最大等待时间（秒）
     * @return 文件的哈希值，如果超时则返回null
     */
    public static String calculateFileHashSync(String sourceKey, String hashType, int maxWaitSeconds) {
        hashType = hashType.toUpperCase();
        try {
            // 1. 提交哈希值计算任务
            FileProcessJobResponse response = calculateFileHash(sourceKey, hashType, false);
            String jobId = response.getJobDetail().getJobId();

            // 2. 轮询等待结果
            long startTime = System.currentTimeMillis();
            while (System.currentTimeMillis() - startTime < maxWaitSeconds * 1000L) {
                FileProcessJobResponse result = queryFileProcessJobResult(jobId);  // 使用统一的查询方法
                String state = result.getJobDetail().getState();
                
                if ("Success".equals(state)) {
                    // 任务成功完成，需要根据不同的hashType获取哈希值
                    FileHashCodeResult hashCodeResult = result.getJobDetail().getOperation().getFileHashCodeResult();
                    if ("MD5".equals(hashType)) {
                        return hashCodeResult.getMd5();
                    } else if ("SHA1".equals(hashType)) {
                        return hashCodeResult.getSha1();
                    } else if ("SHA256".equals(hashType)) {
                        return hashCodeResult.getSha256();
                    }
                } else if ("Failed".equals(state)) {
                    // 任务失败
                    throw new RuntimeException("文件哈希计算失败: " + 
                        result.getJobDetail().getOperation().getUserData());
                }
                
                // 等待1秒后继续查询
                Thread.sleep(1000);
            }
            
            // 超时返回null
            return null;
        } catch (Exception e) {
            throw new RuntimeException("文件哈希计算失败: " + e.getMessage(), e);
        }
    }

}
