package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.ai.bean.AiUserParam;
import com.coocaa.ad.cheese.ai.common.db.bean.AiUserDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiUserEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiUserService;
import com.coocaa.ad.cheese.ai.convert.AiUserConvert;
import com.coocaa.ad.cheese.ai.vo.AiUserInfoReqVO;
import com.coocaa.ad.cheese.ai.vo.AiUserInfoVO;
import com.coocaa.ad.cheese.ai.vo.AiUserVO;
import com.coocaa.ad.cheese.authority.common.tools.result.PageRequestVo;
import com.coocaa.ad.cheese.authority.common.tools.result.PageResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * AI Chat 对话 业务实现类
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiUserService {
    private final IAiUserService aiUserService;

    /**
     * 分页查询AI Chat 对话列表
     *
     * @param pageRequest 分页请求参数
     * @return 分页结果
     */
    public PageResponseVo<AiUserVO> pageList(PageRequestVo<AiUserParam> pageRequest) {
        // 转换查询参数
        AiUserDTO aiUser = AiUserConvert.INSTANCE.toDto(pageRequest.getQuery());
        // 分页查询
        IPage<AiUserEntity> pageAiUsers = aiUserService.pageList(getPage(pageRequest), aiUser);
        PageResponseVo<AiUserVO> pageResponse = AiUserConvert.INSTANCE.toPageResponse(pageAiUsers);
        // 没查到数据，直接返回
        if (CollectionUtils.isEmpty(pageAiUsers.getRecords())) {
            return pageResponse;
        }
        // 翻译字段
        return pageResponse;
    }

    /**
     * AI Chat 对话详情
     *
     * @param id 主键ID
     * @return AI Chat 对话详情
     */
    public AiUserVO getDetail(Long id) {
        return Optional.ofNullable(aiUserService.getById(id))
                .map(aiUser -> {
                    AiUserVO vo = AiUserConvert.INSTANCE.toVo(aiUser);
                    return vo;
                }).orElse(null);
    }

    /**
     * AI Chat 对话新增
     *
     * @param param 实体参数
     * @return 操作结果
     */
    public boolean create(AiUserParam param) {
        AiUserEntity entity = AiUserConvert.INSTANCE.toEntity(param);
        return aiUserService.save(entity);
    }

    /*
     * AI Chat 对话修改
     * @param Integer id
     * @param param 实体参数
     * @return 操作结果
     */
    public boolean update(Long id, AiUserParam param) {
        AiUserEntity entity = AiUserConvert.INSTANCE.toEntity(param);
        entity.setId(id);
        return aiUserService.updateById(entity);
    }

    /**
     * AI Chat 对话删除
     *
     * @param id 主键ID
     * @return 删除结果
     */
    public boolean deleteById(Long id) {
        return aiUserService.lambdaUpdate()
                .set(AiUserEntity::getDeleteFlag, 1)
                .eq(AiUserEntity::getId, id)
                .update();
    }

    /**
     * 查找或创建用户
     *
     * @param userInfoReq 用户信息请求
     * @return 用户信息VO
     */
    public AiUserInfoVO findOrCreateUser(AiUserInfoReqVO userInfoReq) {
        log.info("查找或创建用户，OpenID: {}", userInfoReq.getOpenId());

        // 根据OpenID查找现有用户
        LambdaQueryWrapper<AiUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiUserEntity::getOpenId, userInfoReq.getOpenId())
               .eq(AiUserEntity::getDeleteFlag, 0)
               .orderByDesc(AiUserEntity::getCreateTime)
               .last("LIMIT 1");

        AiUserEntity existingUser = aiUserService.getOne(wrapper);

        if (existingUser != null) {
            log.info("找到现有用户，用户ID: {}, OpenID: {}", existingUser.getId(), existingUser.getOpenId());
            AiUserVO userVO = getDetail(existingUser.getId());
            return convertToUserInfoVO(userVO);
        }

        // 创建新用户
        AiUserParam newUserParam = new AiUserParam();
        newUserParam.setOpenId(userInfoReq.getOpenId());
        newUserParam.setName(userInfoReq.getNickName());
        newUserParam.setAvatar(userInfoReq.getAvatarUrl());
        newUserParam.setMobile(""); // 默认为空
        newUserParam.setCreateTime(LocalDateTime.now());
        newUserParam.setCreator(0);
        newUserParam.setUpdateTime(LocalDateTime.now());
        newUserParam.setOperator(0);
        newUserParam.setDeleteFlag(0);

        boolean created = create(newUserParam);
        if (!created) {
            throw new RuntimeException("创建用户失败");
        }

        // 重新查询创建的用户
        LambdaQueryWrapper<AiUserEntity> newWrapper = new LambdaQueryWrapper<>();
        newWrapper.eq(AiUserEntity::getOpenId, userInfoReq.getOpenId())
                  .eq(AiUserEntity::getDeleteFlag, 0)
                  .orderByDesc(AiUserEntity::getCreateTime)
                  .last("LIMIT 1");

        AiUserEntity newUser = aiUserService.getOne(newWrapper);
        log.info("创建新用户成功，用户ID: {}, OpenID: {}", newUser.getId(), newUser.getOpenId());

        AiUserVO userVO = getDetail(newUser.getId());
        return convertToUserInfoVO(userVO);
    }

    /**
     * 将AiUserVO转换为AiUserInfoVO
     *
     * @param userVO 用户VO
     * @return 用户信息VO
     */
    private AiUserInfoVO convertToUserInfoVO(AiUserVO userVO) {
        return AiUserInfoVO.builder()
                .id(userVO.getId())
                .openId(userVO.getOpenId())
                .nickName(userVO.getName())
                .avatarUrl(userVO.getAvatar())
                .createTime(userVO.getCreateTime())
                .updateTime(userVO.getUpdateTime())
                .build();
    }

    /**
     * 获取分页对象
     */
    private Page<AiUserEntity> getPage(PageRequestVo<?> pageRequest) {
        return new Page<>(
                Optional.ofNullable(pageRequest.getCurrentPage()).orElse(1L),
                Optional.ofNullable(pageRequest.getPageSize()).orElse(10)
        );
    }
}