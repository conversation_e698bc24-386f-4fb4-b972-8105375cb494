package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AI 对话创建 Response VO
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Schema(description = "管理后台 - AI 对话创建 Response VO")
@Data
public class AiConversationCreateRespVO {

    @Schema(description = "对话ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long conversationId;

    @Schema(description = "对话标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "新的对话")
    private String title;

    @Schema(description = "角色ID", example = "1")
    private Long roleId;
}
