
package com.coocaa.ad.cheese.ai.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI Chat 消息
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Data
@Accessors(chain = true)
public class AiChatDTO implements Serializable {

    /**
     * 编号，作为每条聊天记录的唯一标识符
     */
    private Long id;

    /**
     * 对话编号
     */
    private Long conversationId;

    /**
     * 回复编号
     */
    private Long replyId;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 聊天内容
     */
    private String content;

    /**
     * 是否携带上下文
     */
    private Integer userContext;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 删除标记 0正常 1删除
     */
    private Integer deleteFlag;
}