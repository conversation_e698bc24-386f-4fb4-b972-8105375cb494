
package com.coocaa.ad.cheese.ai.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI Chat 对话
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Data
@Accessors(chain = true)
public class AiUserDTO implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String openId;

    /**
     * 名称
     */
    private String name;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 删除标记 0正常 1删除
     */
    private Integer deleteFlag;
}