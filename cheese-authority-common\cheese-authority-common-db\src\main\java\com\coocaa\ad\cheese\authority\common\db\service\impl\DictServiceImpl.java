package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.ad.cheese.authority.common.db.entity.DictEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.DictMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IDictService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 字典 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class DictServiceImpl extends ServiceImpl<DictMapper, DictEntity> implements IDictService {
    @Autowired
    private DictMapper dictMapper;

    @Override
    public Integer getFirstLevelMaxCode() {
        return dictMapper.getFirstLevelMaxCode();
    }

    @Override
    public DictEntity queryByName(String name) {
        LambdaQueryWrapper<DictEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DictEntity::getName, name);

        return getOne(lambdaQueryWrapper);
    }

    @Override
    public List<DictEntity> listFirstLevelsRank(String name) {
        QueryWrapper<DictEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("parent_code", "0")
                .like(StringUtils.isNotBlank(name), "name", name);
        wrapper.orderByAsc("code+0");
        return list(wrapper);
    }

    @Override
    @Deprecated
    public List<DictEntity> listSecondByParent(Integer parentId, String name) {
        // For backward compatibility, call the new implementation
        DictEntity parent = getById(parentId);
        if (parent == null) {
            return new ArrayList<>();
        }
        return listSecondByParentCodeAndName(parent.getCode(), name);
    }

    @Override
    @Deprecated
    public DictEntity querySecondLevel(String name, Integer parentId) {
        // For backward compatibility, call the new implementation
        DictEntity parent = getById(parentId);
        if (parent == null) {
            return null;
        }
        return querySecondLevelByCode(name, parent.getCode());
    }

    @Override
    @Deprecated
    public int countSecondByParentId(Integer parentId) {
        // For backward compatibility, call the new implementation
        DictEntity parent = getById(parentId);
        if (parent == null) {
            return 0;
        }
        return countSecondByParentCode(parent.getCode());
    }

    @Override
    public List<DictEntity> listSecondByParentCode(String code) {
        return listSecondByParentEnable(code);
    }
    
    @Override
    public List<DictEntity> listSecondByParentCodeAndName(String parentCode, String name) {
        return lambdaQuery().eq(DictEntity::getParentCode, parentCode)
                .like(StringUtils.isNotBlank(name), DictEntity::getName, name)
                .orderByAsc(DictEntity::getRank)
                .list();
    }
    
    @Override
    public DictEntity querySecondLevelByCode(String name, String parentCode) {
        return lambdaQuery().eq(DictEntity::getName, name)
                .eq(DictEntity::getParentCode, parentCode).one();
    }
    
    @Override
    public int countSecondByParentCode(String parentCode) {
        return Math.toIntExact(lambdaQuery().eq(DictEntity::getParentCode, parentCode).count());
    }

    private List<DictEntity> listSecondByParentEnable(String parentCode) {
        return lambdaQuery().eq(DictEntity::getParentCode, parentCode)
                .eq(DictEntity::getStatus, true)
                .orderByAsc(DictEntity::getRank)
                .list();
    }

    @Override
    public List<DictEntity> listByCodes(List<String> codes) {
        if(CollectionUtils.isNotEmpty(codes)){
            return lambdaQuery().in(DictEntity::getCode, codes)
                    .list();
        }
        return new ArrayList<>();
    }

    @Override
    public DictEntity getBySecondName(String name, String parentCode) {
        return lambdaQuery().eq(DictEntity::getParentCode, parentCode)
                .eq(DictEntity::getName, name).one();
    }
}
