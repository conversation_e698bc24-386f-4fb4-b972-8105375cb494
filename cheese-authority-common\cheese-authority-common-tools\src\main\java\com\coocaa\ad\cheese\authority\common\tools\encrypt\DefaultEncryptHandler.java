package com.coocaa.ad.cheese.authority.common.tools.encrypt;

import cn.hutool.core.codec.Base64;
import com.coocaa.ad.cheese.authority.common.tools.annotation.EncryptField;
import com.coocaa.ad.cheese.authority.common.tools.utils.AesUtils;
import com.coocaa.ad.cheese.authority.common.tools.utils.RsaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 默认加解密处理器实现
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class DefaultEncryptHandler implements EncryptHandler {
    
    @Override
    public String encrypt(String plainText, EncryptField.EncryptType encryptType) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }
        
        try {
            switch (encryptType) {
                case AES:
                    return AesUtils.encryptHex(plainText);
                case RSA:
                    return RsaUtils.encryptByPublicKey(RsaUtils.publicKey, plainText);
                case BASE64:
                    return Base64.encode(plainText);
                default:
                    log.warn("不支持的加密类型: {}", encryptType);
                    return plainText;
            }
        } catch (Exception e) {
            log.error("加密失败，明文: {}, 加密类型: {}", plainText, encryptType, e);
            return plainText;
        }
    }
    
    @Override
    public String decrypt(String cipherText, EncryptField.EncryptType encryptType) {
        if (!StringUtils.hasText(cipherText)) {
            return cipherText;
        }
        
        try {
            switch (encryptType) {
                case AES:
                    return AesUtils.decryptStr(cipherText);
                case RSA:
                    return RsaUtils.decryptByPrivateKey(RsaUtils.privateKey, cipherText);
                case BASE64:
                    return Base64.decodeStr(cipherText);
                default:
                    log.warn("不支持的解密类型: {}", encryptType);
                    return cipherText;
            }
        } catch (Exception e) {
            log.error("解密失败，密文: {}, 解密类型: {}", cipherText, encryptType, e);
            return cipherText;
        }
    }
    
    @Override
    public boolean supports(EncryptField.EncryptType encryptType) {
        return encryptType == EncryptField.EncryptType.AES 
            || encryptType == EncryptField.EncryptType.RSA 
            || encryptType == EncryptField.EncryptType.BASE64;
    }
}
