package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRuleMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalRuleService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalRuleServiceImpl extends ServiceImpl<ApproveInternalRuleMapper, ApproveInternalRuleEntity> implements IApproveInternalRuleService {
}
