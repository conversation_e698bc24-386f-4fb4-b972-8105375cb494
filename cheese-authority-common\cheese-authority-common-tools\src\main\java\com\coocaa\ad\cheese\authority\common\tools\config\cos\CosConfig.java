package com.coocaa.ad.cheese.authority.common.tools.config.cos;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.coocaa.ad.cheese.authority.common.tools.config.BaseCosConfig;
import com.qcloud.cos.COSClient;

@Configuration
public class CosConfig extends BaseCosConfig {

    @Value("${cos.secret-id}")
    private String secretId;
    
    @Value("${cos.secret-key}")
    private String secretKey;
    
    @Value("${cos.region}")
    public String region;
    
    @Value("${cos.connection-timeout:5000}")
    private Integer connectionTimeout;
    
    @Value("${cos.socket-timeout:10000}")
    private Integer socketTimeout;

    @Bean
    public COSClient cosClient() {
        COSClient cosClient = createCosClient(
            secretId,
            secretKey,
            region,
            connectionTimeout,
            socketTimeout
        );
        return cosClient;
    }
}
