package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户基础信息
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@Accessors(chain = true)
public class UserBasicVO
{
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "123")
    private String wno;

    @Schema(description = "电话号码", type = "String", example = "")
    private String mobile;

}