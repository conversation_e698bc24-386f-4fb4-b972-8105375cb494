package com.coocaa.ad.cheese.authority.common.db.entity.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "message_internal")
public class MessageInternalEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息应用类型编码，字典0124
     */
    private String appCode;

    /**
     * 消息模块类型编码，字典0125
     */
    private String moduleCode;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 跳转地址
     */
    private String url;

    /**
     * 消息发送人
     */
    private Integer sendUser;

    /**
     * 消息接收人
     */
    private Integer receiveUser;

    /**
     * 是否已读，1：已读，0：未读
     */
    private Integer readFlag;

    /**
     * 飞书记录uuid
     */
    private String recordId;

    /**
     * 已读时间
     */
    private LocalDateTime readTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;
}
