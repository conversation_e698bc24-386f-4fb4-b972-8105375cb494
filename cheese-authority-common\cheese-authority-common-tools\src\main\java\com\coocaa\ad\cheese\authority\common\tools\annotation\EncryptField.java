package com.coocaa.ad.cheese.authority.common.tools.annotation;

import java.lang.annotation.*;

/**
 * 字段加解密注解
 * 用于标记需要自动加解密的数据库字段
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EncryptField {
    
    /**
     * 加密算法类型
     */
    EncryptType value() default EncryptType.AES;
    
    /**
     * 是否启用加解密
     * 可用于动态控制是否加解密
     */
    boolean enabled() default true;
    
    /**
     * 加密算法枚举
     */
    enum EncryptType {
        /**
         * AES对称加密
         */
        AES,
        
        /**
         * RSA非对称加密
         */
        RSA,
        
        /**
         * Base64编码（非加密，仅编码）
         */
        BASE64
    }
}
