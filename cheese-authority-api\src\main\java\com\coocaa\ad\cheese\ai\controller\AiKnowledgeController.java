package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.service.AiKnowledgeService;
import com.coocaa.ad.cheese.ai.vo.*;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * AI知识库管理Controller
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@RestController
@RequestMapping("/sys/ai/knowledge")
@Tag(name = "AI知识库管理", description = "AI知识库相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
@Slf4j
public class AiKnowledgeController {

    private final AiKnowledgeService aiKnowledgeService;

    @Operation(
            summary = "获取所有知识库列表",
            description = "获取所有可用的知识库列表"
    )
    @GetMapping("/list")
    public ResultTemplate<List<AiKnowledgeVO>> getAllKnowledgeBases() {
        log.info("获取所有知识库列表请求");
        
        try {
            List<AiKnowledgeVO> knowledgeBases = aiKnowledgeService.getAllKnowledgeBases();
            return ResultTemplate.success(knowledgeBases);
            
        } catch (Exception e) {
            log.error("获取知识库列表失败", e);
            return ResultTemplate.fail("获取知识库列表失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "根据ID获取知识库",
            description = "根据知识库ID获取知识库详细信息"
    )
    @GetMapping("/{knowledgeId}")
    public ResultTemplate<AiKnowledgeVO> getKnowledgeBaseById(
            @Parameter(description = "知识库ID", required = true)
            @PathVariable Long knowledgeId) {
        
        log.info("根据ID获取知识库，知识库ID: {}", knowledgeId);
        
        try {
            AiKnowledgeVO knowledgeBase = aiKnowledgeService.getKnowledgeBaseById(knowledgeId);
            return ResultTemplate.success(knowledgeBase);
            
        } catch (Exception e) {
            log.error("获取知识库失败，知识库ID: {}", knowledgeId, e);
            return ResultTemplate.fail("获取知识库失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "上传文档到知识库",
            description = "上传文档文件到指定知识库"
    )
    @PostMapping("/upload/file")
    public ResultTemplate<Long> uploadDocument(
            @Parameter(description = "文档文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "知识库ID", required = true)
            @RequestParam("knowledgeId") Long knowledgeId,
            @Parameter(description = "文档名称")
            @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "是否立即处理")
            @RequestParam(value = "processImmediately", defaultValue = "true") Boolean processImmediately) {
        
        log.info("上传文档到知识库，知识库ID: {}, 文件名: {}", knowledgeId, file.getOriginalFilename());
        
        try {
            AiKnowledgeDocumentUploadReqVO uploadReq = new AiKnowledgeDocumentUploadReqVO();
            uploadReq.setKnowledgeId(knowledgeId);
            uploadReq.setName(name);
            uploadReq.setProcessImmediately(processImmediately);
            
            Long documentId = aiKnowledgeService.uploadDocument(file, uploadReq);
            return ResultTemplate.success(documentId);
            
        } catch (Exception e) {
            log.error("上传文档失败，知识库ID: {}, 文件名: {}", knowledgeId, file.getOriginalFilename(), e);
            return ResultTemplate.fail("上传文档失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "上传文本内容到知识库",
            description = "直接上传文本内容到指定知识库"
    )
    @PostMapping("/upload/text")
    public ResultTemplate<Long> uploadTextContent(
            @Parameter(description = "文本上传请求", required = true)
            @RequestBody @Validated AiKnowledgeDocumentUploadReqVO uploadReq) {
        
        log.info("上传文本内容到知识库，知识库ID: {}, 内容长度: {}", 
                uploadReq.getKnowledgeId(), 
                uploadReq.getContent() != null ? uploadReq.getContent().length() : 0);
        
        try {
            Long documentId = aiKnowledgeService.uploadTextContent(uploadReq);
            return ResultTemplate.success(documentId);
            
        } catch (Exception e) {
            log.error("上传文本内容失败，知识库ID: {}", uploadReq.getKnowledgeId(), e);
            return ResultTemplate.fail("上传文本内容失败: " + e.getMessage());
        }
    }

    @Operation(
            summary = "在知识库中搜索",
            description = "在指定知识库中搜索相关内容"
    )
    @PostMapping("/search")
    public ResultTemplate<AiKnowledgeSearchRespVO> searchKnowledge(
            @Parameter(description = "搜索请求", required = true)
            @RequestBody @Validated AiKnowledgeSearchReqVO searchReq) {
        
        log.info("在知识库中搜索，知识库ID: {}, 查询: {}", searchReq.getKnowledgeId(), searchReq.getQuery());
        
        try {
            AiKnowledgeSearchRespVO searchResult = aiKnowledgeService.searchKnowledge(searchReq);
            return ResultTemplate.success(searchResult);
            
        } catch (Exception e) {
            log.error("知识库搜索失败，知识库ID: {}, 查询: {}", searchReq.getKnowledgeId(), searchReq.getQuery(), e);
            return ResultTemplate.fail("知识库搜索失败: " + e.getMessage());
        }
    }
}
