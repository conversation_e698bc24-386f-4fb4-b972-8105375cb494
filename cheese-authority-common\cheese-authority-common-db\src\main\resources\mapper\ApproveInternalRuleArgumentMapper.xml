<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRuleArgumentMapper">

    <select id="getArgByRuleNo"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleArgumentEntity">
        select a.id,a.name,a.symbol,a.field_value,r.id as ruleId,r.create_time as ruleCreateTime
        from
        approve_internal_rule_argument a
        right join
        approve_internal_rule r on a.rule_id = r.id
        where r.flag = 1 and r.code = #{ruleNo}
        order by r.create_time desc,r.id desc
    </select>
</mapper>
