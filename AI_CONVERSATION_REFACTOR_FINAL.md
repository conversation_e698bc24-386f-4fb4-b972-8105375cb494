# AI对话表重构 - 最终版本

## 概述

按照项目标准结构，将AI对话相关的数据访问层代码放到 `com.coocaa.ad.cheese.ai.common.db` 包下，业务逻辑在 `AiConversationService` 中使用MyBatis-Plus进行实现。

## 代码结构

### 1. 数据访问层 (common-db)

#### 实体类
```
cheese-authority-common/cheese-authority-common-db/src/main/java/com/coocaa/ad/cheese/ai/common/db/entity/AiConversationEntity.java
```

```java
@Data
@TableName("ai_conversation")
public class AiConversationEntity implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;
    
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;
}
```

#### Mapper接口
```
cheese-authority-common/cheese-authority-common-db/src/main/java/com/coocaa/ad/cheese/ai/common/db/mapper/AiConversationMapper.java
```

```java
public interface AiConversationMapper extends BaseMapper<AiConversationEntity> {
}
```

#### Service接口
```
cheese-authority-common/cheese-authority-common-db/src/main/java/com/coocaa/ad/cheese/ai/common/db/service/IAiConversationService.java
```

```java
public interface IAiConversationService extends IService<AiConversationEntity> {
}
```

#### Service实现
```
cheese-authority-common/cheese-authority-common-db/src/main/java/com/coocaa/ad/cheese/ai/common/db/service/impl/AiConversationServiceImpl.java
```

```java
@Service
public class AiConversationServiceImpl extends ServiceImpl<AiConversationMapper, AiConversationEntity> 
        implements IAiConversationService {
}
```

### 2. 业务层 (api)

#### AiConversationService 业务逻辑

```java
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiConversationService {
    
    private final IAiChatService aiChatService;
    private final IAiUserService aiUserService;
    private final IAiConversationService aiConversationService; // 注入数据访问层服务
    private final DoubaoAiService doubaoAiService;
    
    // 业务方法使用MyBatis-Plus进行数据操作
}
```

## 核心业务方法

### 1. 创建对话
```java
@Transactional(rollbackFor = Exception.class)
public AiConversationCreateRespVO createConversation(AiConversationCreateReqVO reqVO) {
    // 1. 查找或创建用户信息
    AiUserEntity userEntity = findOrCreateUser(reqVO);

    // 2. 创建对话记录
    AiConversationEntity conversationEntity = createConversationForUser(userEntity.getId());
    
    // 3. 创建对话初始记录
    createConversationRecord(conversationEntity.getId(), userEntity.getOpenId(), title);
    
    // 4. 返回响应
    return respVO;
}
```

### 2. 为用户创建对话
```java
private AiConversationEntity createConversationForUser(Long userId) {
    log.info("为用户创建新对话，用户ID: {}", userId);
    
    AiConversationEntity conversation = new AiConversationEntity();
    conversation.setUserId(userId);
    conversation.setCreateTime(LocalDateTime.now());
    conversation.setUpdateTime(LocalDateTime.now());
    conversation.setCreateBy("system");
    conversation.setUpdateBy("system");
    conversation.setDeleteFlag(0);
    
    // 使用MyBatis-Plus保存
    aiConversationService.save(conversation);
    
    log.info("对话创建成功，对话ID: {}, 用户ID: {}", conversation.getId(), userId);
    return conversation;
}
```

### 3. 查找或创建用户
```java
private AiUserEntity findOrCreateUser(AiConversationCreateReqVO reqVO) {
    // 使用MyBatis-Plus查询
    LambdaQueryWrapper<AiUserEntity> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(AiUserEntity::getOpenId, reqVO.getOpenId())
           .eq(AiUserEntity::getDeleteFlag, 0)
           .orderByDesc(AiUserEntity::getCreateTime)
           .last("LIMIT 1");
    
    AiUserEntity existingUser = aiUserService.getOne(wrapper);
    
    if (existingUser != null) {
        return existingUser;
    }
    
    // 创建新用户
    AiUserEntity newUser = new AiUserEntity();
    // ... 设置用户信息
    aiUserService.save(newUser);
    return newUser;
}
```

### 4. 根据对话ID获取用户信息
```java
private AiUserEntity getUserByConversationId(Long conversationId) {
    // 使用MyBatis-Plus查询对话
    AiConversationEntity conversation = aiConversationService.getById(conversationId);
    if (conversation == null) {
        return null;
    }
    
    // 查询用户信息
    return aiUserService.getById(conversation.getUserId());
}
```

## 用户信息管理接口

### 1. 获取用户信息
```java
@GetMapping("/user/{userId}")
public ResultTemplate<AiUserInfoVO> getUserInfo(@PathVariable("userId") Long userId) {
    AiUserVO userVO = aiUserService.getDetail(userId);
    if (userVO == null) {
        return ResultTemplate.fail("用户不存在");
    }
    
    AiUserInfoVO userInfoVO = AiUserInfoVO.builder()
            .id(userVO.getId())
            .openId(userVO.getOpenId())
            .nickName(userVO.getName())
            .avatarUrl(userVO.getAvatar())
            .createTime(userVO.getCreateTime())
            .updateTime(userVO.getUpdateTime())
            .build();
    
    return ResultTemplate.success(userInfoVO);
}
```

### 2. 更新用户信息
```java
@PutMapping("/user")
public ResultTemplate<Boolean> updateUserInfo(@RequestBody @Validated AiUserUpdateReqVO updateReq) {
    AiUserVO existingUser = aiUserService.getDetail(updateReq.getId());
    if (existingUser == null) {
        return ResultTemplate.fail("用户不存在");
    }
    
    // 构建更新参数
    AiUserParam updateParam = new AiUserParam();
    // ... 设置更新字段
    
    boolean updated = aiUserService.update(updateReq.getId(), updateParam);
    return ResultTemplate.success(updated);
}
```

## 数据库表结构

```sql
CREATE TABLE `ai_conversation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（关联ai_user表的id）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI对话表';
```

## 主要优势

### 1. 符合项目架构
- 数据访问层代码放在 `common-db` 包下
- 业务逻辑在 `api` 层实现
- 使用MyBatis-Plus进行数据操作

### 2. 代码结构清晰
- 实体、Mapper、Service分层明确
- 业务逻辑与数据访问分离
- 便于维护和扩展

### 3. 功能完整
- 支持用户信息管理
- 支持对话会话管理
- 支持一用户多对话

### 4. 数据一致性
- 用户信息不重复创建
- 对话与用户关联明确
- 支持软删除

## 使用示例

### 1. 创建对话
```bash
curl -X POST http://localhost:8001/sys/ai-conversations/create \
  -H "Content-Type: application/json" \
  -d '{
    "openId": "wx_user_123",
    "name": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "title": "新的对话"
  }'
```

### 2. 发送消息
```bash
curl -X POST http://localhost:8001/sys/ai-conversations/send-stream \
  -H "Content-Type: application/json" \
  -d '{
    "conversationId": 1001,
    "content": "你好",
    "useContext": true
  }'
```

### 3. 获取用户信息
```bash
curl -X GET http://localhost:8001/sys/wechat/user/1
```

### 4. 更新用户信息
```bash
curl -X PUT http://localhost:8001/sys/wechat/user \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "nickName": "新昵称",
    "avatarUrl": "https://example.com/new_avatar.jpg"
  }'
```

## 总结

重构后的代码完全符合项目的标准架构：

✅ **数据访问层**：实体、Mapper、Service放在 `common-db` 包下  
✅ **业务逻辑层**：在 `AiConversationService` 中注入数据访问服务  
✅ **MyBatis-Plus**：使用标准的MyBatis-Plus方法进行数据操作  
✅ **用户管理**：提供完整的用户信息管理接口  
✅ **对话管理**：支持独立的对话会话管理  

代码结构清晰，符合项目规范，便于维护和扩展！
