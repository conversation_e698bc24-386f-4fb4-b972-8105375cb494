package com.coocaa.ad.cheese.authority.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class UserFeishuVO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 飞书用户ID
     */
    private String feishuUserId;

    /**
     * 用户的 union_id
     */
    private String unionId;

    /**
     * openid
     */
    private String openId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    private String wno;
}

