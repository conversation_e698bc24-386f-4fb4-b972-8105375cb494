package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.coocaa.ad.cheese.authority.common.tools.utils.ChangeFormatter;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.ChangeExtract;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
public class UserDepartmentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * 姓名
     */
    @ChangeExtract(readableName = "姓名")
    private String name;

    /**
     * 登陆名
     */
    @ChangeExtract(readableName = "登陆名")
    private String userName;

    /**
     * 手机号
     */
    @ChangeExtract(readableName = "手机")
    private String mobile;
    /**
     * 部门领导OpenId
     */
    @ChangeExtract(readableName = "部门领导OpenId")
    private String leaderUserId;
    /**
     * 部门领导OpenId
     */
    @ChangeExtract(readableName = "部门OpenId")
    private String openDepartmentId;
    /**
     * 部门名称
     */
    @ChangeExtract(readableName = "部门名称")
    private String departmentName;

    @ChangeExtract(readableName = "状态,0禁用,1正常,2未激活")
    private Integer status;
}
