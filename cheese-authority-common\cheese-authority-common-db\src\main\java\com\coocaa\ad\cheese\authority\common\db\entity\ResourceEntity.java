package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.coocaa.ad.cheese.authority.common.tools.utils.ChangeFormatter;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.ChangeExtract;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 资源
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("resource")
public class ResourceEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父ID
     */
    private Integer parentId;

    /**
     * 名称
     */
    @ChangeExtract(readableName = "名称")
    private String name;

    /**
     * 编码
     */
    @ChangeExtract(readableName = "编码")
    private String code;

    /**
     * 资源类型 [1:系统, 2:菜单, 3:内部]
     */
    private Integer type;

    /**
     * 资源地址
     */
    @ChangeExtract(readableName = "地址")
    private String uri;

    /**
     * 排序(自然顺序，越小越靠前)
     */
    private Integer rank;

    /**
     * 层级 (系统:1级)
     */
    private Integer level;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    @ChangeExtract(readableName = "状态", formatter = ChangeFormatter.StatusFormatter.class)
    private Boolean status;

    /**
     * 删除标记 [0:未删除, 1:已删除]
     */
    @ChangeExtract(readableName = "删除标记", formatter = ChangeFormatter.DeleteFormatter.class)
    private Boolean deleteFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;
    /**
     * 平台字段
     */
    private String platform;

}
