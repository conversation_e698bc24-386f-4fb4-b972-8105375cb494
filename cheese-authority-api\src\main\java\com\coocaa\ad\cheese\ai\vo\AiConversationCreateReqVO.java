package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * AI 对话创建 Request VO
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Schema(description = "管理后台 - AI 对话创建 Request VO")
@Data
public class AiConversationCreateReqVO {

    @Schema(description = "用户openId", requiredMode = Schema.RequiredMode.REQUIRED, example = "user123")
    @NotEmpty(message = "用户openId不能为空")
    private String openId;

    @Schema(description = "对话标题", example = "新的对话")
    private String title;

    @Schema(description = "角色ID", example = "1")
    private Long roleId;

    @Schema(description = "用户名", example = "张三")
    private String name;

    @Schema(description = "头像", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "手机号", example = "13800138000")
    private String mobile;
}
