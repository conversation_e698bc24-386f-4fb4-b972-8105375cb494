-- AI知识库相关表结构
-- 执行时间：2025-07-17

-- 1. 知识库表
CREATE TABLE `ai_knowledge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '知识库名称',
  `description` text COMMENT '知识库描述',
  `embedding_model` varchar(50) DEFAULT 'text-embedding-ada-002' COMMENT '嵌入模型',
  `vector_dimension` int(11) DEFAULT 1536 COMMENT '向量维度',
  `chunk_size` int(11) DEFAULT 1000 COMMENT '文档分块大小',
  `chunk_overlap` int(11) DEFAULT 200 COMMENT '分块重叠大小',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态（active-活跃，inactive-非活跃）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI知识库表';

-- 2. 知识库文档表
CREATE TABLE `ai_knowledge_document` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `name` varchar(200) NOT NULL COMMENT '文档名称',
  `file_name` varchar(200) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型（pdf,docx,txt,md等）',
  `content` longtext COMMENT '文档内容',
  `status` varchar(20) DEFAULT 'uploading' COMMENT '状态（uploading-上传中，processing-处理中，completed-完成，failed-失败）',
  `chunk_count` int(11) DEFAULT 0 COMMENT '分块数量',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`),
  CONSTRAINT `fk_document_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `ai_knowledge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI知识库文档表';

-- 3. 知识库文档分块表
CREATE TABLE `ai_knowledge_segment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `content` text NOT NULL COMMENT '分块内容',
  `word_count` int(11) DEFAULT 0 COMMENT '字数',
  `position` int(11) NOT NULL COMMENT '在文档中的位置',
  `vector_id` varchar(100) DEFAULT NULL COMMENT '向量数据库中的ID',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态（pending-待处理，vectorized-已向量化，failed-失败）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_vector_id` (`vector_id`),
  KEY `idx_status` (`status`),
  KEY `idx_position` (`position`),
  KEY `idx_delete_flag` (`delete_flag`),
  CONSTRAINT `fk_segment_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `ai_knowledge` (`id`),
  CONSTRAINT `fk_segment_document` FOREIGN KEY (`document_id`) REFERENCES `ai_knowledge_document` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI知识库文档分块表';

-- 4. 角色知识库关联表
CREATE TABLE `ai_role_knowledge` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `knowledge_id` bigint(20) NOT NULL COMMENT '知识库ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_knowledge` (`role_id`, `knowledge_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  CONSTRAINT `fk_role_knowledge_role` FOREIGN KEY (`role_id`) REFERENCES `ai_role` (`id`),
  CONSTRAINT `fk_role_knowledge_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `ai_knowledge` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色知识库关联表';

-- 插入示例数据
INSERT INTO `ai_knowledge` (`name`, `description`, `embedding_model`, `vector_dimension`, `chunk_size`, `chunk_overlap`, `status`) VALUES 
('通用知识库', '包含常见问题和答案的通用知识库', 'text-embedding-ada-002', 1536, 1000, 200, 'active'),
('技术文档库', '包含技术文档和API说明的知识库', 'text-embedding-ada-002', 1536, 800, 150, 'active'),
('产品手册库', '包含产品使用手册和说明的知识库', 'text-embedding-ada-002', 1536, 1200, 250, 'active');

-- 表结构说明：
-- ai_knowledge: 知识库基本信息，包含嵌入模型配置和分块参数
-- ai_knowledge_document: 存储上传的文档信息和处理状态
-- ai_knowledge_segment: 文档分块后的内容，每个分块对应一个向量
-- ai_role_knowledge: 角色与知识库的多对多关联关系

-- 使用场景：
-- 1. 创建知识库并配置参数
-- 2. 上传文档到知识库
-- 3. 文档自动分块和向量化
-- 4. 角色绑定知识库
-- 5. 基于知识库进行RAG对话

-- 查询示例：
-- 1. 查询知识库及其文档数量：
-- SELECT k.*, COUNT(d.id) as document_count 
-- FROM ai_knowledge k 
-- LEFT JOIN ai_knowledge_document d ON k.id = d.knowledge_id AND d.delete_flag = 0 
-- WHERE k.delete_flag = 0 
-- GROUP BY k.id;

-- 2. 查询角色关联的知识库：
-- SELECT k.* FROM ai_knowledge k 
-- INNER JOIN ai_role_knowledge rk ON k.id = rk.knowledge_id 
-- WHERE rk.role_id = ? AND k.delete_flag = 0;

-- 3. 查询文档的分块信息：
-- SELECT * FROM ai_knowledge_segment 
-- WHERE document_id = ? AND delete_flag = 0 
-- ORDER BY position;
