package com.coocaa.ad.cheese.authority.common.db.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDataPermissionEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserDataPermissionMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IUserDataPermissionService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户数据权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Service
public class UserDataPermissionServiceImpl extends ServiceImpl<UserDataPermissionMapper,UserDataPermissionEntity> implements IUserDataPermissionService{

}
