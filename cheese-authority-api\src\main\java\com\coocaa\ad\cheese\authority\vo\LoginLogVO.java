package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 登陆日志
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-19
 */
@Data
@Accessors(chain = true)
public class LoginLogVO {

    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "用户ID", type = "Integer", example = "1")
    private Integer userId;

    @Schema(description = "用户名称", type = "String", example = "张三")
    private String userName;

    @Schema(description = "IP地址", type = "String", example = "***********")
    private String ip;

    @Schema(description = "登陆地址", type = "String", example = "四川成都")
    private String address;

    @Schema(description = "浏览器", type = "String", example = "Chrome")
    private String browser;

    @Schema(description = "用户Agent", type = "String", example = "Chrome")
    private String userAgent;

    @Schema(description = "来源", type = "String", example = "来源")
    private String source;

    @Schema(description = "登陆状态", type = "String", example = "true")
    private Boolean status;

    @Schema(description = "失败原因", type = "String", example = "密码错误")
    private String failReason;

    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "Date", example = "2024.10.22 15:26:37")
    private Date loginTime;

}
