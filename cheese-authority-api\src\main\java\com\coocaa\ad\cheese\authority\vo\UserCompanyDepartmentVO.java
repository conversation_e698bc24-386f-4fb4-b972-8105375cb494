package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class UserCompanyDepartmentVO {

    /**
     * ID
     */
    private Integer userDepartmentRelationId;


    /**
     * 部门名称
     */
    @Schema(description = "部门名称", type = "String", example = "")
    private String departmentName;

    /**
     * 部门ID
     */
    @Schema(description = "部门openID", type = "String", example = "")
    private String departmentOpenId;

    /**
     * 公司名称
     */
    @Schema(description = "部门名称", type = "String", example = "")
    private String companyName;

    /**
     * 公司ID
     */
    @Schema(description = "部门openID", type = "String", example = "")
    private String companyOpenId;



}
