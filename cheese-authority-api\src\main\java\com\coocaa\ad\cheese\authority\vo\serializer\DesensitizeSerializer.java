package com.coocaa.ad.cheese.authority.vo.serializer;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;
import com.coocaa.ad.cheese.authority.common.tools.utils.AesUtils;
import com.coocaa.ad.cheese.authority.common.tools.utils.RsaUtils;
import com.coocaa.ad.cheese.authority.common.tools.utils.StringUtils;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Objects;

/**
 * 对数据进行加密，并进行脱敏
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-06
 */
@Slf4j
public class DesensitizeSerializer extends JsonSerializer<String> implements ObjectWriter<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StringUtils.isBlank(value)) {
            gen.writeNull();
            return;
        }

        String text = getText(value, gen.getOutputContext().getCurrentName());
        if (StringUtils.isBlank(text)) {
            gen.writeNull();
        } else {
            gen.writeString(text);
        }
    }


    @Override
    public void write(JSONWriter jsonWriter, Object object, Object fieldName, Type fieldType, long features) {
        if (Objects.isNull(object)) {
            jsonWriter.writeNull();
            return;
        }

        String text = getText((String) object, (String) fieldName);
        if (StringUtils.isBlank(text)) {
            jsonWriter.writeNull();
        } else {
            jsonWriter.writeString(text);
        }
    }


    /**
     * 解析成数据库存储的密文
     */
    private String getText(String value, String fieldName) {
        try {
            String plainText = AesUtils.decryptStr(value);
            log.info("对参数[{}]解密结果:[{}]", value, plainText);

            if (StringUtils.isBlank(plainText)) {
                return null;
            }

            // 如果是邮箱，则脱敏
            String desensitizedText = plainText;
            if (StringUtils.indexOf(plainText, '@') > -1 || StringUtils.containsIgnoreCase(fieldName, "mobile")) {
                desensitizedText = StringUtils.desensitizedEmail(plainText);
            } else {
                desensitizedText = DesensitizedUtil.mobilePhone(plainText);
            }
            log.info("对参数[{}]脱敏结果:[{}]", plainText, desensitizedText);

            String encryptedText = RsaUtils.encryptByPublicKey(RsaUtils.publicKey, desensitizedText);
            log.info("对参数[{}]加密结果:[{}]", plainText, encryptedText);

            return encryptedText;
        } catch (Exception e) {
            log.warn("解密/加密文本({})失败", value);
            return null;
        }
    }
}