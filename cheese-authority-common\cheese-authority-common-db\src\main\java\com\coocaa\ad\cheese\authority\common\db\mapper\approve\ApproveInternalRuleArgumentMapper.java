package com.coocaa.ad.cheese.authority.common.db.mapper.approve;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleArgumentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Mapper
public interface ApproveInternalRuleArgumentMapper extends BaseMapper<ApproveInternalRuleArgumentEntity> {
    List<ApproveInternalRuleArgumentEntity> getArgByRuleNo(@Param("ruleNo") Integer ruleNo);
}
