package com.coocaa.ad.cheese.authority.common.tools.query.enums;

/**
 * 类DeletedStatus实现描述:
 * 删除状态
 * <AUTHOR>
 * @date 2022年05月08 12:58
 */
public enum DeletedStatus {

    NOT_DELETED(0, "未删除"),
    DELETED(1, "已删除");


    public Integer key;
    public String value;


    DeletedStatus(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
