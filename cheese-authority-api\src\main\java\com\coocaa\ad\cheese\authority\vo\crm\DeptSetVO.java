package com.coocaa.ad.cheese.authority.vo.crm;

import groovyjarjarantlr4.v4.runtime.misc.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024年12月21 10:30
 */
@Data
@Accessors(chain = true)
public class DeptSetVO {

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "上级部门ID，0为最上级")
    @NotNull
    private Long parentId;

    @Schema(description = "部门名称")
    @NotNull
    @Size(max = 20)
    private String name;

    @Schema(description = "部门负责人")
    private Long ownerUserId;

}
