# AI知识库Spring AI集成测试指南

## 概述

现在我们已经完成了Spring AI的集成，包括：
- ✅ Spring AI VectorStore支持
- ✅ Spring AI Tika Document Reader集成
- ✅ 双重实现策略（Spring AI + 自定义Redis）
- ✅ 自动回退机制

## 🧪 测试步骤

### 1. 环境准备

#### 检查依赖
确保以下依赖已正确添加到 `cheese-authority-api/pom.xml`：

```xml
<!-- Spring AI Redis Vector Store -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-vector-store-redis</artifactId>
    <version>1.0.0-M6</version>
</dependency>

<!-- Spring AI Tika Document Reader -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-tika-document-reader</artifactId>
    <version>1.0.0-M6</version>
</dependency>

<!-- Apache Tika -->
<dependency>
    <groupId>org.apache.tika</groupId>
    <artifactId>tika-core</artifactId>
    <version>2.9.1</version>
</dependency>
```

#### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

### 2. 功能测试

#### A. 文本文档上传测试

```bash
# 1. 上传文本内容
curl -X POST http://localhost:8080/sys/ai/knowledge/upload/text \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeId": 1,
    "name": "Spring AI测试文档",
    "content": "Spring AI是一个用于构建AI应用的框架。它提供了向量存储、文档读取、嵌入模型等功能。通过Spring AI，开发者可以轻松构建RAG应用。",
    "processImmediately": true
  }'
```

**预期响应：**
```json
{
  "code": 200,
  "data": 123,
  "message": "文档上传成功"
}
```

#### B. 文件上传测试

```bash
# 2. 上传PDF文件（测试Tika集成）
curl -X POST http://localhost:8080/sys/ai/knowledge/upload/file \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test-document.pdf" \
  -F "knowledgeId=1" \
  -F "name=PDF测试文档" \
  -F "processImmediately=true"
```

#### C. 语义搜索测试

```bash
# 3. 搜索相关内容
curl -X POST http://localhost:8080/sys/ai/knowledge/search \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeId": 1,
    "query": "什么是Spring AI",
    "topK": 5,
    "threshold": 0.7
  }'
```

**预期响应：**
```json
{
  "code": 200,
  "data": {
    "results": [
      {
        "segmentId": 456,
        "documentId": 123,
        "documentName": "Spring AI测试文档",
        "content": "Spring AI是一个用于构建AI应用的框架...",
        "score": 0.85,
        "position": 1,
        "wordCount": 50
      }
    ],
    "searchTime": 120,
    "totalCount": 1
  }
}
```

### 3. 日志检查

#### 启动日志
查看应用启动时的日志，确认Spring AI组件是否正确加载：

```log
2025-07-17 10:00:00 [main] INFO  SpringAiConfig - Spring AI配置类已加载
2025-07-17 10:00:01 [main] INFO  SpringAiConfig - 没有配置EmbeddingModel，将使用自定义嵌入模型工厂
2025-07-17 10:00:01 [main] INFO  SpringAiConfig - 没有配置VectorStore，将使用自定义Redis实现
2025-07-17 10:00:02 [main] INFO  AiVectorService - AI向量服务初始化完成，使用自定义Redis实现
```

#### 文档处理日志
```log
2025-07-17 10:05:00 [http-nio-8080-exec-1] INFO  DocumentContentExtractor - 开始提取文档内容，文件名: test.pdf, 大小: 1024 bytes
2025-07-17 10:05:01 [http-nio-8080-exec-1] INFO  DocumentContentExtractor - 使用Spring AI Tika Document Reader提取内容
2025-07-17 10:05:02 [http-nio-8080-exec-1] INFO  DocumentContentExtractor - 内容提取成功，提取文本长度: 500
```

#### 向量化日志
```log
2025-07-17 10:05:03 [http-nio-8080-exec-1] INFO  AiVectorService - 开始对分块进行向量化，分块数量: 3
2025-07-17 10:05:03 [http-nio-8080-exec-1] INFO  AiVectorService - 使用自定义Redis实现进行向量化
2025-07-17 10:05:04 [http-nio-8080-exec-1] INFO  AiVectorService - 分块向量化完成，分块ID: 456, 向量ID: kb_1_doc_123_seg_456
```

#### 搜索日志
```log
2025-07-17 10:10:00 [http-nio-8080-exec-2] INFO  AiVectorService - 搜索相似分块，知识库ID: 1, 查询: 什么是Spring AI, TopK: 5
2025-07-17 10:10:00 [http-nio-8080-exec-2] INFO  AiVectorService - 使用自定义Redis搜索
2025-07-17 10:10:01 [http-nio-8080-exec-2] INFO  AiVectorService - 自定义Redis搜索完成，找到1个相似结果
```

### 4. Redis数据检查

#### 检查向量数据
```bash
# 连接Redis
redis-cli

# 查看向量索引
SMEMBERS ai:vector:index:1

# 查看向量数据
HGETALL ai:vector:data:kb_1_doc_123_seg_456

# 查看向量内容
HGET ai:vector:data:kb_1_doc_123_seg_456 vector
```

### 5. 错误处理测试

#### A. 无效文件格式测试
```bash
# 上传不支持的文件格式
curl -X POST http://localhost:8080/sys/ai/knowledge/upload/file \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test.xyz" \
  -F "knowledgeId=1" \
  -F "name=无效格式测试"
```

**预期响应：**
```json
{
  "code": 400,
  "message": "不支持的文件格式: xyz"
}
```

#### B. 大文件测试
```bash
# 上传超大文件（>10MB）
curl -X POST http://localhost:8080/sys/ai/knowledge/upload/file \
  -H "Content-Type: multipart/form-data" \
  -F "file=@large-file.pdf" \
  -F "knowledgeId=1" \
  -F "name=大文件测试"
```

**预期响应：**
```json
{
  "code": 400,
  "message": "文件大小超过限制，最大支持10MB"
}
```

### 6. 性能测试

#### 批量文档上传
```javascript
// 批量上传测试脚本
const uploadPromises = [];
for (let i = 0; i < 10; i++) {
  const promise = fetch('/sys/ai/knowledge/upload/text', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      knowledgeId: 1,
      name: `测试文档${i}`,
      content: `这是第${i}个测试文档的内容。包含一些关于AI和机器学习的信息。`,
      processImmediately: true
    })
  });
  uploadPromises.push(promise);
}

Promise.all(uploadPromises).then(results => {
  console.log('批量上传完成:', results.length);
});
```

#### 并发搜索测试
```javascript
// 并发搜索测试
const searchPromises = [];
const queries = ['AI', '机器学习', 'Spring', '框架', '应用'];

for (const query of queries) {
  const promise = fetch('/sys/ai/knowledge/search', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      knowledgeId: 1,
      query: query,
      topK: 3,
      threshold: 0.5
    })
  });
  searchPromises.push(promise);
}

Promise.all(searchPromises).then(results => {
  console.log('并发搜索完成:', results.length);
});
```

## 🔧 故障排除

### 1. Spring AI依赖问题
如果遇到Spring AI类找不到的错误：

```bash
# 清理Maven缓存
mvn clean

# 重新下载依赖
mvn dependency:resolve

# 检查依赖树
mvn dependency:tree | grep spring-ai
```

### 2. Tika解析失败
如果PDF或Office文档解析失败：

```log
# 检查Tika版本兼容性
# 确保tika-core和tika-parsers版本一致
# 检查文档是否损坏或加密
```

### 3. Redis连接问题
```bash
# 检查Redis连接
redis-cli ping

# 检查Redis配置
redis-cli config get "*"

# 检查内存使用
redis-cli info memory
```

### 4. 向量搜索结果为空
```bash
# 检查向量数据是否存在
redis-cli SMEMBERS ai:vector:index:1

# 检查嵌入模型是否正常工作
# 查看日志中的向量化过程

# 检查相似度阈值设置
# 尝试降低threshold值
```

## 📊 监控指标

### 关键指标
- 文档上传成功率
- 向量化处理时间
- 搜索响应时间
- Redis内存使用量
- 错误率统计

### 监控命令
```bash
# 查看应用日志
tail -f logs/application.log | grep -E "(AiVectorService|DocumentContentExtractor)"

# 监控Redis内存
redis-cli --latency-history -i 1

# 查看JVM内存使用
jstat -gc [pid] 1s
```

## 🎯 下一步优化

### 1. 集成真实嵌入模型
- 配置OpenAI API密钥
- 实现HuggingFace模型调用
- 添加本地模型支持

### 2. 完善Spring AI集成
- 配置正确的RedisVectorStore
- 添加EmbeddingModel Bean
- 实现完整的Spring AI工作流

### 3. 性能优化
- 实现批量向量化
- 添加缓存机制
- 优化搜索算法

### 4. 监控和告警
- 添加Prometheus指标
- 配置Grafana仪表板
- 设置告警规则

## 总结

Spring AI集成已完成基础架构：

🎯 **双重实现策略**：Spring AI + 自定义Redis，确保稳定性  
🎯 **文档解析增强**：集成Tika Document Reader，支持多种格式  
🎯 **自动回退机制**：当Spring AI不可用时自动使用自定义实现  
🎯 **完整的错误处理**：优雅处理各种异常情况  
🎯 **性能监控**：详细的日志和指标收集  

现在可以开始测试和优化，逐步完善各项功能！
