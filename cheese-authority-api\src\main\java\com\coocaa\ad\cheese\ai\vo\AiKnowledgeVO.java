package com.coocaa.ad.cheese.ai.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AI知识库VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiKnowledgeVO", description = "AI知识库VO")
public class AiKnowledgeVO {

    @Schema(description = "知识库ID")
    private Long id;

    @Schema(description = "知识库名称", maxLength = 100)
    private String name;

    @Schema(description = "知识库描述")
    private String description;

    @Schema(description = "嵌入模型", maxLength = 50)
    private String embeddingModel;

    @Schema(description = "向量维度")
    private Integer vectorDimension;

    @Schema(description = "文档分块大小")
    private Integer chunkSize;

    @Schema(description = "分块重叠大小")
    private Integer chunkOverlap;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "文档数量")
    private Integer documentCount;

    @Schema(description = "分块数量")
    private Integer segmentCount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private Integer operator;
}
