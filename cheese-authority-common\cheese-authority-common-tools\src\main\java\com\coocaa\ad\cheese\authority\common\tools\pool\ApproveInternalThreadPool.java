package com.coocaa.ad.cheese.authority.common.tools.pool;


import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
public class ApproveInternalThreadPool {

    private static ThreadPoolExecutor threadPool;

    public static void initThreadPool() {
        // 核心线程数
        int corePoolSize = 4;
        // 最大线程数
        int maximumPoolSize = 8;
        // 非核心线程的存活时间，单位为秒
        long keepAliveTime = 60;
        // 任务队列
        BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(50);

        threadPool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS, workQueue);
    }

    public static void executeTask(Runnable task) {
        if (threadPool == null) {
            initThreadPool();
        }
        threadPool.execute(task);
    }

    public static void shutdownThreadPool() {
        if (threadPool != null) {
            threadPool.shutdown();
        }
    }

}
