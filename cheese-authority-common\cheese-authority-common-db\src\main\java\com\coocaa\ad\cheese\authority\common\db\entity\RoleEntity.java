package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.coocaa.ad.cheese.authority.common.tools.utils.ChangeFormatter;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.ChangeExtract;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 角色
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("role")
public class RoleEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     */
    @ChangeExtract(readableName = "名称")
    private String name;

    /**
     * 描述
     */
    @ChangeExtract(readableName = "描述")
    private String description;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    @ChangeExtract(readableName = "状态", formatter = ChangeFormatter.StatusFormatter.class)
    private Boolean status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;
}
