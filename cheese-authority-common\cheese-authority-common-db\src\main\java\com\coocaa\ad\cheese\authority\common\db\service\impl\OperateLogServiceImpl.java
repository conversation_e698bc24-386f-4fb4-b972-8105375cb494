package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.coocaa.ad.cheese.authority.common.db.entity.OperateLogEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.OperateLogMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IOperateLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 操作日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class OperateLogServiceImpl extends ServiceImpl<OperateLogMapper, OperateLogEntity> implements IOperateLogService {

}
