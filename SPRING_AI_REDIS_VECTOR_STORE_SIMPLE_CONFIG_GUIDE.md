# Spring AI Redis Vector Store 简化配置指南

## 概述

现在我们已经简化了配置，直接使用Spring AI自带的`RedisVectorStoreProperties`，配置更加简洁和标准化。

## 🔧 配置文件

### application-local.properties
```properties
# 禁用服务注册
spring.cloud.service-registry.auto-registration.enabled=false

# Redis配置
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.timeout=5000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# Spring AI向量存储配置
spring.ai.vectorstore.redis.initialize-schema=true
spring.ai.vectorstore.redis.index-name=knowledge_index
spring.ai.vectorstore.redis.prefix=knowledge_segment:
```

## 🏗️ 架构优化

### 1. 使用Spring AI标准配置
- ✅ 删除了自定义的`AiVectorStoreConfig`
- ✅ 直接使用`RedisVectorStoreProperties`
- ✅ 配置更加标准化和简洁

### 2. 自动配置验证
- ✅ 创建了`AiConfigurationService`来验证配置
- ✅ 应用启动时自动检查配置状态
- ✅ 提供配置信息查询接口

### 3. 智能回退机制
- ✅ 当Spring AI VectorStore不可用时，自动使用自定义Redis实现
- ✅ 配置前缀自动适配Spring AI设置
- ✅ 保证系统稳定运行

## 🧪 测试步骤

### 1. 启动应用并检查日志

启动应用后，查看控制台日志：

```log
2025-07-17 10:00:00 [main] INFO  SpringAiConfig - Spring AI配置类已加载
2025-07-17 10:00:01 [main] INFO  AiConfigurationService - === AI配置验证开始 ===
2025-07-17 10:00:01 [main] INFO  AiConfigurationService - ✅ RedisVectorStoreProperties配置已加载:
2025-07-17 10:00:01 [main] INFO  AiConfigurationService -    - 索引名称: knowledge_index
2025-07-17 10:00:01 [main] INFO  AiConfigurationService -    - 前缀: knowledge_segment:
2025-07-17 10:00:01 [main] INFO  AiConfigurationService -    - 初始化Schema: true
2025-07-17 10:00:01 [main] INFO  AiConfigurationService - ℹ️ VectorStore Bean未创建，将使用自定义Redis实现
2025-07-17 10:00:01 [main] INFO  AiConfigurationService - ℹ️ EmbeddingModel Bean未创建，将使用自定义嵌入模型工厂
2025-07-17 10:00:01 [main] INFO  AiConfigurationService - === AI配置验证完成 ===
```

### 2. 检查配置状态

访问配置检查接口：

```bash
curl -X GET http://localhost:8080/sys/ai/knowledge/config
```

**预期响应：**
```json
{
  "code": 200,
  "data": {
    "vectorStoreConfigured": true,
    "indexName": "knowledge_index",
    "prefix": "knowledge_segment:",
    "initializeSchema": true,
    "vectorStoreAvailable": false,
    "embeddingModelAvailable": false
  },
  "message": "success"
}
```

### 3. 测试文档上传

```bash
curl -X POST http://localhost:8080/sys/ai/knowledge/upload/text \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeId": 1,
    "name": "Spring AI配置测试",
    "content": "这是一个测试Spring AI Redis向量存储配置的文档。配置包括索引名称knowledge_index和前缀knowledge_segment:。",
    "processImmediately": true
  }'
```

### 4. 检查Redis数据

```bash
# 连接Redis
redis-cli

# 查看向量数据（使用新的前缀）
KEYS knowledge_segment:*

# 查看具体的向量数据
HGETALL knowledge_segment:kb_1_doc_123_seg_456
```

### 5. 测试搜索功能

```bash
curl -X POST http://localhost:8080/sys/ai/knowledge/search \
  -H "Content-Type: application/json" \
  -d '{
    "knowledgeId": 1,
    "query": "Spring AI配置",
    "topK": 5,
    "threshold": 0.7
  }'
```

## 📋 配置说明

### Spring AI配置属性

| 属性 | 说明 | 默认值 |
|------|------|--------|
| `spring.ai.vectorstore.redis.initialize-schema` | 是否初始化Redis Schema | `false` |
| `spring.ai.vectorstore.redis.index-name` | Redis向量索引名称 | `default-index` |
| `spring.ai.vectorstore.redis.prefix` | 向量数据键前缀 | `vector:` |

### 配置验证

`AiConfigurationService`会在应用启动时验证：

1. **RedisVectorStoreProperties**：检查配置是否正确加载
2. **VectorStore Bean**：检查Spring AI VectorStore是否可用
3. **EmbeddingModel Bean**：检查嵌入模型是否配置

### 智能回退

当Spring AI组件不可用时，系统会：

1. **使用自定义Redis实现**：保证向量存储功能正常
2. **使用自定义嵌入模型工厂**：支持多种嵌入模型
3. **保持配置一致性**：使用相同的前缀和索引名称

## 🔍 故障排除

### 1. 配置未生效

如果配置没有生效，检查：

```bash
# 检查配置文件是否正确
cat cheese-authority-api/src/main/resources/application-local.properties

# 检查Spring Profile
echo $SPRING_PROFILES_ACTIVE

# 检查应用启动参数
ps aux | grep java
```

### 2. Redis连接问题

```bash
# 测试Redis连接
redis-cli ping

# 检查Redis配置
redis-cli config get "*"

# 查看Redis日志
tail -f /var/log/redis/redis-server.log
```

### 3. 向量数据问题

```bash
# 查看所有向量键
redis-cli KEYS "knowledge_segment:*"

# 查看索引信息
redis-cli FT.INFO knowledge_index

# 清理测试数据
redis-cli FLUSHDB
```

## 🚀 下一步优化

### 1. 集成真实嵌入模型

添加OpenAI配置：

```properties
# OpenAI配置
spring.ai.openai.api-key=${OPENAI_API_KEY}
spring.ai.openai.embedding.model=text-embedding-ada-002
```

### 2. 启用Spring AI VectorStore

当嵌入模型配置完成后，Spring AI会自动创建VectorStore Bean。

### 3. 性能监控

添加监控配置：

```properties
# 监控配置
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always
```

## 📊 配置对比

### 简化前 vs 简化后

| 方面 | 简化前 | 简化后 |
|------|--------|--------|
| 配置文件 | 复杂的YAML配置 | 简单的properties配置 |
| 配置类 | 自定义AiVectorStoreConfig | 使用Spring AI标准配置 |
| 维护性 | 需要手动维护配置映射 | 自动配置，无需维护 |
| 兼容性 | 可能与Spring AI不兼容 | 完全兼容Spring AI标准 |
| 扩展性 | 需要手动添加新配置 | 自动支持Spring AI新特性 |

## 总结

通过使用Spring AI自带的`RedisVectorStoreProperties`，我们实现了：

🎯 **配置简化**：从复杂的自定义配置简化为标准的Spring AI配置  
🎯 **标准兼容**：完全兼容Spring AI的配置规范  
🎯 **自动验证**：应用启动时自动验证配置状态  
🎯 **智能回退**：当Spring AI不可用时自动使用自定义实现  
🎯 **易于维护**：减少了自定义配置代码，提高了可维护性  

现在系统配置更加简洁和标准化，为后续集成更多Spring AI功能打下了良好基础！
