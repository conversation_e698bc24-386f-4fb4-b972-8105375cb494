package com.coocaa.ad.cheese.ai.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.mapper.AiKnowledgeDocumentMapper;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeDocumentService;
import org.springframework.stereotype.Service;

/**
 * AI知识库文档服务实现
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
public class AiKnowledgeDocumentServiceImpl extends ServiceImpl<AiKnowledgeDocumentMapper, AiKnowledgeDocumentEntity> 
        implements IAiKnowledgeDocumentService {
}
