package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * AI用户信息请求VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Schema(description = "AI用户信息请求")
public class AiUserInfoReqVO {

    @Schema(description = "用户OpenID", required = true)
    @NotBlank(message = "OpenID不能为空")
    private String openId;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像URL")
    private String avatarUrl;

    @Schema(description = "用户性别，0-未知，1-男，2-女")
    private Integer gender;

    @Schema(description = "用户所在国家")
    private String country;

    @Schema(description = "用户所在省份")
    private String province;

    @Schema(description = "用户所在城市")
    private String city;

    @Schema(description = "用户语言")
    private String language;

    @Schema(description = "用户UnionID")
    private String unionId;
}
