package com.coocaa.ad.cheese.ai.common.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI对话实体
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@TableName("ai_conversation")
public class AiConversationEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID（关联ai_user表的id）
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 角色ID（关联ai_role表的id）
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 删除标记（0-未删除，1-已删除）
     */
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;
}
