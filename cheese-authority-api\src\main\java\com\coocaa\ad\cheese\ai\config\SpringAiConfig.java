package com.coocaa.ad.cheese.ai.config;

import com.coocaa.ad.cheese.ai.embedding.DoubaoEmbeddingModel;
import com.coocaa.ad.cheese.ai.embedding.DoubaoEmbeddingOptions;
import com.coocaa.ad.cheese.ai.service.DoubaoAiService;
import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.redis.RedisVectorStore;
import org.springframework.ai.vectorstore.redis.autoconfigure.RedisVectorStoreProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * Spring AI配置类
 *
 * 使用Spring AI自带的RedisVectorStoreProperties配置
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Configuration
@Slf4j
@EnableConfigurationProperties(RedisVectorStoreProperties.class)
public class SpringAiConfig {

    /**
     * 配置Redis向量存储
     * 使用Spring AI自带的配置属性
     */
    @Bean
    @ConditionalOnClass(VectorStore.class)
    @ConditionalOnMissingBean(VectorStore.class)
    @ConditionalOnBean(EmbeddingModel.class)
    @ConditionalOnProperty(prefix = "spring.ai.vectorstore.redis", name = "initialize-schema", havingValue = "true")
    public VectorStore redisVectorStore(EmbeddingModel embeddingModel,
                                       RedisVectorStoreProperties properties,
                                       RedisTemplate<String, Object> redisTemplate) {
        log.info("配置Spring AI Redis向量存储，索引名称: {}, 前缀: {}",
                properties.getIndexName(), properties.getPrefix());

        try {
            // 注意：RedisVectorStore的API可能需要不同的参数
            // 这里先返回null，让系统使用自定义实现
            // 当Spring AI API稳定后再更新
            log.info("Spring AI Redis向量存储配置已准备，但API需要调整，当前使用自定义实现");
            return null;
        } catch (Exception e) {
            log.warn("Redis向量存储配置失败，将使用自定义实现: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 配置豆包嵌入模型
     * 当没有其他EmbeddingModel Bean时创建
     */
    @Bean
    @ConditionalOnMissingBean(EmbeddingModel.class)
    @ConditionalOnBean(DoubaoAiService.class)
    public EmbeddingModel doubaoEmbeddingModel(DoubaoAiService doubaoAiService) {
        log.info("配置豆包嵌入模型");

        try {
            // 从DoubaoAiService获取ArkService
            ArkService arkService = doubaoAiService.getArkService();
            if (arkService == null) {
                log.warn("ArkService未初始化，无法创建豆包嵌入模型");
                return null;
            }

            DoubaoEmbeddingOptions options = DoubaoEmbeddingOptions.builder()
                    .model("doubao-embedding-vision-250615")
                    .dimensions(3072) // 豆包多模态模型默认维度
                    .encodingFormat("float")
                    .multimodal(true) // 启用多模态支持
                    .build();

            return new DoubaoEmbeddingModel(arkService, org.springframework.ai.document.MetadataMode.EMBED, options);

        } catch (Exception e) {
            log.warn("豆包嵌入模型配置失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 当没有VectorStore Bean时，返回null
     * 这样AiVectorService会使用自定义Redis实现
     */
    @Bean
    @ConditionalOnMissingBean(VectorStore.class)
    public VectorStore nullVectorStore() {
        log.info("没有配置VectorStore，将使用自定义Redis实现");
        return null;
    }

    public SpringAiConfig() {
        log.info("Spring AI配置类已加载");
    }
}
