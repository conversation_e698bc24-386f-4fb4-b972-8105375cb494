package com.coocaa.ad.cheese.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

/**
 * Spring AI配置类
 * 
 * 当Spring AI依赖可用时，自动配置相关Bean
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Configuration
@Slf4j
public class SpringAiConfig {

    // 当Spring AI依赖可用时，这里可以配置相关的Bean
    // 例如：VectorStore, EmbeddingClient等
    
    /*
    @Bean
    @ConditionalOnClass(name = "org.springframework.ai.vectorstore.VectorStore")
    public VectorStore redisVectorStore(RedisTemplate<String, Object> redisTemplate,
                                       EmbeddingClient embeddingClient) {
        // 配置Redis向量存储
        return RedisVectorStore.builder(redisTemplate, embeddingClient)
                .indexName("ai_knowledge_index")
                .prefix("ai:vector:")
                .initializeSchema(true)
                .build();
    }

    @Bean
    @ConditionalOnClass(name = "org.springframework.ai.embedding.EmbeddingClient")
    public EmbeddingClient embeddingClient() {
        // 配置嵌入模型客户端
        // 可以是OpenAI、HuggingFace等
        return new OpenAiEmbeddingClient(openAiApi);
    }
    */

    public SpringAiConfig() {
        log.info("Spring AI配置类已加载，等待依赖可用时自动配置");
    }
}
