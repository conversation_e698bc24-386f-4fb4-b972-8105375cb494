package com.coocaa.ad.cheese.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring AI配置类
 *
 * 当Spring AI依赖可用时，自动配置相关Bean
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Configuration
@Slf4j
public class SpringAiConfig {

    /**
     * 配置Redis向量存储
     * 当VectorStore类可用且没有其他VectorStore Bean时创建
     */
    @Bean
    @ConditionalOnClass(VectorStore.class)
    @ConditionalOnMissingBean(VectorStore.class)
    @ConditionalOnBean(EmbeddingModel.class)
    public VectorStore redisVectorStore(EmbeddingModel embeddingModel) {
        log.info("配置Redis向量存储");

        try {
            // 注意：这里需要根据实际的Spring AI Redis Vector Store API来配置
            // 当前版本可能需要不同的配置方式
            log.warn("Redis向量存储配置需要根据实际API调整，当前返回null使用自定义实现");
            return null;
        } catch (Exception e) {
            log.warn("Redis向量存储配置失败，将使用自定义实现: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 当没有EmbeddingModel Bean时，返回null
     * 这样AiVectorService会使用自定义的嵌入模型工厂
     */
    @Bean
    @ConditionalOnMissingBean(EmbeddingModel.class)
    public EmbeddingModel nullEmbeddingModel() {
        log.info("没有配置EmbeddingModel，将使用自定义嵌入模型工厂");
        return null;
    }

    /**
     * 当没有VectorStore Bean时，返回null
     * 这样AiVectorService会使用自定义Redis实现
     */
    @Bean
    @ConditionalOnMissingBean(VectorStore.class)
    public VectorStore nullVectorStore() {
        log.info("没有配置VectorStore，将使用自定义Redis实现");
        return null;
    }

    public SpringAiConfig() {
        log.info("Spring AI配置类已加载");
    }
}
