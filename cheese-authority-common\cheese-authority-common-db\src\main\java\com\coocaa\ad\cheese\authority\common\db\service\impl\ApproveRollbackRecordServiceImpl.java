package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.ApproveRollbackRecordEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.ApproveRollbackRecordMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IApproveAppInfoService;
import com.coocaa.ad.cheese.authority.common.db.service.IApproveRollbackRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-20
 */
@Service
public class ApproveRollbackRecordServiceImpl extends ServiceImpl<ApproveRollbackRecordMapper, ApproveRollbackRecordEntity> implements IApproveRollbackRecordService {

}
