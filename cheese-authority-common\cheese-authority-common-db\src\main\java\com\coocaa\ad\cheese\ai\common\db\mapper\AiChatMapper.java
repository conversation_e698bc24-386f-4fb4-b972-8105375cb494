package com.coocaa.ad.cheese.ai.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.ai.common.db.bean.AiChatDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiChatEntity;
import org.apache.ibatis.annotations.Param;

/**
 * AI Chat 消息接口
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
public interface AiChatMapper extends BaseMapper<AiChatEntity> {

    /**
     * 按条件查询AI Chat 消息列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return AI Chat 消息列表
     */
    IPage<AiChatEntity> pageList(@Param("page") IPage<AiChatEntity> page, @Param("condition") AiChatDTO condition);
}