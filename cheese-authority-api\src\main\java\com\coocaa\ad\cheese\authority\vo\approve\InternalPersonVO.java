package com.coocaa.ad.cheese.authority.vo.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
public class InternalPersonVO {

    private Integer id;

    /** 规则表id */
    @Schema(description = "规则表id")
    private Integer ruleId;

    /** 审核人员排序 */
    @Schema(description = "排序")
    private Integer rank;

    /** 审批人员ID */
    @Schema(description = "审批人员ID")
    private String userId;

    @Schema(description = "审批人员姓名")
    private String userName;

    @Schema(description = "审批人员姓名")
    private String wno;

    @Schema(description = "任务名称")
    private String taskName;
}
