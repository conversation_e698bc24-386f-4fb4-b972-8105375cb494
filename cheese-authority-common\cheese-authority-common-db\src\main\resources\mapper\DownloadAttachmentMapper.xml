<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.DownloadAttachmentMapper">
    <!-- 按条件查询列表 -->
    <select id="pageList" resultType="com.coocaa.ad.cheese.authority.common.db.entity.DownloadAttachmentEntity">
        SELECT do.* 
        FROM download_attachment do
        <where>
            AND do.delete_flag = 0
        </where>
        ORDER BY do.update_time DESC
    </select>
</mapper>