package com.coocaa.ad.cheese.authority.common.db.service.approve;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRulePersonEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IApproveInternalRulePersonService extends IService<ApproveInternalRulePersonEntity> {

    /**
     * 查询规则审批人
     */
    List<ApproveInternalRulePersonEntity> getPersonByRuleId(Integer ruleId);

    void updateAfterRank(Integer ruleId, Integer rank);
}
