
package com.coocaa.ad.cheese.ai.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI Chat 消息
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Data
@TableName("ai_chat")
public class AiChatEntity implements Serializable {

    /**
     * 编号，作为每条聊天记录的唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对话编号
     */
    private Long conversationId;

    /**
     * 回复编号
     */
    private Long replyId;

    /**
     * 用户openId
     */
    private String openId;

    /**
     * 消息类型：user-用户消息，assistant-AI助手消息
     */
    private String messageType;

    /**
     * 聊天内容
     */
    private String content;

    /**
     * 是否携带上下文
     */
    private Integer userContext;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 删除标记 0正常 1删除
     */
    private Integer deleteFlag;
}