package com.coocaa.ad.cheese.authority.common.tools.query.enums;

/**
 * 类OrderType实现描述:
 *
 * <AUTHOR>
 * @date 2022年05月08 12:20
 */
public enum OrderType {

    /**
     * 正序
     */
    ASC("ASC"),
    /**
     * 倒序
     */
    DESC("DESC");

    private String name;

    OrderType(String type) {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
