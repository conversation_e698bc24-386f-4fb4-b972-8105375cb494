package com.coocaa.ad.cheese.authority.common.db.entity.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "message_app_config")
public class MessageAppConfigEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用类型code，字典0124
     */
    private String appCode;

    /**
     * 飞书应用ID
     */
    private String appId;

    /**
     * 飞书应用密钥
     */
    private String appSecret;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 是否启用，1：启用，0：未启用
     */
    private Integer flag;
}
