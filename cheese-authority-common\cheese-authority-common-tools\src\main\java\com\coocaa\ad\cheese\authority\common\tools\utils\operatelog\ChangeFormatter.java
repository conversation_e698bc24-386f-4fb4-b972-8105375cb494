package com.coocaa.ad.cheese.authority.common.tools.utils.operatelog;

import org.apache.commons.lang3.StringUtils;
import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-27
 */
@FunctionalInterface
public interface ChangeFormatter {
    DateFormat DATE_FORMAT = new SimpleDateFormat(SysConstant.DATE_TIME_FORMAT);
    String DEFAULT_OUTPUT_STRING = "-";

    /**
     * 将对象格式化成字符
     *
     * @param value 需要格式化的对象
     * @return 字符串
     */
    String format(Object value);

    /**
     * 默认格式器
     */
    class DefaultFormatter implements ChangeFormatter {
        @Override
        public String format(Object value) {
            if (Objects.isNull(value)) return DEFAULT_OUTPUT_STRING;
            if (value instanceof String) return (String) value;
            if (value instanceof Date) return DATE_FORMAT.format(value);
            return Objects.toString(value, DEFAULT_OUTPUT_STRING);
        }
    }


    /**
     * 状态值格式化器
     */
    class StatusFormatter extends BooleanFormatter {
        public StatusFormatter() {
            super("启用", "禁用");
        }
    }

    /**
     * 删除格式化器
     */
    class DeleteFormatter extends BooleanFormatter {
        public DeleteFormatter() {
            super("已删除", "未删除");
        }
    }

    /**
     * 布尔类型数据格式化器
     */
    class BooleanFormatter implements ChangeFormatter {
        private final String trueValue;
        private final String falseValue;

        public BooleanFormatter(String trueValue, String falseValue) {
            this.trueValue = trueValue;
            this.falseValue = falseValue;
        }

        @Override
        public String format(Object value) {
            if (Objects.isNull(value)) return DEFAULT_OUTPUT_STRING;
            if (value instanceof Boolean) return Objects.equals(value, Boolean.TRUE) ? trueValue : falseValue;
            if (value instanceof Integer) return Objects.equals(value, 1) ? trueValue : falseValue;
            if (value instanceof String)
                return StringUtils.equalsIgnoreCase((String) value, "on") ? trueValue : falseValue;
            return Objects.toString(value, DEFAULT_OUTPUT_STRING);
        }
    }
}
