package com.coocaa.ad.cheese.authority.common.tools.utils.operatelog;

import cn.hutool.core.util.ObjectUtil;
import com.coocaa.ad.cheese.authority.common.tools.utils.ReflectUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 提取变更信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-24
 */
@Slf4j
public final class ChangeExtractor {
    /**
     * 变化的数据输出格式器
     */
    private static final Map<Class<? extends ChangeFormatter>, ChangeFormatter> CHANGED_VALUE_FORMATTER_MAP = Maps.newConcurrentMap();

    /**
     * 提供两个对象的变更信息
     *
     * @param source 老数据
     * @param target 新数据
     * @return 变更后的信息
     */
    public static <S, T> String extract(S source, T target) {
        if (Objects.isNull(source) || Objects.isNull(target)) {
            return StringUtils.EMPTY;
        }

        // 变更的字段信息
        List<ChangedItem> changedItems = Lists.newArrayList();

        // 获取可读/写方法
        Map<String, Method> sourceReadableMethodMap = ReflectUtils.getReadableMethods(source);
        Map<String, Method> targetReadableMethodMap = ReflectUtils.getReadableMethods(target);

        Object sourceFieldValue = null, targetFieldValue = null;
        for (Field field : ReflectUtils.getAllFields(source)) {
            ChangeExtract annotation = field.getAnnotation(ChangeExtract.class);
            if (Objects.isNull(annotation)) {
                log.debug("字段({})不需要比较", field.getName());
                continue;
            }

            // 检查输入对象的字段是否有数据
            String sourceFieldName = field.getName().toLowerCase();
            Method sourceReadMethod = sourceReadableMethodMap.get(sourceFieldName);

            String targetFieldName = (StringUtils.isNotBlank(annotation.targetFieldName())
                    ? annotation.targetFieldName()
                    : field.getName()).toLowerCase();
            Method targetReadMethod = targetReadableMethodMap.get(targetFieldName);
            if (Objects.isNull(sourceReadMethod) || Objects.isNull(targetReadMethod)) {
                log.debug("字段({})没有可操作的方法", field.getName());
                continue;
            }

            // 获取输入和要比较对象的值
            try {
                sourceFieldValue = sourceReadMethod.invoke(source);
                targetFieldValue = targetReadMethod.invoke(target);
            } catch (IllegalAccessException | InvocationTargetException ex) {
                log.warn("获取字段({})值失败", field.getName());
                continue;
            }

            // 不需要强制比较，目标对象的属性为空，不需要记录
            if (!annotation.force() && Objects.isNull(targetFieldValue)) {
                log.debug("目标字段({})没有值", field.getName());
                continue;
            }

            // 对两个数据进行比较
            if (ObjectUtil.isEmpty(sourceFieldValue) || test(sourceFieldValue, targetFieldValue)) {
                log.debug("字段({})的值({})相同", field.getName(), targetFieldValue);
                continue;
            }

            // 调用格式化器
            ChangeFormatter formatter = CHANGED_VALUE_FORMATTER_MAP.get(annotation.formatter());
            if (Objects.isNull(formatter)) {
                try {
                    formatter = annotation.formatter().newInstance();
                    CHANGED_VALUE_FORMATTER_MAP.put(annotation.formatter(), formatter);
                } catch (InstantiationException | IllegalAccessException ex) {
                    log.warn("创建格式化器 {} 失败!", annotation.formatter().getName());
                }
            }

            // 生成变更的对象
            String oldValue = Objects.isNull(formatter) ? Objects.toString(targetFieldValue) : formatter.format(targetFieldValue);
            String newValue = Objects.isNull(formatter) ? Objects.toString(sourceFieldValue) : formatter.format(sourceFieldValue);
            if (StringUtils.equalsIgnoreCase(oldValue, newValue)) {
                continue;
            }

            // 记录变化信息
            changedItems.add(new ChangedItem(getFieldReadableName(field), oldValue, newValue));
        }

        return format(changedItems);
    }


    /**
     * 格式化合同变更内容
     *
     * @param changedItems 变更内容
     * @return 格式化后的字符串列表
     */
    private static String format(List<ChangedItem> changedItems) {
        if (CollectionUtils.isEmpty(changedItems)) {
            return StringUtils.EMPTY;
        }

        // 格式化字符串
        Function<ChangedItem, String> formatItem = item -> {
            // return String.format("[%s]从(%s)改为(%s)", item.getFieldName(), item.getBeforeChanged(), item.getAfterChanged());
            return String.format("(%s)改为(%s)", item.getFieldName(), item.getAfterChanged());
        };

        return changedItems.stream()
                .map(formatItem)
                .collect(Collectors.joining(", "));
    }


    /**
     * 获取字段的中文名称
     *
     * @param field 字段
     * @return 可读的中文名称
     */
    private static String getFieldReadableName(Field field) {
        String readableName = Optional.ofNullable(field.getAnnotation(ChangeExtract.class))
                .map(ChangeExtract::readableName).orElse(StringUtils.EMPTY);
        return StringUtils.isBlank(readableName) ? field.getName() : readableName;
    }

    /**
     * 比较两对象是否相等
     */
    private static boolean test(Object a, Object b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }

        if (a instanceof String) {
            return StringUtils.equalsIgnoreCase((String) a, (String) b);
        }

        if (a instanceof Date) {
            return a.equals(b);
        }

        return Objects.equals(a, b);
    }
}
