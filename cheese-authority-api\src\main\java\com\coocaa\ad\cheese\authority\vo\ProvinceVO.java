package com.coocaa.ad.cheese.authority.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: cheese-authority
 * @ClassName PprovinceVO
 * @description:
 * @author: z<PERSON><PERSON><PERSON>n
 * @create: 2025-03-25 16:40
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProvinceVO {

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 省编码
     */
    private String provinceBzCode;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 市名称
     */
    private String cityName;

    /**
     * 市编码
     */
    private String cityBzCode;


}
