package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-24
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OperateLogVO {
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "系统名称", type = "String", example = "权限系统")
    private String resourceCode;
    private String resourceName;

    @Schema(description = "功能", type = "String", example = "增加用户")
    private String function;

    @Schema(description = "实体", type = "String", example = "用户")
    private String typeCode;
    private String typeName;

    @Schema(description = "实体ID", type = "Integer", example = "1")
    private Integer targetId;

    @Schema(description = "操作内容", type = "String", example = "新增用户(管理员)")
    private String content;

    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "Date", example = "2024.10.22 15:26:37")
    private Date createTime;

    @Schema(description = "创建人", type = "String", example = "权限系统")
    private Integer creator;
    private String creatorName;
}
