package com.coocaa.ad.cheese.authority.common.tools.query.params;


import com.coocaa.ad.cheese.authority.common.tools.query.enums.OrderType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 类IOrderBy实现描述:
 *
 * <AUTHOR>
 * @date 2022年05月08 12:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class IOrderBy {

    private String orderByName;
    private OrderType orderByType;

}
