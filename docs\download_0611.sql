-- 异步下载任务
CREATE TABLE `download_attachment`
(
    `id`          BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id`     BIGINT(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '下载任务ID',
    `name`        VARCHAR(100)        NOT NULL DEFAULT '' COMMENT '附件名称',
    `url`         VARCHAR(200)        NOT NULL DEFAULT '' COMMENT '附件全路径',
    `size`        BIGINT(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '大小(字节)',
    `file_type`   VARCHAR(10)         NOT NULL DEFAULT '' COMMENT '文件类型 (pdf, doc,...)',
    `delete_flag` TINYINT(2) UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `creator`     INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time` DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_task` (`task_id`)
) ENGINE = InnoDB COMMENT ='下载附件';


CREATE TABLE `download_task`
(
    `id`             BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `sys_type`       VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '系统类型',
    `type`           VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '任务类型',
    `name`           VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '任务名称',
    `category`       VARCHAR(50)         NOT NULL DEFAULT '' COMMENT '任务类别',
    `status`         VARCHAR(20)         NOT NULL DEFAULT '' COMMENT '状态',
    `execute_params` TEXT                         DEFAULT NULL COMMENT '执行时的参数',
    `fail_msg`       TEXT                         DEFAULT NULL COMMENT '失败原因',
    `delete_flag`    TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记  [0:否, 1:是]',
    `version`        INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '版本号',
    `count`          INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '下载次数',
    `creator`        INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `create_time`    DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `operator`       INT(11) UNSIGNED             DEFAULT '0' COMMENT '最后修改人',
    `update_time`    DATETIME                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_sys_type` (`sys_type`)
) ENGINE = InnoDB COMMENT ='下载任务表';


INSERT INTO dict (parent_code, code, name, `rank`, status)
VALUES ('0', '0155', '下载中心-系统类型', 0, 1),
       ('0155', '0155-1', '销售工作台', 1, 1),
       ('0155', '0155-2', '媒资工作台', 2, 1);

INSERT INTO dict (parent_code, code, name, `rank`, status)
VALUES ('0', '0156', '下载中心-任务类别', 0, 1),
       ('0156', '0156-1', '下载任务', 1, 1);

INSERT INTO dict (parent_code, code, name, `rank`, status)
VALUES ('0', '0157', '下载中心-任务状态', 0, 1),
       ('0157', '0157-1', '待执行', 1, 1),
       ('0157', '0157-2', '执行中', 2, 1),
       ('0157', '0157-3', '已完成', 3, 1),
       ('0157', '0157-4', '执行失败', 4, 1),
       ('0157', '0157-5', '已下载', 5, 1);