package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.ApproveStatusChangeLogEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.ApproveStatusChangeLogMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IApproveStatusChangeLogService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class ApproveStatusChangeLogServiceImpl extends ServiceImpl<ApproveStatusChangeLogMapper, ApproveStatusChangeLogEntity> implements IApproveStatusChangeLogService {
}
