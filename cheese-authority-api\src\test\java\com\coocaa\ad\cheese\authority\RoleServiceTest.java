package com.coocaa.ad.cheese.authority;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.ad.cheese.authority.common.db.entity.RoleEntity;
import com.coocaa.ad.cheese.authority.common.db.service.IRoleService;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import com.coocaa.ad.cheese.authority.service.RoleService;
import com.coocaa.ad.cheese.authority.vo.RoleAddVO;
import com.coocaa.ad.cheese.authority.vo.RoleEditVO;
import com.coocaa.ad.cheese.authority.vo.RoleVO;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-28
 */
@DisplayName("角色管理")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class RoleServiceTest extends BaseTest {
    @Autowired
    private RoleService sysRoleService;

    @Autowired
    private IRoleService roleService;


    @Test
    @DisplayName("资源新增/修改/禁用")
    public void testCreateAndUpdateRole() {
        RoleVO role = generateRole(vo -> {
            vo.setName("用于测试的角色");
            vo.setDescription(RandomStringUtils.randomAlphabetic(32));
        });

        RoleAddVO roleAdd = new RoleAddVO();
        roleAdd.setName(role.getName());
        roleAdd.setDescription(role.getDescription());
        ResultTemplate<Boolean> result = sysRoleService.createRole(roleAdd);
        Assertions.assertNotNull(result.getSuccess(), "创建成功");
        Assertions.assertTrue(result.getData(), "创建角色");

        // 创建后查出来检查
        RoleEntity entity = roleService.getOne(Wrappers.<RoleEntity>lambdaQuery().eq(RoleEntity::getName, role.getName()));
        Assertions.assertEquals(role.getName(), entity.getName(), "名称");
        Assertions.assertEquals(role.getDescription(), entity.getDescription(), "描述");
        Assertions.assertEquals(role.getStatus(), entity.getStatus(), "状态");


        // 修改数据
        RoleEditVO updateRole = new RoleEditVO();
        updateRole.setId(entity.getId());
        updateRole.setName("用于测试的角色-修改");
        updateRole.setDescription(RandomStringUtils.randomAlphabetic(32));
        result = sysRoleService.updateRole(updateRole);
        Assertions.assertNotNull(result.getSuccess(), "修改成功");
        Assertions.assertTrue(result.getData(), "修改资源");


        // 将修改后的信息查出来检查
        entity = roleService.getById(entity.getId());
        Assertions.assertEquals(role.getName(), entity.getName(), "名称");
        Assertions.assertEquals(role.getDescription(), entity.getDescription(), "描述");
        Assertions.assertEquals(role.getStatus(), entity.getStatus(), "状态");


        // 禁用角色
        role.setId(entity.getId());
        role.setStatus(Boolean.FALSE);
        result = sysRoleService.changeStatus(role);
        Assertions.assertNotNull(result.getSuccess(), "禁用成功");
        Assertions.assertTrue(result.getData(), "禁用角色");

        // 删除测试数据
        roleService.removeById(entity.getId());
    }


    /**
     * 创建角色，自定义属性优先
     */
    private RoleVO generateRole(Consumer<RoleVO> consumer) {
        RoleVO vo = new RoleVO();
        vo.setStatus(true);
        vo.setCreateTime(NOW);
        vo.setCreator(USER_ID);
        vo.setUpdateTime(NOW);
        vo.setOperator(USER_ID);

        // 可自定义一些属性
        Optional.ofNullable(consumer).ifPresent(c -> c.accept(vo));

        return vo;
    }
}
