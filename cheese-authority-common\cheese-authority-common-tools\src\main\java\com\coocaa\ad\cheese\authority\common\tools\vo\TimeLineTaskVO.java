package com.coocaa.ad.cheese.authority.common.tools.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
public class TimeLineTaskVO {

    /**
     * 动态类型
     */
    private String type;

    /**
     * 动态类型中文
     */
    private String typeName;

    /**
     * 发生时间时间戳
     */
    private String createTimeStamp;

    /**
     * 发生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 理由
     */
    private String comment;

    /**
     * 飞书用户ID
     */
    private String feiShuUserId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 节点唯一标识
     */
    private String nodeKey;

}
