package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeSegmentEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeDocumentService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeSegmentService;
import com.coocaa.ad.cheese.ai.config.RedisVectorConfig;
import com.coocaa.ad.cheese.ai.model.VectorDocument;
import com.coocaa.ad.cheese.ai.model.VectorSearchResult;
import com.coocaa.ad.cheese.ai.vo.AiKnowledgeSearchReqVO;
import com.coocaa.ad.cheese.ai.vo.AiKnowledgeSearchRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI向量服务 - 基于Spring AI Redis VectorStore实现
 *
 * 参考yudao-module-ai-server的实现，使用Spring AI框架：
 * - 文本向量化
 * - 向量存储和检索
 * - 相似度搜索
 * - 批量操作
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiVectorService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisVectorConfig vectorConfig;
    private final IAiKnowledgeDocumentService documentService;
    private final IAiKnowledgeSegmentService segmentService;
    private final AiEmbeddingModelFactory embeddingModelFactory;

    // Spring AI组件（可选，当配置可用时注入）
    private final VectorStore vectorStore;
    private final EmbeddingModel embeddingModel;

    /**
     * 对文档分块进行向量化
     *
     * @param segments 分块列表
     */
    public void vectorizeSegments(List<AiKnowledgeSegmentEntity> segments) {
        log.info("开始对分块进行向量化，分块数量: {}", segments.size());

        try {
            if (vectorStore != null) {
                // 使用Spring AI VectorStore
                vectorizeWithSpringAI(segments);
            } else {
                // 使用自定义Redis实现
                vectorizeWithCustomRedis(segments);
            }

            log.info("所有分块向量化完成，成功处理: {}个", segments.size());

        } catch (Exception e) {
            log.error("分块向量化失败", e);
            throw new RuntimeException("向量化处理失败: " + e.getMessage());
        }
    }

    /**
     * 使用Spring AI VectorStore进行向量化
     */
    private void vectorizeWithSpringAI(List<AiKnowledgeSegmentEntity> segments) {
        List<Document> documents = new ArrayList<>();

        for (AiKnowledgeSegmentEntity segment : segments) {
            // 1. 生成向量ID
            String vectorId = generateVectorId(segment.getKnowledgeId(), segment.getDocumentId(), segment.getId());

            // 2. 创建Spring AI Document
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("knowledgeId", segment.getKnowledgeId());
            metadata.put("documentId", segment.getDocumentId());
            metadata.put("segmentId", segment.getId());
            metadata.put("wordCount", segment.getWordCount());
            metadata.put("position", segment.getPosition());
            metadata.put("createTime", System.currentTimeMillis());

            Document document = new Document(vectorId, segment.getContent(), metadata);
            documents.add(document);

            // 3. 更新分块的向量ID和状态
            segment.setVectorId(vectorId);
            segment.setStatus("vectorized");
            segmentService.updateById(segment);

            log.info("分块向量化完成，分块ID: {}, 向量ID: {}", segment.getId(), vectorId);
        }

        // 4. 批量存储到VectorStore
        vectorStore.add(documents);
    }

    /**
     * 使用自定义Redis实现进行向量化
     */
    private void vectorizeWithCustomRedis(List<AiKnowledgeSegmentEntity> segments) {
        for (AiKnowledgeSegmentEntity segment : segments) {
            // 1. 生成向量ID
            String vectorId = generateVectorId(segment.getKnowledgeId(), segment.getDocumentId(), segment.getId());

            // 2. 调用嵌入模型生成向量
            float[] vector = embedText(segment.getContent());

            // 3. 创建向量文档
            VectorDocument vectorDoc = new VectorDocument()
                    .setVectorId(vectorId)
                    .setKnowledgeId(segment.getKnowledgeId())
                    .setDocumentId(segment.getDocumentId())
                    .setSegmentId(segment.getId())
                    .setContent(segment.getContent())
                    .setVector(vector)
                    .setWordCount(segment.getWordCount())
                    .setPosition(segment.getPosition())
                    .setCreateTime(System.currentTimeMillis());

            // 4. 存储到Redis
            storeVectorDocument(vectorDoc);

            // 5. 更新分块的向量ID和状态
            segment.setVectorId(vectorId);
            segment.setStatus("vectorized");
            segmentService.updateById(segment);

            log.info("分块向量化完成，分块ID: {}, 向量ID: {}", segment.getId(), vectorId);
        }
    }

    /**
     * 搜索相似的文档分块
     *
     * @param searchReq 搜索请求
     * @return 搜索结果
     */
    public List<AiKnowledgeSearchRespVO.SearchResult> searchSimilarSegments(AiKnowledgeSearchReqVO searchReq) {
        log.info("搜索相似分块，知识库ID: {}, 查询: {}, TopK: {}",
                searchReq.getKnowledgeId(), searchReq.getQuery(), searchReq.getTopK());

        try {
            if (vectorStore != null) {
                // 使用Spring AI VectorStore搜索
                return searchWithSpringAI(searchReq);
            } else {
                // 使用自定义Redis搜索
                return searchWithCustomRedis(searchReq);
            }

        } catch (Exception e) {
            log.error("向量搜索失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 使用Spring AI VectorStore进行搜索
     */
    private List<AiKnowledgeSearchRespVO.SearchResult> searchWithSpringAI(AiKnowledgeSearchReqVO searchReq) {
        try {
            // 1. 构建搜索请求
            SearchRequest searchRequest = SearchRequest.builder()
                    .query(searchReq.getQuery())
                    .topK(searchReq.getTopK())
                    .similarityThreshold(searchReq.getThreshold())
                    .build();

            // 2. 执行搜索
            List<Document> searchResults = vectorStore.similaritySearch(searchRequest);

            // 3. 转换为响应格式
            List<AiKnowledgeSearchRespVO.SearchResult> results = new ArrayList<>();
            for (Document doc : searchResults) {
                Map<String, Object> metadata = doc.getMetadata();

                // 过滤知识库ID
                Long docKnowledgeId = (Long) metadata.get("knowledgeId");
                if (searchReq.getKnowledgeId() != null && !searchReq.getKnowledgeId().equals(docKnowledgeId)) {
                    continue;
                }

                // 获取文档信息
                Long documentId = (Long) metadata.get("documentId");
                AiKnowledgeDocumentEntity documentEntity = documentService.getById(documentId);
                String documentName = documentEntity != null ? documentEntity.getName() : "未知文档";

                AiKnowledgeSearchRespVO.SearchResult searchResult = new AiKnowledgeSearchRespVO.SearchResult()
                        .setSegmentId((Long) metadata.get("segmentId"))
                        .setDocumentId(documentId)
                        .setDocumentName(documentName)
                        .setContent(doc.getText()) // 使用getText()方法
                        .setScore(1.0) // Spring AI会在metadata中提供score
                        .setPosition((Integer) metadata.get("position"))
                        .setWordCount((Integer) metadata.get("wordCount"));

                results.add(searchResult);
            }

            log.info("Spring AI搜索完成，找到{}个相似结果", results.size());
            return results;

        } catch (Exception e) {
            log.error("Spring AI搜索失败，回退到自定义Redis搜索", e);
            return searchWithCustomRedis(searchReq);
        }
    }

    /**
     * 使用自定义Redis进行搜索
     */
    private List<AiKnowledgeSearchRespVO.SearchResult> searchWithCustomRedis(AiKnowledgeSearchReqVO searchReq) {
        // 1. 将查询文本向量化
        float[] queryVector = embedText(searchReq.getQuery());

        // 2. 从Redis获取知识库的所有向量文档
        List<VectorDocument> vectorDocs = getVectorDocumentsByKnowledgeId(searchReq.getKnowledgeId());

        // 3. 计算相似度并排序
        List<VectorSearchResult> searchResults = new ArrayList<>();
        for (VectorDocument doc : vectorDocs) {
            double similarity = calculateCosineSimilarity(queryVector, doc.getVector());
            if (similarity >= searchReq.getThreshold()) {
                VectorSearchResult result = new VectorSearchResult()
                        .setVectorId(doc.getVectorId())
                        .setScore(similarity)
                        .setDocument(doc)
                        .setDistance(1.0 - similarity);
                searchResults.add(result);
            }
        }

        // 4. 按相似度排序并取TopK
        searchResults.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));
        List<VectorSearchResult> topResults = searchResults.stream()
                .limit(searchReq.getTopK())
                .collect(Collectors.toList());

        // 5. 转换为响应格式
        List<AiKnowledgeSearchRespVO.SearchResult> results = new ArrayList<>();
        for (VectorSearchResult result : topResults) {
            VectorDocument doc = result.getDocument();

            // 获取文档信息
            AiKnowledgeDocumentEntity documentEntity = documentService.getById(doc.getDocumentId());
            String documentName = documentEntity != null ? documentEntity.getName() : "未知文档";

            AiKnowledgeSearchRespVO.SearchResult searchResult = new AiKnowledgeSearchRespVO.SearchResult()
                    .setSegmentId(doc.getSegmentId())
                    .setDocumentId(doc.getDocumentId())
                    .setDocumentName(documentName)
                    .setContent(doc.getContent())
                    .setScore(result.getScore())
                    .setPosition(doc.getPosition())
                    .setWordCount(doc.getWordCount());

            results.add(searchResult);
        }

        log.info("自定义Redis搜索完成，找到{}个相似结果", results.size());
        return results;
    }

    /**
     * 存储单个向量
     *
     * @param vectorId 向量ID
     * @param vector 向量数据
     * @param metadata 元数据
     */
    public void storeVector(String vectorId, float[] vector, Map<String, Object> metadata) {
        try {
            String key = vectorConfig.getDataPrefix() + vectorId;

            // 将向量和元数据存储为Hash
            Map<String, Object> vectorData = new HashMap<>(metadata);
            vectorData.put("vectorId", vectorId);
            vectorData.put("vector", vector);

            redisTemplate.opsForHash().putAll(key, vectorData);

            // 添加到知识库索引
            Long knowledgeId = (Long) metadata.get("knowledgeId");
            if (knowledgeId != null) {
                String indexKey = vectorConfig.getIndexPrefix() + knowledgeId;
                redisTemplate.opsForSet().add(indexKey, vectorId);
            }

            log.debug("向量存储成功: {}", vectorId);

        } catch (Exception e) {
            log.error("存储向量失败: {}", vectorId, e);
            throw new RuntimeException("存储向量失败: " + e.getMessage());
        }
    }

    /**
     * 删除向量
     *
     * @param vectorIds 向量ID列表
     */
    public void deleteVectors(List<String> vectorIds) {
        log.info("删除向量，向量ID数量: {}", vectorIds.size());

        try {
            for (String vectorId : vectorIds) {
                String key = vectorConfig.getDataPrefix() + vectorId;
                redisTemplate.delete(key);
                log.debug("删除向量: {}", vectorId);
            }
            log.info("向量删除完成，删除数量: {}", vectorIds.size());
        } catch (Exception e) {
            log.error("删除向量失败", e);
            throw new RuntimeException("删除向量失败: " + e.getMessage());
        }
    }

    /**
     * 文本向量化
     *
     * @param text 文本内容
     * @return 向量数组
     */
    public float[] embedText(String text) {
        log.info("文本向量化，文本长度: {}", text.length());

        try {
            // 使用嵌入模型工厂获取默认模型
            AiEmbeddingModelFactory.EmbeddingModelWrapper embeddingModel =
                    embeddingModelFactory.getDefaultEmbeddingModel();

            return embeddingModel.embed(text);

        } catch (Exception e) {
            log.error("嵌入模型调用失败，使用伪向量替代", e);
            return generatePseudoVector(text, vectorConfig.getDefaultDimension());
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成向量ID
     */
    private String generateVectorId(Long knowledgeId, Long documentId, Long segmentId) {
        return String.format("kb_%d_doc_%d_seg_%d", knowledgeId, documentId, segmentId);
    }

    /**
     * 存储向量文档到Redis
     */
    private void storeVectorDocument(VectorDocument vectorDoc) {
        try {
            String key = vectorConfig.getDataPrefix() + vectorDoc.getVectorId();

            // 将向量文档转换为Map存储
            Map<String, Object> docMap = new HashMap<>();
            docMap.put("vectorId", vectorDoc.getVectorId());
            docMap.put("knowledgeId", vectorDoc.getKnowledgeId());
            docMap.put("documentId", vectorDoc.getDocumentId());
            docMap.put("segmentId", vectorDoc.getSegmentId());
            docMap.put("content", vectorDoc.getContent());
            docMap.put("vector", vectorDoc.getVector());
            docMap.put("wordCount", vectorDoc.getWordCount());
            docMap.put("position", vectorDoc.getPosition());
            docMap.put("createTime", vectorDoc.getCreateTime());

            redisTemplate.opsForHash().putAll(key, docMap);

            // 添加到知识库索引
            String indexKey = vectorConfig.getIndexPrefix() + vectorDoc.getKnowledgeId();
            redisTemplate.opsForSet().add(indexKey, vectorDoc.getVectorId());

            log.debug("向量文档存储成功: {}", vectorDoc.getVectorId());

        } catch (Exception e) {
            log.error("存储向量文档失败: {}", vectorDoc.getVectorId(), e);
            throw new RuntimeException("存储向量文档失败: " + e.getMessage());
        }
    }

    /**
     * 根据知识库ID获取所有向量文档
     */
    private List<VectorDocument> getVectorDocumentsByKnowledgeId(Long knowledgeId) {
        try {
            String indexKey = vectorConfig.getIndexPrefix() + knowledgeId;
            Set<Object> vectorIds = redisTemplate.opsForSet().members(indexKey);

            if (vectorIds == null || vectorIds.isEmpty()) {
                return new ArrayList<>();
            }

            List<VectorDocument> vectorDocs = new ArrayList<>();
            for (Object vectorIdObj : vectorIds) {
                String vectorId = vectorIdObj.toString();
                VectorDocument doc = getVectorDocumentById(vectorId);
                if (doc != null) {
                    vectorDocs.add(doc);
                }
            }

            return vectorDocs;

        } catch (Exception e) {
            log.error("获取知识库向量文档失败，知识库ID: {}", knowledgeId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据向量ID获取向量文档
     */
    private VectorDocument getVectorDocumentById(String vectorId) {
        try {
            String key = vectorConfig.getDataPrefix() + vectorId;
            Map<Object, Object> docMap = redisTemplate.opsForHash().entries(key);

            if (docMap.isEmpty()) {
                return null;
            }

            VectorDocument doc = new VectorDocument();
            doc.setVectorId((String) docMap.get("vectorId"));
            doc.setKnowledgeId(Long.valueOf(docMap.get("knowledgeId").toString()));
            doc.setDocumentId(Long.valueOf(docMap.get("documentId").toString()));
            doc.setSegmentId(Long.valueOf(docMap.get("segmentId").toString()));
            doc.setContent((String) docMap.get("content"));
            doc.setVector((float[]) docMap.get("vector"));
            doc.setWordCount((Integer) docMap.get("wordCount"));
            doc.setPosition((Integer) docMap.get("position"));
            doc.setCreateTime(Long.valueOf(docMap.get("createTime").toString()));

            return doc;

        } catch (Exception e) {
            log.error("获取向量文档失败，向量ID: {}", vectorId, e);
            return null;
        }
    }

    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(float[] vectorA, float[] vectorB) {
        if (vectorA.length != vectorB.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;

        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += Math.pow(vectorA[i], 2);
            normB += Math.pow(vectorB[i], 2);
        }

        if (normA == 0.0 || normB == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    /**
     * 生成伪向量（用于演示，实际应该调用真实的嵌入模型）
     */
    private float[] generatePseudoVector(String text, int dimension) {
        // 使用文本哈希生成确定性的伪向量
        int hash = text.hashCode();
        Random random = new Random(hash);

        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float) (random.nextGaussian() * 0.1); // 生成正态分布的随机数
        }

        // 归一化向量
        float norm = 0.0f;
        for (float v : vector) {
            norm += v * v;
        }
        norm = (float) Math.sqrt(norm);

        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }

        return vector;
    }
}
