package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeSegmentEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeDocumentService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeSegmentService;
import com.coocaa.ad.cheese.ai.config.RedisVectorConfig;
import com.coocaa.ad.cheese.ai.model.VectorDocument;
import com.coocaa.ad.cheese.ai.model.VectorSearchResult;
import com.coocaa.ad.cheese.ai.vo.AiKnowledgeSearchReqVO;
import com.coocaa.ad.cheese.ai.vo.AiKnowledgeSearchRespVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI向量服务 - 基于Redis实现
 *
 * 使用Redis作为向量数据库，支持：
 * - 文本向量化
 * - 向量存储和检索
 * - 相似度搜索
 * - 批量操作
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiVectorService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisVectorConfig vectorConfig;
    private final IAiKnowledgeDocumentService documentService;
    private final IAiKnowledgeSegmentService segmentService;

    /**
     * 对文档分块进行向量化
     *
     * @param segments 分块列表
     */
    public void vectorizeSegments(List<AiKnowledgeSegmentEntity> segments) {
        log.info("开始对分块进行向量化，分块数量: {}", segments.size());

        try {
            for (AiKnowledgeSegmentEntity segment : segments) {
                // 1. 生成向量ID
                String vectorId = generateVectorId(segment.getKnowledgeId(), segment.getDocumentId(), segment.getId());

                // 2. 调用嵌入模型生成向量
                float[] vector = embedText(segment.getContent());

                // 3. 创建向量文档
                VectorDocument vectorDoc = new VectorDocument()
                        .setVectorId(vectorId)
                        .setKnowledgeId(segment.getKnowledgeId())
                        .setDocumentId(segment.getDocumentId())
                        .setSegmentId(segment.getId())
                        .setContent(segment.getContent())
                        .setVector(vector)
                        .setWordCount(segment.getWordCount())
                        .setPosition(segment.getPosition())
                        .setCreateTime(System.currentTimeMillis());

                // 4. 存储到Redis
                storeVectorDocument(vectorDoc);

                // 5. 更新分块的向量ID和状态
                segment.setVectorId(vectorId);
                segment.setStatus("vectorized");
                segmentService.updateById(segment);

                log.info("分块向量化完成，分块ID: {}, 向量ID: {}", segment.getId(), vectorId);
            }

            log.info("所有分块向量化完成，成功处理: {}个", segments.size());

        } catch (Exception e) {
            log.error("分块向量化失败", e);
            throw new RuntimeException("向量化处理失败: " + e.getMessage());
        }
    }

    /**
     * 搜索相似的文档分块
     *
     * @param searchReq 搜索请求
     * @return 搜索结果
     */
    public List<AiKnowledgeSearchRespVO.SearchResult> searchSimilarSegments(AiKnowledgeSearchReqVO searchReq) {
        log.info("搜索相似分块，知识库ID: {}, 查询: {}, TopK: {}",
                searchReq.getKnowledgeId(), searchReq.getQuery(), searchReq.getTopK());

        try {
            // 1. 将查询文本向量化
            float[] queryVector = embedText(searchReq.getQuery());

            // 2. 从Redis获取知识库的所有向量文档
            List<VectorDocument> vectorDocs = getVectorDocumentsByKnowledgeId(searchReq.getKnowledgeId());

            // 3. 计算相似度并排序
            List<VectorSearchResult> searchResults = new ArrayList<>();
            for (VectorDocument doc : vectorDocs) {
                double similarity = calculateCosineSimilarity(queryVector, doc.getVector());
                if (similarity >= searchReq.getThreshold()) {
                    VectorSearchResult result = new VectorSearchResult()
                            .setVectorId(doc.getVectorId())
                            .setScore(similarity)
                            .setDocument(doc)
                            .setDistance(1.0 - similarity);
                    searchResults.add(result);
                }
            }

            // 4. 按相似度排序并取TopK
            searchResults.sort((a, b) -> Double.compare(b.getScore(), a.getScore()));
            List<VectorSearchResult> topResults = searchResults.stream()
                    .limit(searchReq.getTopK())
                    .collect(Collectors.toList());

            // 5. 转换为响应格式
            List<AiKnowledgeSearchRespVO.SearchResult> results = new ArrayList<>();
            for (VectorSearchResult result : topResults) {
                VectorDocument doc = result.getDocument();

                // 获取文档信息
                AiKnowledgeDocumentEntity documentEntity = documentService.getById(doc.getDocumentId());
                String documentName = documentEntity != null ? documentEntity.getName() : "未知文档";

                AiKnowledgeSearchRespVO.SearchResult searchResult = new AiKnowledgeSearchRespVO.SearchResult()
                        .setSegmentId(doc.getSegmentId())
                        .setDocumentId(doc.getDocumentId())
                        .setDocumentName(documentName)
                        .setContent(doc.getContent())
                        .setScore(result.getScore())
                        .setPosition(doc.getPosition())
                        .setWordCount(doc.getWordCount());

                results.add(searchResult);
            }

            log.info("搜索完成，找到{}个相似结果", results.size());
            return results;

        } catch (Exception e) {
            log.error("向量搜索失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 删除向量
     *
     * @param vectorIds 向量ID列表
     */
    public void deleteVectors(List<String> vectorIds) {
        log.info("删除向量，向量ID数量: {}", vectorIds.size());

        try {
            for (String vectorId : vectorIds) {
                String key = vectorConfig.getDataPrefix() + vectorId;
                redisTemplate.delete(key);
                log.debug("删除向量: {}", vectorId);
            }
            log.info("向量删除完成，删除数量: {}", vectorIds.size());
        } catch (Exception e) {
            log.error("删除向量失败", e);
            throw new RuntimeException("删除向量失败: " + e.getMessage());
        }
    }

    /**
     * 文本向量化
     *
     * @param text 文本内容
     * @return 向量数组
     */
    public float[] embedText(String text) {
        log.info("文本向量化，文本长度: {}", text.length());

        // TODO: 这里应该调用真实的嵌入模型API
        // 比如OpenAI Embeddings、HuggingFace、或者本地模型
        // 现在使用简单的文本哈希来生成伪向量（仅用于演示）

        return generatePseudoVector(text, vectorConfig.getDefaultDimension());
    }

    // ========== 私有辅助方法 ==========

    /**
     * 生成向量ID
     */
    private String generateVectorId(Long knowledgeId, Long documentId, Long segmentId) {
        return String.format("kb_%d_doc_%d_seg_%d", knowledgeId, documentId, segmentId);
    }

    /**
     * 存储向量文档到Redis
     */
    private void storeVectorDocument(VectorDocument vectorDoc) {
        try {
            String key = vectorConfig.getDataPrefix() + vectorDoc.getVectorId();

            // 将向量文档转换为Map存储
            Map<String, Object> docMap = new HashMap<>();
            docMap.put("vectorId", vectorDoc.getVectorId());
            docMap.put("knowledgeId", vectorDoc.getKnowledgeId());
            docMap.put("documentId", vectorDoc.getDocumentId());
            docMap.put("segmentId", vectorDoc.getSegmentId());
            docMap.put("content", vectorDoc.getContent());
            docMap.put("vector", vectorDoc.getVector());
            docMap.put("wordCount", vectorDoc.getWordCount());
            docMap.put("position", vectorDoc.getPosition());
            docMap.put("createTime", vectorDoc.getCreateTime());

            redisTemplate.opsForHash().putAll(key, docMap);

            // 添加到知识库索引
            String indexKey = vectorConfig.getIndexPrefix() + vectorDoc.getKnowledgeId();
            redisTemplate.opsForSet().add(indexKey, vectorDoc.getVectorId());

            log.debug("向量文档存储成功: {}", vectorDoc.getVectorId());

        } catch (Exception e) {
            log.error("存储向量文档失败: {}", vectorDoc.getVectorId(), e);
            throw new RuntimeException("存储向量文档失败: " + e.getMessage());
        }
    }

    /**
     * 根据知识库ID获取所有向量文档
     */
    private List<VectorDocument> getVectorDocumentsByKnowledgeId(Long knowledgeId) {
        try {
            String indexKey = vectorConfig.getIndexPrefix() + knowledgeId;
            Set<Object> vectorIds = redisTemplate.opsForSet().members(indexKey);

            if (vectorIds == null || vectorIds.isEmpty()) {
                return new ArrayList<>();
            }

            List<VectorDocument> vectorDocs = new ArrayList<>();
            for (Object vectorIdObj : vectorIds) {
                String vectorId = vectorIdObj.toString();
                VectorDocument doc = getVectorDocumentById(vectorId);
                if (doc != null) {
                    vectorDocs.add(doc);
                }
            }

            return vectorDocs;

        } catch (Exception e) {
            log.error("获取知识库向量文档失败，知识库ID: {}", knowledgeId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据向量ID获取向量文档
     */
    private VectorDocument getVectorDocumentById(String vectorId) {
        try {
            String key = vectorConfig.getDataPrefix() + vectorId;
            Map<Object, Object> docMap = redisTemplate.opsForHash().entries(key);

            if (docMap.isEmpty()) {
                return null;
            }

            VectorDocument doc = new VectorDocument();
            doc.setVectorId((String) docMap.get("vectorId"));
            doc.setKnowledgeId(Long.valueOf(docMap.get("knowledgeId").toString()));
            doc.setDocumentId(Long.valueOf(docMap.get("documentId").toString()));
            doc.setSegmentId(Long.valueOf(docMap.get("segmentId").toString()));
            doc.setContent((String) docMap.get("content"));
            doc.setVector((float[]) docMap.get("vector"));
            doc.setWordCount((Integer) docMap.get("wordCount"));
            doc.setPosition((Integer) docMap.get("position"));
            doc.setCreateTime(Long.valueOf(docMap.get("createTime").toString()));

            return doc;

        } catch (Exception e) {
            log.error("获取向量文档失败，向量ID: {}", vectorId, e);
            return null;
        }
    }

    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(float[] vectorA, float[] vectorB) {
        if (vectorA.length != vectorB.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;

        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += Math.pow(vectorA[i], 2);
            normB += Math.pow(vectorB[i], 2);
        }

        if (normA == 0.0 || normB == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }

    /**
     * 生成伪向量（用于演示，实际应该调用真实的嵌入模型）
     */
    private float[] generatePseudoVector(String text, int dimension) {
        // 使用文本哈希生成确定性的伪向量
        int hash = text.hashCode();
        Random random = new Random(hash);

        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float) (random.nextGaussian() * 0.1); // 生成正态分布的随机数
        }

        // 归一化向量
        float norm = 0.0f;
        for (float v : vector) {
            norm += v * v;
        }
        norm = (float) Math.sqrt(norm);

        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }

        return vector;
    }
}
