package com.coocaa.ad.cheese.authority.common.tools.utils.loginLog;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 * @date 2024年11月26 11:19
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class LoginLogDTO implements LoginLog {


    /**
     * 系统名称
     * 数据字典中配置为 0006-1
     */
    private String systemCode= "0006-1";

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 登录IP
     */
    private String ip;

    /**
     * 登录地点
     */
    private String address;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 用户Agent
     */
    private String userAgent;

    /**
     * 来源（Web、Android等）
     */
    private String source;

    /**
     * 状态 [1:成功, 0:失败]
     */
    private Boolean status;

    /**
     * 登录失败原因
     */
    private String failReason;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 登录时间
     */
    private Date loginTime;

}
