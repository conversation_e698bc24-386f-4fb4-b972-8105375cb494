package com.coocaa.ad.cheese.authority.common.tools.enums;

import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/09/09 16:41
 * @desc <p>状态枚举</p>
 */
@Getter
public enum StatusEnum {

    /**
     * 禁用
     */
    DISABLE(false, "禁用"),
    /**
     * 启用
     */
    ENABLE(true, "启用");

    private final boolean enable;

    private final String desc;

    StatusEnum(boolean enable, String desc) {
        this.enable = enable;
        this.desc = desc;
    }



    public static StatusEnum get(boolean enable) {
        for (StatusEnum value : values()) {
           if(value.enable==enable){
               return value;
            }
        }
        throw new CommonException("没有找到status");
    }

}
