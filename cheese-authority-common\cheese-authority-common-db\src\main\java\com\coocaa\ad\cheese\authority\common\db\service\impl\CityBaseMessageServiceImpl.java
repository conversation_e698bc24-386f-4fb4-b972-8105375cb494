package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.coocaa.ad.cheese.authority.common.db.entity.CityBaseMessageEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.CityBaseMessageMapper;
import com.coocaa.ad.cheese.authority.common.db.service.ICityBaseMessageService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Service
public class CityBaseMessageServiceImpl extends ServiceImpl<CityBaseMessageMapper, CityBaseMessageEntity> implements ICityBaseMessageService {

}
