package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.UserFeishuEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserFeishuMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IUserFeishuService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户飞书信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Service
public class UserFeishuServiceImpl extends ServiceImpl<UserFeishuMapper, UserFeishuEntity> implements IUserFeishuService {

}
