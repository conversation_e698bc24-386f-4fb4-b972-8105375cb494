package com.coocaa.ad.cheese.authority.common.tools.query.params;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 类QueryOptions实现描述:
 *
 * <AUTHOR>
 * @date 2022年05月08 12:15
 */
@Getter
@Setter
public class QueryOptions {

    /**
     * 查询列数组
     */
    private String[] columns;

    /**
     * 查询列值集合
     */
    private List<IFilter> filters;

    /**
     * 排序集合
     */
    private List<IOrderBy> orderBys;

}
