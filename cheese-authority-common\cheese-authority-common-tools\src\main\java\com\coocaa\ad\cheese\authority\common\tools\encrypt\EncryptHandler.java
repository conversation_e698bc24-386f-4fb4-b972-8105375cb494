package com.coocaa.ad.cheese.authority.common.tools.encrypt;

import com.coocaa.ad.cheese.authority.common.tools.annotation.EncryptField;

/**
 * 加解密处理器接口
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface EncryptHandler {
    
    /**
     * 加密
     * 
     * @param plainText 明文
     * @param encryptType 加密类型
     * @return 密文
     */
    String encrypt(String plainText, EncryptField.EncryptType encryptType);
    
    /**
     * 解密
     * 
     * @param cipherText 密文
     * @param encryptType 加密类型
     * @return 明文
     */
    String decrypt(String cipherText, EncryptField.EncryptType encryptType);
    
    /**
     * 是否支持指定的加密类型
     * 
     * @param encryptType 加密类型
     * @return 是否支持
     */
    boolean supports(EncryptField.EncryptType encryptType);
}
