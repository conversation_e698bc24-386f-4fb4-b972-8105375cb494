package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * AI知识库文档上传请求VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiKnowledgeDocumentUploadReqVO", description = "AI知识库文档上传请求VO")
public class AiKnowledgeDocumentUploadReqVO {

    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    @Schema(description = "文档名称", maxLength = 200)
    private String name;

    @Schema(description = "文档内容（如果是文本直接上传）")
    private String content;

    @Schema(description = "是否立即处理", example = "true")
    private Boolean processImmediately = true;
}
