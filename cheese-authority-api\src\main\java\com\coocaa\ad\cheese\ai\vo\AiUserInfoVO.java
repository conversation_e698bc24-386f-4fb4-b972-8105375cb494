package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * AI用户信息VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "AI用户信息")
public class AiUserInfoVO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户OpenID")
    private String openId;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像URL")
    private String avatarUrl;

    @Schema(description = "用户性别，0-未知，1-男，2-女")
    private Integer gender;

    @Schema(description = "用户所在国家")
    private String country;

    @Schema(description = "用户所在省份")
    private String province;

    @Schema(description = "用户所在城市")
    private String city;

    @Schema(description = "用户语言")
    private String language;

    @Schema(description = "用户UnionID")
    private String unionId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
