package com.coocaa.ad.cheese.authority.common.tools.pool;

import com.coocaa.ad.cheese.authority.common.tools.vo.TimeLineTaskVO;

import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
public class ApproveQueryThreadPool {

    private static ThreadPoolExecutor threadPool;

    public static void initThreadPool() {
        int corePoolSize = 8; // 核心线程数
        int maximumPoolSize = 12; // 最大线程数
        long keepAliveTime = 60; // 非核心线程的存活时间，单位为秒
        BlockingQueue<Runnable> workQueue = new ArrayBlockingQueue<>(50); // 任务队列

        threadPool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS, workQueue);
    }

    public static Future<List<TimeLineTaskVO>> submitTimeLineTask(Callable<List<TimeLineTaskVO>> task) {
        if (threadPool == null) {
            initThreadPool();
        }
        return threadPool.submit(task);
    }

    public static void shutdownThreadPool() {
        if (threadPool != null) {
            threadPool.shutdown();
        }
    }

}
