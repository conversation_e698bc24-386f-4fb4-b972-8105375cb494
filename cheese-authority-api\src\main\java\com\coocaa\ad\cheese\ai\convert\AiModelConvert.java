package com.coocaa.ad.cheese.ai.convert;

import com.coocaa.ad.cheese.ai.common.db.entity.AiModelEntity;
import com.coocaa.ad.cheese.ai.vo.AiModelVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AI模型转换器
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Mapper
public interface AiModelConvert {

    AiModelConvert INSTANCE = Mappers.getMapper(AiModelConvert.class);

    /**
     * 实体转VO
     *
     * @param entity 实体
     * @return VO
     */
    AiModelVO toVo(AiModelEntity entity);
}
