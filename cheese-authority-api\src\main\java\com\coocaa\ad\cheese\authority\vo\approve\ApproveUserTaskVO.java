package com.coocaa.ad.cheese.authority.vo.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
public class ApproveUserTaskVO {

    @Schema(description = "审批任务")
    private List<ProcessUserTaskVO> task = Collections.emptyList();

    @Schema(description = "分页标记")
    private String pageToken;

    @Schema(description = "是否还有更多项")
    private Boolean hasMore;
}
