package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.bean.CityDetailParam;
import com.coocaa.ad.cheese.authority.common.db.bean.CityDetailVO;
import com.coocaa.ad.cheese.authority.common.db.bean.CityUpdateCodeParam;
import com.coocaa.ad.cheese.authority.common.db.bean.ProvinceCityDistrictSelectVO;
import com.coocaa.ad.cheese.authority.common.db.entity.CityEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 城市 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface ICityService extends IService<CityEntity> {

    List<CityEntity> queryByName(String name);

    int countCity();

    List<CityEntity> queryByParentId(Integer id);

    List<CityEntity> selectList(Boolean status);

    List<CityEntity> listByCodes(Collection<String> codes);

    void removeByParentId(Integer id);

    List<CityEntity> listByIdsEnable(Collection<Integer> ids, Boolean status);

    List<CityEntity> listByCityIdEnable(Integer cityId);

    /**
     * 根据城市名称查询城市列表
     * @param name 城市名称
     * @return 城市列表
     */
    List<CityEntity> queryByNameAndParentId(String name);

    /**
     * 通过bzCode批量查询指定父级ID的城市列表
     * @param codes bzCode集合
     * @return 城市列表
     */
    List<CityEntity> listByCodesAndParentId(Collection<String> codes);

    CityEntity getCityByName(String name);

    CityEntity getCounty(String cityName, String countyName);

    void updateImportCityCode(List<CityUpdateCodeParam> param);



    List<CityEntity> listByGbCode(List<String> gbCodes);

    CityEntity getCityByGbCode(String code);

    List<CityEntity> getGbByCityId(Integer cityId);

    List<CityDetailVO> getDetailByNames(List<CityDetailParam> params);

    List<ProvinceCityDistrictSelectVO> getProvinceCity(Set<Integer> districts);
}
