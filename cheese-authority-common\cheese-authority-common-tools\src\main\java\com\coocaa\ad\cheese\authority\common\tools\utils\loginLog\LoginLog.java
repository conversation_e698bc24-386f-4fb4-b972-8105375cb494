package com.coocaa.ad.cheese.authority.common.tools.utils.loginLog;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年11月25 17:52
 */

public interface LoginLog {

    /**
     * 系统名称
     * 数据字典中配置为 0006-1
     */
    default String getSystemCode() {
        return "0006-2";
    }

    /**
     * 用户ID
     */
     Integer getUserId();

    /**
     * 用户名
     */
     String getUserName();

    /**
     * 登录IP
     */
     String getIp();

    /**
     * 登录地点
     */
     String getAddress();

    /**
     * 浏览器
     */
     String getBrowser();

    /**
     * 用户Agent
     */
     String getUserAgent();

    /**
     * 来源（Web、Android等）
     */
     String getSource();

    /**
     * 状态 [1:成功, 0:失败]
     */
     Boolean getStatus();

    /**
     * 登录失败原因
     */
     String getFailReason();

    /**
     * 操作内容
     */
     String getContent();

    /**
     * 登录时间
     */
     Date getLoginTime();
}
