package com.coocaa.ad.cheese.ai.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.coocaa.ad.cheese.ai.bean.AiUserParam;
import com.coocaa.ad.cheese.ai.config.WechatMiniProgramConfig;
import com.coocaa.ad.cheese.ai.service.AiUserService;
import com.coocaa.ad.cheese.ai.vo.*;
import com.coocaa.ad.cheese.ai.vo.WechatLoginReqVO;
import com.coocaa.ad.cheese.ai.vo.WechatLoginRespVO;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 微信小程序登录服务
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WechatLoginService {

    private final WechatMiniProgramConfig config;
    private final AiUserService aiUserService;
    private WxMaService wxMaService;

    @PostConstruct
    public void init() {
        if (config.getAppId() == null || config.getAppId().trim().isEmpty() || "your_miniprogram_app_id".equals(config.getAppId())) {
            log.warn("微信小程序AppID未配置，请设置wechat.miniprogram.app-id配置项");
            return;
        }

        if (config.getAppSecret() == null || config.getAppSecret().trim().isEmpty() || "your_miniprogram_app_secret".equals(config.getAppSecret())) {
            log.warn("微信小程序AppSecret未配置，请设置wechat.miniprogram.app-secret配置项");
            return;
        }

        // 初始化微信小程序服务
        WxMaDefaultConfigImpl wxMaConfig = new WxMaDefaultConfigImpl();
        wxMaConfig.setAppid(config.getAppId());
        wxMaConfig.setSecret(config.getAppSecret());

        this.wxMaService = new WxMaServiceImpl();
        this.wxMaService.setWxMaConfig(wxMaConfig);

        log.info("微信小程序登录服务初始化完成，AppID: {}", config.getAppId());
    }

    /**
     * 微信小程序登录
     *
     * @param loginReq 登录请求
     * @return 登录响应
     * @throws Exception 登录失败时抛出异常
     */
    public WechatLoginRespVO login(WechatLoginReqVO loginReq) throws Exception {
        if (wxMaService == null) {
            throw new RuntimeException("微信小程序服务未初始化，请检查AppID和AppSecret配置");
        }

        try {
            // 1. 使用SDK调用微信接口，通过code换取openid和session_key
            WxMaJscode2SessionResult sessionResult = wxMaService.getUserService().getSessionInfo(loginReq.getCode());

            log.info("微信小程序登录成功，openId: {}, unionId: {}", sessionResult.getOpenid(), sessionResult.getUnionid());

            // 2. 生成自定义登录态token
            String token = generateToken(sessionResult.getOpenid());

            // 3. 查询或创建用户信息
            UserInfo userInfo = findOrCreateUser(sessionResult.getOpenid(), sessionResult.getUnionid(), loginReq);

            // 4. 构建响应（注意：不要将session_key返回给前端）
            return WechatLoginRespVO.success(
                    sessionResult.getOpenid(),
                    sessionResult.getUnionid(),
                    sessionResult.getSessionKey(),
                    token,
                    userInfo.getUserId(),
                    userInfo.getNickName(),
                    userInfo.getAvatarUrl(),
                    userInfo.getIsNewUser()
            );

        } catch (WxErrorException e) {
            log.error("微信小程序登录失败，错误码: {}, 错误信息: {}", e.getError().getErrorCode(), e.getError().getErrorMsg());
            throw new RuntimeException("微信登录失败: " + e.getError().getErrorMsg(), e);
        } catch (Exception e) {
            log.error("微信小程序登录过程中发生错误", e);
            throw new RuntimeException("登录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户详细信息（解密）
     *
     * @param sessionKey    会话密钥
     * @param encryptedData 加密数据
     * @param iv            初始向量
     * @return 用户详细信息
     * @throws Exception 解密失败时抛出异常
     */
    public WxMaUserInfo getUserInfo(String sessionKey, String encryptedData, String iv) throws Exception {
        if (wxMaService == null) {
            throw new RuntimeException("微信小程序服务未初始化，请检查AppID和AppSecret配置");
        }

        try {
            WxMaUserInfo userInfo = wxMaService.getUserService().getUserInfo(sessionKey, encryptedData, iv);
            log.info("获取用户详细信息成功，昵称: {}, 城市: {}", userInfo.getNickName(), userInfo.getCity());
            return userInfo;
        } catch (Exception e) {
            log.error("获取用户详细信息失败", e);
            throw new RuntimeException("获取用户详细信息失败: " + e.getMessage(), e);
        }
    }


    /**
     * 生成自定义登录态token
     *
     * @param openid 用户openid
     * @return token
     */
    private String generateToken(String openid) {
        // 简单的token生成策略，实际项目中应该使用JWT或其他安全的token生成方式
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis());
        return "wx_" + openid.substring(0, Math.min(8, openid.length())) + "_" + timestamp + "_" + uuid.substring(0, 8);
    }

    /**
     * 查询或创建用户信息
     *
     * @param openid   用户openid
     * @param unionid  用户unionid
     * @param loginReq 登录请求
     * @return 用户信息
     */
    private UserInfo findOrCreateUser(String openid, String unionid, WechatLoginReqVO loginReq) {
        // 这里应该查询数据库，检查用户是否已存在
        // 如果不存在则创建新用户
        // 为了演示，这里返回模拟数据

        log.info("查询或创建用户，openid: {}, unionid: {}", openid, unionid);

        // 模拟用户ID生成
        Long userId = Math.abs(openid.hashCode()) % 1000000L + 1000000L;

        // 模拟判断是否为新用户（实际应该查询数据库）
        boolean isNewUser = true;

        return UserInfo.builder()
                .userId(userId)
                .nickName("微信用户")
                .avatarUrl("")
                .isNewUser(isNewUser)
                .build();
    }


    /**
     * 获取用户信息并创建AI用户记录
     *
     * @param userInfoReq 用户信息请求
     * @return 微信用户信息响应
     * @throws WxErrorException 微信API异常
     */
    public WechatUserInfoRespVO getUserInfoAndCreateAiUser(WechatUserInfoReqVO userInfoReq) throws WxErrorException {
        log.info("获取用户信息并创建AI用户记录，OpenID: {}", userInfoReq.getOpenId());

        if (wxMaService == null) {
            throw new RuntimeException("微信小程序服务未初始化，请检查AppID和AppSecret配置");
        }

        // 1. 解密用户信息
        WxMaUserInfo wxUserInfo = wxMaService.getUserService().getUserInfo(
                userInfoReq.getSessionKey(),
                userInfoReq.getEncryptedData(),
                userInfoReq.getIv()
        );

        log.info("解密用户信息成功，昵称: {}, 城市: {}", wxUserInfo.getNickName(), wxUserInfo.getCity());

        // 2. 创建或更新AI用户信息
        AiUserVO aiUserInfo = createOrUpdateAiUser(wxUserInfo, userInfoReq.getOpenId());

        // 3. 构建响应
        WechatUserInfoRespVO respVO = WechatUserInfoRespVO.builder()
                .nickName(wxUserInfo.getNickName())
                .avatarUrl(wxUserInfo.getAvatarUrl())
                .gender(aiUserInfo.getGender())
                .country(wxUserInfo.getCountry())
                .province(wxUserInfo.getProvince())
                .city(wxUserInfo.getCity())
                .language(wxUserInfo.getLanguage())
                .openId(userInfoReq.getOpenId())
                .unionId(wxUserInfo.getUnionId())
                .build();

        log.info("获取用户详细信息成功，昵称: {}, 城市: {}, AI用户ID: {}",
                wxUserInfo.getNickName(), wxUserInfo.getCity(), aiUserInfo.getId());

        return respVO;
    }

    /**
     * 获取AI用户信息（查找或创建）
     *
     * @param userInfoReq 用户信息请求
     * @return AI用户信息
     */
    public AiUserVO getAiUserInfo(AiUserInfoReqVO userInfoReq) {
        log.info("获取AI用户信息，OpenID: {}", userInfoReq.getOpenId());

        // 调用AiUserService的业务方法
        return aiUserService.findOrCreateUser(userInfoReq);
    }

    /**
     * 创建或更新AI用户信息
     *
     * @param wxUserInfo 微信用户信息
     * @param openId 用户OpenID
     * @return AI用户信息
     */
    private AiUserVO createOrUpdateAiUser(WxMaUserInfo wxUserInfo, String openId) {
        log.info("创建或更新AI用户信息，OpenID: {}", openId);

        // 构建用户信息请求
        AiUserInfoReqVO userInfoReq = new AiUserInfoReqVO();
        userInfoReq.setOpenId(openId);
        userInfoReq.setNickName(wxUserInfo.getNickName());
        userInfoReq.setAvatarUrl(wxUserInfo.getAvatarUrl());

        // 处理性别
        if (wxUserInfo.getGender() != null) {
            try {
                userInfoReq.setGender(Integer.parseInt(wxUserInfo.getGender()));
            } catch (NumberFormatException e) {
                log.warn("性别格式转换失败: {}", wxUserInfo.getGender());
                // 默认为未知
                userInfoReq.setGender(0);
            }
        }

        userInfoReq.setCountry(wxUserInfo.getCountry());
        userInfoReq.setProvince(wxUserInfo.getProvince());
        userInfoReq.setCity(wxUserInfo.getCity());
        userInfoReq.setLanguage(wxUserInfo.getLanguage());
        userInfoReq.setUnionId(wxUserInfo.getUnionId());

        // 调用服务层方法查找或创建用户
        return aiUserService.findOrCreateUser(userInfoReq);
    }

    /**
     * 用户信息
     */
    @lombok.Data
    @lombok.Builder
    public static class UserInfo {
        private Long userId;
        private String nickName;
        private String avatarUrl;
        private Boolean isNewUser;
    }
}
