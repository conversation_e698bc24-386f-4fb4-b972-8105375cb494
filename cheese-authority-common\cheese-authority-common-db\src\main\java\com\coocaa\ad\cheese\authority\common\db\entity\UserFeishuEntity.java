package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 用户飞书信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_feishu")
public class UserFeishuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 飞书用户ID
     */
    private String feishuUserId;

    /**
     * 用户的 union_id
     */
    private String unionId;

    /**
     * openid
     */
    private String openId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;


}
