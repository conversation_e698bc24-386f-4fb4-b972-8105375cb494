package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.config.AiVectorStoreConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * AI嵌入模型工厂
 * 
 * 参考yudao-module-ai-server的AiModelFactory实现
 * 负责创建和管理各种嵌入模型
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiEmbeddingModelFactory {

    private final AiVectorStoreConfig vectorStoreConfig;
    
    // 模型缓存
    private final ConcurrentMap<String, Object> modelCache = new ConcurrentHashMap<>();

    /**
     * 获取或创建嵌入模型
     *
     * @param platform 平台类型
     * @param apiKey API密钥
     * @param model 模型名称
     * @return 嵌入模型（这里返回我们的包装类）
     */
    public EmbeddingModelWrapper getOrCreateEmbeddingModel(String platform, String apiKey, String model) {
        String cacheKey = buildCacheKey(platform, apiKey, model);
        
        return (EmbeddingModelWrapper) modelCache.computeIfAbsent(cacheKey, key -> {
            log.info("创建嵌入模型，平台: {}, 模型: {}", platform, model);
            return createEmbeddingModel(platform, apiKey, model);
        });
    }

    /**
     * 获取默认嵌入模型
     */
    public EmbeddingModelWrapper getDefaultEmbeddingModel() {
        AiVectorStoreConfig.EmbeddingConfig config = vectorStoreConfig.getEmbedding();
        return getOrCreateEmbeddingModel(config.getPlatform(), config.getApiKey(), config.getModel());
    }

    /**
     * 创建嵌入模型
     */
    private EmbeddingModelWrapper createEmbeddingModel(String platform, String apiKey, String model) {
        switch (platform.toLowerCase()) {
            case "openai":
                return createOpenAIEmbeddingModel(apiKey, model);
            case "huggingface":
                return createHuggingFaceEmbeddingModel(apiKey, model);
            case "local":
                return createLocalEmbeddingModel(model);
            default:
                log.warn("未知的嵌入模型平台: {}, 使用默认实现", platform);
                return createDefaultEmbeddingModel(model);
        }
    }

    /**
     * 创建OpenAI嵌入模型
     */
    private EmbeddingModelWrapper createOpenAIEmbeddingModel(String apiKey, String model) {
        // TODO: 集成真实的OpenAI Embeddings API
        log.info("创建OpenAI嵌入模型: {}", model);
        return new EmbeddingModelWrapper("openai", model, 1536) {
            @Override
            public float[] embed(String text) {
                // 模拟OpenAI API调用
                return generatePseudoVector(text, 1536);
            }

            @Override
            public List<float[]> embedBatch(List<String> texts) {
                return texts.stream().map(this::embed).toList();
            }
        };
    }

    /**
     * 创建HuggingFace嵌入模型
     */
    private EmbeddingModelWrapper createHuggingFaceEmbeddingModel(String apiKey, String model) {
        // TODO: 集成HuggingFace模型
        log.info("创建HuggingFace嵌入模型: {}", model);
        return new EmbeddingModelWrapper("huggingface", model, 768) {
            @Override
            public float[] embed(String text) {
                // 模拟HuggingFace模型调用
                return generatePseudoVector(text, 768);
            }

            @Override
            public List<float[]> embedBatch(List<String> texts) {
                return texts.stream().map(this::embed).toList();
            }
        };
    }

    /**
     * 创建本地嵌入模型
     */
    private EmbeddingModelWrapper createLocalEmbeddingModel(String model) {
        // TODO: 集成本地模型
        log.info("创建本地嵌入模型: {}", model);
        return new EmbeddingModelWrapper("local", model, 512) {
            @Override
            public float[] embed(String text) {
                // 模拟本地模型推理
                return generatePseudoVector(text, 512);
            }

            @Override
            public List<float[]> embedBatch(List<String> texts) {
                return texts.stream().map(this::embed).toList();
            }
        };
    }

    /**
     * 创建默认嵌入模型
     */
    private EmbeddingModelWrapper createDefaultEmbeddingModel(String model) {
        log.info("创建默认嵌入模型: {}", model);
        return new EmbeddingModelWrapper("default", model, 1536) {
            @Override
            public float[] embed(String text) {
                return generatePseudoVector(text, 1536);
            }

            @Override
            public List<float[]> embedBatch(List<String> texts) {
                return texts.stream().map(this::embed).toList();
            }
        };
    }

    /**
     * 生成缓存键
     */
    private String buildCacheKey(String platform, String apiKey, String model) {
        return String.format("%s_%s_%s", platform, 
                apiKey != null ? apiKey.hashCode() : "null", model);
    }

    /**
     * 生成伪向量（用于演示）
     */
    private float[] generatePseudoVector(String text, int dimension) {
        int hash = text.hashCode();
        Random random = new Random(hash);
        
        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float) (random.nextGaussian() * 0.1);
        }
        
        // 归一化
        float norm = 0.0f;
        for (float v : vector) {
            norm += v * v;
        }
        norm = (float) Math.sqrt(norm);
        
        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }
        
        return vector;
    }

    /**
     * 嵌入模型包装类
     */
    public abstract static class EmbeddingModelWrapper {
        private final String platform;
        private final String model;
        private final int dimension;

        public EmbeddingModelWrapper(String platform, String model, int dimension) {
            this.platform = platform;
            this.model = model;
            this.dimension = dimension;
        }

        public abstract float[] embed(String text);
        public abstract List<float[]> embedBatch(List<String> texts);

        public String getPlatform() { return platform; }
        public String getModel() { return model; }
        public int getDimension() { return dimension; }
    }
}
