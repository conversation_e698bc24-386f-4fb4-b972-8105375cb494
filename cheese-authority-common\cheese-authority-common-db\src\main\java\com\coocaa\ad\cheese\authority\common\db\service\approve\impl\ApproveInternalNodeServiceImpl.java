package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalNodeEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalNodeMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalNodeService;
import com.coocaa.ad.cheese.authority.common.db.vo.approve.InstanceNodeCommonVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalNodeServiceImpl extends ServiceImpl<ApproveInternalNodeMapper, ApproveInternalNodeEntity> implements IApproveInternalNodeService {

    @Override
    public List<InstanceNodeCommonVO> getInstanceNode(String instanceCode) {
        return this.baseMapper.getInstanceNode(instanceCode);
    }

    @Override
    public List<InstanceNodeCommonVO> getInstanceNodeBatch(List<String> instanceCodes) {
        return this.baseMapper.getInstanceNodeBatch(instanceCodes);
    }
}
