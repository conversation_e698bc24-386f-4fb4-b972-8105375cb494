package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.bean.DownloadTaskDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.DownloadTaskEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.DownloadTaskMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IDownloadTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 下载任务表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DownloadTaskServiceImpl extends ServiceImpl<DownloadTaskMapper, DownloadTaskEntity> implements IDownloadTaskService {

    @Override
    public IPage<DownloadTaskEntity> pageList(IPage<DownloadTaskEntity> page, DownloadTaskDTO downloadTask) {
        return baseMapper.pageList(page, downloadTask);
    }
}