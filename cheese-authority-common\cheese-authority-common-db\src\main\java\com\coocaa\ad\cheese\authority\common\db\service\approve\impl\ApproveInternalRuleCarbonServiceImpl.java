package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleCarbonEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRulePersonEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRuleCarbonMapper;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRulePersonMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalRuleCarbonService;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalRulePersonService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
public class ApproveInternalRuleCarbonServiceImpl extends ServiceImpl<ApproveInternalRuleCarbonMapper, ApproveInternalRuleCarbonEntity> implements IApproveInternalRuleCarbonService {

}
