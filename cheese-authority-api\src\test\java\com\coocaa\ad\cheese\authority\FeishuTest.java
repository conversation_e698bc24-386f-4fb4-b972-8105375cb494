package com.coocaa.ad.cheese.authority;

import com.alibaba.fastjson2.JSON;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentReq;
import com.lark.oapi.service.contact.v3.model.ChildrenDepartmentResp;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @since 2024/12/7
 */
@ActiveProfiles("local")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FeishuTest {
    public static void main(String[] args) throws Exception {
        // 构建client
        Client client = Client.newBuilder("cli_a7a2362bb5b9d00e", "qY647AlUlXeYQ8m3ujdrkfMRzpSvkNK1").build();
        // 创建请求对象 创世部门id
        ChildrenDepartmentReq req = ChildrenDepartmentReq.newBuilder()
                .departmentId("od-81f94fbf83023532fdde6f78787b83df")
                .pageSize(50)
                .build();
        // 发起请求
        ChildrenDepartmentResp resp = client.contact().department().children(req, RequestOptions.newBuilder().build());

        System.out.println(JSON.toJSONString(resp));
    }
}
