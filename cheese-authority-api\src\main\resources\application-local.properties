spring.cloud.service-registry.auto-registration.enabled=false

# Redis配置
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.timeout=5000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# Spring AI向量存储配置
spring.ai.vectorstore.redis.initialize-schema=true
spring.ai.vectorstore.redis.index-name=knowledge_index
spring.ai.vectorstore.redis.prefix=knowledge_segment:






