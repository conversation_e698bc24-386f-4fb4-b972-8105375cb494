# AI模型表配置和角色关联模型功能总结

## 概述

新增AI模型表配置功能，并在角色表中添加模型关联，实现角色与模型的绑定，为不同角色配置不同的AI模型，提供更灵活的AI对话体验。

## 主要功能

### 1. AI模型管理
- ✅ 模型信息配置（名称、标识符、提供商等）
- ✅ 模型价格管理（输入/输出价格）
- ✅ 模型启用/禁用控制
- ✅ 模型类型分类（聊天、嵌入、图像等）
- ✅ 模型排序和展示

### 2. 角色模型关联
- ✅ 角色绑定特定模型
- ✅ 角色信息包含模型详情
- ✅ 创建对话时验证角色和模型
- ✅ 支持角色模型信息查询

## 数据库设计

### AI模型表 (ai_model)
```sql
CREATE TABLE `ai_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_key` varchar(100) NOT NULL COMMENT '模型标识符（API调用时使用）',
  `provider` varchar(50) NOT NULL COMMENT '模型提供商',
  `model_type` varchar(20) NOT NULL DEFAULT 'chat' COMMENT '模型类型',
  `description` text COMMENT '模型描述',
  `max_tokens` int(11) DEFAULT NULL COMMENT '最大上下文长度',
  `input_price` decimal(10,6) DEFAULT NULL COMMENT '输入价格（每1000tokens）',
  `output_price` decimal(10,6) DEFAULT NULL COMMENT '输出价格（每1000tokens）',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort` int(11) DEFAULT 0 COMMENT '排序值',
  -- 标准字段：create_time, creator, update_time, operator, delete_flag
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_key` (`model_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型表';
```

### 角色表更新 (ai_role)
```sql
-- 新增模型关联字段
ALTER TABLE `ai_role` 
ADD COLUMN `model_id` bigint(20) DEFAULT NULL COMMENT '模型编号，关联ai_model表的id字段' 
AFTER `role_prompt`;
```

## 代码架构

### 1. 实体层 (Entity)
```java
// AI模型实体
@Data
@TableName("ai_model")
public class AiModelEntity {
    private Long id;
    private String modelName;
    private String modelKey;
    private String provider;
    private String modelType;
    private String description;
    private Integer maxTokens;
    private BigDecimal inputPrice;
    private BigDecimal outputPrice;
    private Integer enabled;
    private Integer sort;
    // ... 标准字段
}

// 角色实体更新
@Data
@TableName("ai_role")
public class AiRoleEntity {
    // ... 原有字段
    @TableField("model_id")
    private Long modelId; // 新增模型关联
}
```

### 2. 服务层 (Service)
```java
// AI模型业务服务
@Service
public class AiModelService {
    public List<AiModelVO> getAllModels();
    public AiModelVO getModelById(Long modelId);
    public AiModelVO getModelByKey(String modelKey);
    public List<AiModelVO> getModelsByType(String modelType);
}

// 角色服务增强
@Service
public class AiRoleService {
    // 原有方法增强，包含模型信息
    public List<AiRoleVO> getAllRoles();
    public AiRoleVO getRoleById(Long roleId);
    
    // 新增方法
    public String getRolePrompt(Long roleId);
    public Long getRoleModelId(Long roleId);
}
```

### 3. 控制器层 (Controller)
```java
// AI模型管理接口
@RestController
@RequestMapping("/sys/ai/model")
public class AiModelController {
    @GetMapping("/list")
    public ResultTemplate<List<AiModelVO>> getAllModels();
    
    @GetMapping("/{modelId}")
    public ResultTemplate<AiModelVO> getModelById(@PathVariable Long modelId);
    
    @GetMapping("/type/{modelType}")
    public ResultTemplate<List<AiModelVO>> getModelsByType(@PathVariable String modelType);
    
    @GetMapping("/key/{modelKey}")
    public ResultTemplate<AiModelVO> getModelByKey(@PathVariable String modelKey);
}
```

### 4. VO层增强
```java
// 模型VO
@Data
public class AiModelVO {
    private Long id;
    private String modelName;
    private String modelKey;
    private String provider;
    private String modelType;
    private String description;
    private Integer maxTokens;
    private BigDecimal inputPrice;
    private BigDecimal outputPrice;
    private Integer enabled;
    private Integer sort;
    // ... 标准字段
}

// 角色VO增强
@Data
public class AiRoleVO {
    // ... 原有字段
    private Long modelId;
    private AiModelVO model; // 关联的模型信息
}
```

## 预置数据

### 默认模型配置
```sql
INSERT INTO `ai_model` VALUES 
(1, '豆包-4o', 'doubao-pro-4o', 'ByteDance', 'chat', '字节跳动豆包大模型，支持多轮对话', 32768, 0.0008, 0.002, 1, 1),
(2, '豆包-32k', 'doubao-pro-32k', 'ByteDance', 'chat', '字节跳动豆包大模型，32k上下文', 32768, 0.0005, 0.001, 1, 2),
(3, '豆包-128k', 'doubao-pro-128k', 'ByteDance', 'chat', '字节跳动豆包大模型，128k上下文', 131072, 0.001, 0.002, 1, 3),
(4, 'GPT-4', 'gpt-4', 'OpenAI', 'chat', 'OpenAI GPT-4 模型', 8192, 0.03, 0.06, 0, 10),
(5, 'GPT-3.5 Turbo', 'gpt-3.5-turbo', 'OpenAI', 'chat', 'OpenAI GPT-3.5 Turbo 模型', 4096, 0.0015, 0.002, 0, 11);
```

### 角色模型关联
```sql
-- 将现有角色关联到默认模型
UPDATE `ai_role` SET `model_id` = 1 WHERE `model_id` IS NULL AND `delete_flag` = 0;
```

## API接口

### 1. 模型管理接口

#### 获取所有模型
```bash
GET /sys/ai/model/list
```

#### 根据ID获取模型
```bash
GET /sys/ai/model/{modelId}
```

#### 根据类型获取模型
```bash
GET /sys/ai/model/type/{modelType}
```

#### 根据标识符获取模型
```bash
GET /sys/ai/model/key/{modelKey}
```

### 2. 角色接口增强

#### 获取角色列表（包含模型信息）
```bash
GET /sys/ai/role/list

# 响应示例
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "roleName": "编程助手",
      "rolePrompt": "你是一个专业的编程助手...",
      "modelId": 1,
      "model": {
        "id": 1,
        "modelName": "豆包-4o",
        "modelKey": "doubao-pro-4o",
        "provider": "ByteDance",
        "modelType": "chat",
        "maxTokens": 32768
      }
    }
  ]
}
```

## 业务流程

### 1. 创建对话流程
```java
// 1. 验证角色存在
if (roleId != null) {
    String rolePrompt = aiRoleService.getRolePrompt(roleId);
    if (rolePrompt == null) {
        throw new RuntimeException("指定的角色不存在或已禁用");
    }
}

// 2. 创建对话（关联角色和模型）
AiConversationEntity conversation = createConversationForUser(userId, roleId);
```

### 2. 发送消息流程
```java
// 1. 获取角色提示词
String rolePrompt = getRolePrompt(sendReqVO.getRoleId());

// 2. 构建消息（包含角色提示词）
List<ChatMessage> messages = doubaoAiService.buildMessageList(
    sendReqVO.getContent(), 
    contextMessages, 
    rolePrompt
);
```

## 扩展功能

### 1. 模型切换
- 支持运行时切换角色关联的模型
- 支持A/B测试不同模型效果

### 2. 成本控制
- 基于模型价格计算对话成本
- 支持用户级别的成本限制

### 3. 性能监控
- 统计各模型的使用情况
- 监控模型响应时间和质量

### 4. 模型路由
- 根据用户类型自动选择模型
- 支持负载均衡和故障转移

## 查询示例

### 1. 查询角色及其模型信息
```sql
SELECT 
    r.id,
    r.role_name,
    r.role_prompt,
    m.model_name,
    m.provider,
    m.max_tokens
FROM ai_role r 
LEFT JOIN ai_model m ON r.model_id = m.id 
WHERE r.delete_flag = 0 
  AND (m.delete_flag = 0 OR m.id IS NULL);
```

### 2. 统计模型使用情况
```sql
SELECT 
    m.model_name,
    m.provider,
    COUNT(r.id) as role_count,
    COUNT(c.id) as conversation_count
FROM ai_model m 
LEFT JOIN ai_role r ON m.id = r.model_id AND r.delete_flag = 0
LEFT JOIN ai_conversation c ON r.id = c.role_id AND c.delete_flag = 0
WHERE m.delete_flag = 0 
GROUP BY m.id, m.model_name, m.provider 
ORDER BY conversation_count DESC;
```

### 3. 查询可用的聊天模型
```sql
SELECT * FROM ai_model 
WHERE model_type = 'chat' 
  AND enabled = 1 
  AND delete_flag = 0 
ORDER BY sort, id;
```

## 总结

通过引入AI模型表配置和角色模型关联功能，我们实现了：

🎯 **灵活的模型管理**：支持多种AI模型的配置和管理  
🎯 **角色模型绑定**：不同角色可以使用不同的AI模型  
🎯 **成本可控**：基于模型价格进行成本计算和控制  
🎯 **扩展性强**：为后续功能扩展提供良好基础  
🎯 **易于维护**：清晰的数据结构和业务逻辑  

这为构建更智能、更灵活的AI对话系统奠定了坚实的基础！
