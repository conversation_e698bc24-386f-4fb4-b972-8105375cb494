package com.coocaa.ad.cheese.authority.vo.message;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class MessageInternalVO {
    /**
     * ID
     */
    private Integer id;

    /**
     * 消息应用类型编码，字典0124
     */
    @Schema(description = "消息应用类型编码，字典0124")
    private String appCode;

    /**
     * 消息模块类型编码，字典0125
     */
    @Schema(description = "消息模块类型编码，字典0125")
    private String moduleCode;

    /**
     * 标题
     */
    @Schema(description = "标题")
    private String title;

    /**
     * 内容
     */
    @Schema(description = "内容")
    private String content;

    /**
     * 跳转地址
     */
    @Schema(description = "跳转地址")
    private String url;

    /**
     * 消息发送人
     */
    @Schema(description = "消息发送人ID")
    private Integer sendUser;

    /**
     * 消息接收人
     */
    @Schema(description = "消息接收人ID")
    private Integer receiveUser;

    @Schema(description = "消息发送人名字")
    private String sendUserName;

    @Schema(description = "消息接收人名字")
    private String receiveUserName;

    /**
     * 是否已读，1：已读，0：未读
     */
    @Schema(description = "是否已读，1：已读，0：未读")
    private Integer readFlag;


    @Schema(description = "是否发送了飞书消息")
    private Boolean feiShuFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
