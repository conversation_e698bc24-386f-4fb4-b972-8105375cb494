package com.coocaa.ad.cheese.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * AI向量存储配置
 * 
 * 参考yudao-module-ai-server的配置结构
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai.vector-store")
public class AiVectorStoreConfig {

    /**
     * 向量存储类型（redis, milvus, qdrant, simple）
     */
    private String type = "redis";

    /**
     * Redis向量存储配置
     */
    private RedisConfig redis = new RedisConfig();

    /**
     * 嵌入模型配置
     */
    private EmbeddingConfig embedding = new EmbeddingConfig();

    /**
     * 元数据字段配置
     */
    private Map<String, String> metadataFields = new HashMap<>();

    @Data
    public static class RedisConfig {
        /**
         * 索引名称
         */
        private String indexName = "ai_knowledge_index";

        /**
         * 键前缀
         */
        private String prefix = "ai:vector:";

        /**
         * 是否初始化Schema
         */
        private boolean initializeSchema = true;

        /**
         * 向量维度
         */
        private int dimension = 1536;

        /**
         * 距离算法（COSINE, L2, IP）
         */
        private String distanceMetric = "COSINE";
    }

    @Data
    public static class EmbeddingConfig {
        /**
         * 嵌入模型平台（openai, huggingface, local）
         */
        private String platform = "openai";

        /**
         * 模型名称
         */
        private String model = "text-embedding-ada-002";

        /**
         * API密钥
         */
        private String apiKey;

        /**
         * API地址
         */
        private String apiUrl;

        /**
         * 向量维度
         */
        private int dimension = 1536;

        /**
         * 批处理大小
         */
        private int batchSize = 100;
    }

    /**
     * 初始化默认元数据字段
     */
    public void initDefaultMetadataFields() {
        if (metadataFields.isEmpty()) {
            metadataFields.put("knowledgeId", "NUMERIC");
            metadataFields.put("documentId", "NUMERIC");
            metadataFields.put("segmentId", "NUMERIC");
            metadataFields.put("documentName", "TEXT");
            metadataFields.put("fileType", "TAG");
            metadataFields.put("wordCount", "NUMERIC");
            metadataFields.put("position", "NUMERIC");
            metadataFields.put("createTime", "NUMERIC");
        }
    }

    /**
     * 配置RedisVectorStoreProperties Bean
     * 当Spring AI依赖可用时使用
     */
    @Bean
    public RedisVectorStorePropertiesWrapper redisVectorStoreProperties() {
        RedisVectorStorePropertiesWrapper properties = new RedisVectorStorePropertiesWrapper();
        properties.setIndexName(redis.getIndexName());
        properties.setPrefix(redis.getPrefix());
        properties.setInitializeSchema(redis.isInitializeSchema());
        return properties;
    }

    /**
     * RedisVectorStoreProperties包装类
     * 用于在Spring AI依赖不可用时提供兼容性
     */
    @Data
    public static class RedisVectorStorePropertiesWrapper {
        private String indexName = "ai_knowledge_index";
        private String prefix = "ai:vector:";
        private boolean initializeSchema = true;
    }
}
