package com.coocaa.ad.cheese.authority.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DataUserVO {
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "ccxx01")
    private String wno;
}
