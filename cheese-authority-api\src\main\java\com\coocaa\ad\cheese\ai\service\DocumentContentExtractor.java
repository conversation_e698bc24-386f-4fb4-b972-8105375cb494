package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.utils.FileTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.exception.TikaException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * 文档内容提取服务
 * 
 * 使用Apache Tika提取各种格式文档的文本内容
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
public class DocumentContentExtractor {

    private static final Tika TIKA = new Tika();
    
    // 设置最大文件大小为10MB
    private static final int MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * 从上传文件中提取文本内容
     *
     * @param file 上传的文件
     * @return 提取的文本内容
     * @throws IOException 文件读取异常
     */
    public String extractContent(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        long fileSize = file.getSize();
        
        log.info("开始提取文档内容，文件名: {}, 大小: {} bytes", fileName, fileSize);

        // 检查文件大小
        if (fileSize > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小超过限制，最大支持10MB");
        }

        // 检查文件类型
        if (!FileTypeUtils.isSupportedDocument(fileName)) {
            log.warn("不支持的文件类型: {}", fileName);
            // 对于不支持的类型，尝试按文本文件处理
            return extractAsText(file);
        }

        try (InputStream inputStream = file.getInputStream()) {
            String content = extractContentFromStream(inputStream, fileName);
            
            if (content == null || content.trim().isEmpty()) {
                log.warn("提取的内容为空，文件名: {}", fileName);
                return "";
            }

            // 清理和标准化内容
            content = cleanContent(content);
            
            log.info("文档内容提取成功，文件名: {}, 内容长度: {}", fileName, content.length());
            return content;
            
        } catch (Exception e) {
            log.error("提取文档内容失败，文件名: {}", fileName, e);
            
            // 如果Tika提取失败，尝试按文本文件处理
            if (FileTypeUtils.isTextFile(fileName)) {
                log.info("尝试按文本文件处理: {}", fileName);
                return extractAsText(file);
            }
            
            throw new RuntimeException("文档内容提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从输入流中提取内容
     */
    private String extractContentFromStream(InputStream inputStream, String fileName) throws IOException, TikaException {
        if (FileTypeUtils.isTextFile(fileName)) {
            // 对于文本文件，直接读取
            return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        } else {
            // 使用Tika提取其他格式
            return TIKA.parseToString(inputStream);
        }
    }

    /**
     * 按文本文件提取内容
     */
    private String extractAsText(MultipartFile file) throws IOException {
        try {
            return new String(file.getBytes(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.warn("按UTF-8编码读取失败，尝试其他编码: {}", file.getOriginalFilename());
            // 尝试其他编码
            try {
                return new String(file.getBytes(), "GBK");
            } catch (Exception e2) {
                return new String(file.getBytes(), "ISO-8859-1");
            }
        }
    }

    /**
     * 清理和标准化文本内容
     */
    private String cleanContent(String content) {
        if (content == null) {
            return "";
        }

        // 移除多余的空白字符
        content = content.replaceAll("\\r\\n", "\n");  // 统一换行符
        content = content.replaceAll("\\r", "\n");     // 处理Mac换行符
        content = content.replaceAll("\\n{3,}", "\n\n"); // 合并多个连续换行
        content = content.replaceAll("[ \\t]+", " ");   // 合并多个空格和制表符
        content = content.replaceAll(" *\\n *", "\n");  // 清理行首行尾空格

        // 移除特殊字符（保留基本标点）
        content = content.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        return content.trim();
    }

    /**
     * 获取支持的文件类型列表
     */
    public String[] getSupportedFileTypes() {
        return new String[]{
            "txt", "md", "csv", "json", "xml", "html", "htm",  // 文本文件
            "pdf",                                              // PDF文件
            "doc", "docx",                                      // Word文档
            "xls", "xlsx",                                      // Excel文件
            "ppt", "pptx"                                       // PowerPoint文件
        };
    }

    /**
     * 检查文件是否支持
     */
    public boolean isFileSupported(String fileName) {
        return FileTypeUtils.isSupportedDocument(fileName);
    }

    /**
     * 获取文件类型描述
     */
    public String getFileTypeDescription(String fileName) {
        if (FileTypeUtils.isTextFile(fileName)) {
            return "文本文件";
        } else if (FileTypeUtils.isPdfFile(fileName)) {
            return "PDF文档";
        } else if (FileTypeUtils.isWordFile(fileName)) {
            return "Word文档";
        } else if (FileTypeUtils.isExcelFile(fileName)) {
            return "Excel表格";
        } else if (FileTypeUtils.isPowerPointFile(fileName)) {
            return "PowerPoint演示文稿";
        } else {
            return "未知类型";
        }
    }
}
