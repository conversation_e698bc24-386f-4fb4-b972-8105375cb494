#!/bin/bash

# 测试语音识别COS URL接口
# 使用方法: ./test-speech-recognition-cos.sh [cos_url]

BASE_URL="http://localhost:8001"
DEFAULT_COS_URL="https://test-1330579985.cos.ap-guangzhou.myqcloud.com/audio/test.mp3"
COS_URL=${1:-$DEFAULT_COS_URL}

echo "=== 测试语音识别COS URL接口 ==="
echo "基础URL: $BASE_URL"
echo "COS URL: $COS_URL"
echo ""

# 测试1: 正常的COS URL
echo "🧪 测试1: 正常的COS URL"
echo "正在发送语音识别请求..."

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST \
  "$BASE_URL/sys/speech/recognize-sdk" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "url=$COS_URL")
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo "⏱️  处理时间: ${DURATION}秒"
echo "📥 响应内容:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"
echo ""

# 检查响应结果
if echo "$RESPONSE" | jq -e '.data.success' >/dev/null 2>&1; then
    SUCCESS=$(echo "$RESPONSE" | jq -r '.data.success')
    if [ "$SUCCESS" = "true" ]; then
        RECOGNIZED_TEXT=$(echo "$RESPONSE" | jq -r '.data.text // "无识别结果"')
        echo "✅ 语音识别成功!"
        echo "🎤 识别结果: $RECOGNIZED_TEXT"
    else
        ERROR_MSG=$(echo "$RESPONSE" | jq -r '.data.message // "未知错误"')
        echo "❌ 语音识别失败!"
        echo "🚫 错误信息: $ERROR_MSG"
    fi
else
    echo "⚠️  响应格式异常"
fi

echo "----------------------------------------"
echo ""

# 测试2: 空URL
echo "🧪 测试2: 空URL"
EMPTY_RESPONSE=$(curl -s -X POST \
  "$BASE_URL/sys/speech/recognize-sdk" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "url=")

echo "📥 空URL响应:"
echo "$EMPTY_RESPONSE" | jq '.' 2>/dev/null || echo "$EMPTY_RESPONSE"
echo ""

# 测试3: 无效URL
echo "🧪 测试3: 无效URL"
INVALID_RESPONSE=$(curl -s -X POST \
  "$BASE_URL/sys/speech/recognize-sdk" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "url=https://invalid-url.com/nonexistent.mp3")

echo "📥 无效URL响应:"
echo "$INVALID_RESPONSE" | jq '.' 2>/dev/null || echo "$INVALID_RESPONSE"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "💡 测试说明:"
echo "1. 接口地址: POST /sys/speech/recognize-sdk"
echo "2. 参数: url (COS语音文件URL)"
echo "3. 支持的音频格式: mp3, wav, m4a等"
echo "4. 文件大小限制: 10MB"
echo ""
echo "🔧 如果遇到问题，请检查:"
echo "- COS URL是否可访问"
echo "- 音频文件格式是否支持"
echo "- 微信小程序AppID和AppSecret配置是否正确"
echo "- 网络连接是否正常"
echo ""
echo "📚 使用示例:"
echo "curl -X POST \\"
echo "  http://localhost:8001/sys/speech/recognize-sdk \\"
echo "  -H 'Content-Type: application/x-www-form-urlencoded' \\"
echo "  -d 'url=https://test-1330579985.cos.ap-guangzhou.myqcloud.com/audio/test.mp3'"
