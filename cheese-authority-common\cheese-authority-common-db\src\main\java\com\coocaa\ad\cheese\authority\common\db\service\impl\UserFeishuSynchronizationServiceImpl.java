package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.UserFeishuSynchronizationEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserFeishuSynchronizationMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IUserFeishuSynchronizationService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户同步飞书信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
public class UserFeishuSynchronizationServiceImpl extends ServiceImpl<UserFeishuSynchronizationMapper, UserFeishuSynchronizationEntity> implements IUserFeishuSynchronizationService {

}
