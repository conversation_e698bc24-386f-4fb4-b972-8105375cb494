<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRulePersonMapper">

    <update id="updateAfterRank">
        update approve_internal_rule_person set rank = rank - 1 where rule_id = #{ruleId} and rank > #{rank}
    </update>


    <select id="getPersonByRuleId"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRulePersonEntity">
        select p.id,p.rule_id,p.rank,p.user_id,r.code as ruleCode, r.approval_name as approvalName,p.task_name as taskName
        from approve_internal_rule_person p
        inner join approve_internal_rule r on p.rule_id = r.id
        where r.id = #{ruleId}
        order by p.rank asc
    </select>
</mapper>
