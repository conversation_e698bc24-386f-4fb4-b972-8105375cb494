package com.coocaa.ad.cheese.ai.embedding;

import com.coocaa.ad.cheese.ai.service.AiEmbeddingModelFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.AbstractEmbeddingModel;
import org.springframework.ai.embedding.Embedding;
import org.springframework.ai.embedding.EmbeddingOptions;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 豆包AI嵌入模型实现
 *
 * 参考yudao-module-ai-server的实现逻辑
 * 继承Spring AI的AbstractEmbeddingModel
 * 基于现有的AiEmbeddingModelFactory实现
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Slf4j
public class DoubaoEmbeddingModel extends AbstractEmbeddingModel {

    private static final String DEFAULT_MODEL_NAME = "doubao-embedding-001";

    private final AiEmbeddingModelFactory embeddingModelFactory;
    private final String modelName;
    private final DoubaoEmbeddingOptions defaultOptions;

    /**
     * 构造函数
     *
     * @param embeddingModelFactory 嵌入模型工厂
     */
    public DoubaoEmbeddingModel(AiEmbeddingModelFactory embeddingModelFactory) {
        this(embeddingModelFactory, DEFAULT_MODEL_NAME);
    }

    /**
     * 构造函数
     *
     * @param embeddingModelFactory 嵌入模型工厂
     * @param modelName 模型名称
     */
    public DoubaoEmbeddingModel(AiEmbeddingModelFactory embeddingModelFactory, String modelName) {
        this(embeddingModelFactory, modelName, DoubaoEmbeddingOptions.builder().build());
    }

    /**
     * 完整构造函数
     *
     * @param embeddingModelFactory 嵌入模型工厂
     * @param modelName 模型名称
     * @param options 默认选项
     */
    public DoubaoEmbeddingModel(AiEmbeddingModelFactory embeddingModelFactory, String modelName,
                               DoubaoEmbeddingOptions options) {
        Assert.notNull(embeddingModelFactory, "AiEmbeddingModelFactory不能为null");
        Assert.hasText(modelName, "模型名称不能为空");
        Assert.notNull(options, "DoubaoEmbeddingOptions不能为null");

        this.embeddingModelFactory = embeddingModelFactory;
        this.modelName = modelName;
        this.defaultOptions = options;
    }

    @Override
    public EmbeddingResponse call(EmbeddingRequest request) {
        log.debug("开始调用豆包嵌入模型，输入数量: {}", request.getInstructions().size());

        try {
            // 1. 获取嵌入模型包装器
            AiEmbeddingModelFactory.EmbeddingModelWrapper modelWrapper =
                    embeddingModelFactory.getDefaultEmbeddingModel();

            // 2. 处理输入文本
            List<String> inputs = request.getInstructions();
            List<Embedding> embeddings = new ArrayList<>();
            AtomicInteger indexCounter = new AtomicInteger(0);

            // 3. 批量处理或单个处理
            if (inputs.size() > 1) {
                // 批量处理
                List<float[]> vectors = modelWrapper.embedBatch(inputs);
                for (float[] vector : vectors) {
                    embeddings.add(new Embedding(vector, indexCounter.getAndIncrement()));
                }
            } else {
                // 单个处理
                for (String input : inputs) {
                    float[] vector = modelWrapper.embed(input);
                    embeddings.add(new Embedding(vector, indexCounter.getAndIncrement()));
                }
            }

            log.debug("豆包嵌入模型调用完成，返回{}个向量", embeddings.size());
            return new EmbeddingResponse(embeddings);

        } catch (Exception e) {
            log.error("豆包嵌入模型调用失败", e);
            throw new RuntimeException("嵌入模型调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Double> embed(Document document) {
        log.debug("嵌入单个文档，内容长度: {}", document.getText().length());

        try {
            AiEmbeddingModelFactory.EmbeddingModelWrapper modelWrapper =
                    embeddingModelFactory.getDefaultEmbeddingModel();

            float[] vector = modelWrapper.embed(document.getText());
            return convertToDoubleList(vector);

        } catch (Exception e) {
            log.error("文档嵌入失败", e);
            throw new RuntimeException("文档嵌入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换float数组为Double列表
     */
    private List<Double> convertToDoubleList(float[] floatArray) {
        List<Double> doubleList = new ArrayList<>();
        for (float value : floatArray) {
            doubleList.add((double) value);
        }
        return doubleList;
    }

    /**
     * 获取模型名称
     */
    public String getModelName() {
        return this.modelName;
    }

    /**
     * 获取默认选项
     */
    public DoubaoEmbeddingOptions getDefaultOptions() {
        return this.defaultOptions;
    }
}
