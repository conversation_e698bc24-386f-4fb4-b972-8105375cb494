package com.coocaa.ad.cheese.ai.embedding;

import com.coocaa.ad.cheese.ai.service.AiEmbeddingModelFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.AbstractEmbeddingModel;
import org.springframework.ai.embedding.Embedding;
import org.springframework.ai.embedding.EmbeddingOptions;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 豆包AI嵌入模型实现
 *
 * 参考yudao-module-ai-server的实现逻辑
 * 继承Spring AI的AbstractEmbeddingModel
 * 基于现有的AiEmbeddingModelFactory实现
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Slf4j
public class DoubaoEmbeddingModel extends AbstractEmbeddingModel {

    private static final String DEFAULT_MODEL_NAME = "doubao-embedding-001";

    private final AiEmbeddingModelFactory embeddingModelFactory;
    private final String modelName;
    private final DoubaoEmbeddingOptions defaultOptions;

    /**
     * 构造函数
     *
     * @param embeddingModelFactory 嵌入模型工厂
     */
    public DoubaoEmbeddingModel(AiEmbeddingModelFactory embeddingModelFactory) {
        this(embeddingModelFactory, DEFAULT_MODEL_NAME);
    }

    /**
     * 构造函数
     *
     * @param embeddingModelFactory 嵌入模型工厂
     * @param modelName 模型名称
     */
    public DoubaoEmbeddingModel(AiEmbeddingModelFactory embeddingModelFactory, String modelName) {
        this(embeddingModelFactory, modelName, DoubaoEmbeddingOptions.builder().build());
    }

    /**
     * 完整构造函数
     *
     * @param embeddingModelFactory 嵌入模型工厂
     * @param modelName 模型名称
     * @param options 默认选项
     */
    public DoubaoEmbeddingModel(AiEmbeddingModelFactory embeddingModelFactory, String modelName,
                               DoubaoEmbeddingOptions options) {
        Assert.notNull(embeddingModelFactory, "AiEmbeddingModelFactory不能为null");
        Assert.hasText(modelName, "模型名称不能为空");
        Assert.notNull(options, "DoubaoEmbeddingOptions不能为null");

        this.embeddingModelFactory = embeddingModelFactory;
        this.modelName = modelName;
        this.defaultOptions = options;
    }

    @Override
    public EmbeddingResponse call(EmbeddingRequest request) {
        log.debug("开始调用豆包嵌入模型，输入数量: {}", request.getInstructions().size());

        try {
            // 1. 获取嵌入模型包装器
            AiEmbeddingModelFactory.EmbeddingModelWrapper modelWrapper =
                    embeddingModelFactory.getDefaultEmbeddingModel();

            // 2. 处理输入文本
            List<String> inputs = request.getInstructions();
            List<Embedding> embeddings = new ArrayList<>();
            AtomicInteger indexCounter = new AtomicInteger(0);

            // 3. 批量处理或单个处理
            if (inputs.size() > 1) {
                // 批量处理
                List<float[]> vectors = modelWrapper.embedBatch(inputs);
                for (float[] vector : vectors) {
                    embeddings.add(new Embedding(vector, indexCounter.getAndIncrement()));
                }
            } else {
                // 单个处理
                for (String input : inputs) {
                    float[] vector = modelWrapper.embed(input);
                    embeddings.add(new Embedding(vector, indexCounter.getAndIncrement()));
                }
            }

            log.debug("豆包嵌入模型调用完成，返回{}个向量", embeddings.size());
            return new EmbeddingResponse(embeddings);

        } catch (Exception e) {
            log.error("豆包嵌入模型调用失败", e);
            throw new RuntimeException("嵌入模型调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Double> embed(Document document) {
        log.debug("嵌入单个文档，内容长度: {}", document.getText().length());

        try {
            AiEmbeddingModelFactory.EmbeddingModelWrapper modelWrapper =
                    embeddingModelFactory.getDefaultEmbeddingModel();

            float[] vector = modelWrapper.embed(document.getText());
            return convertToDoubleList(vector);

        } catch (Exception e) {
            log.error("文档嵌入失败", e);
            throw new RuntimeException("文档嵌入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 多模态嵌入方法
     * 支持文本、图像、视频的混合输入
     *
     * @param inputs 多模态输入列表
     * @return 嵌入向量
     */
    public List<Double> embedMultimodal(List<MultimodalInput> inputs) {
        log.debug("多模态嵌入，输入数量: {}", inputs.size());

        try {
            // 验证输入
            for (MultimodalInput input : inputs) {
                if (!input.isValid()) {
                    throw new IllegalArgumentException("无效的多模态输入: " + input.getDescription());
                }
            }

            // 检查是否支持多模态
            if (defaultOptions.getMultimodal() != null && !defaultOptions.getMultimodal()) {
                log.warn("当前模型配置不支持多模态，回退到文本嵌入");
                return embedTextOnly(inputs);
            }

            // 使用嵌入模型工厂处理多模态输入
            AiEmbeddingModelFactory.EmbeddingModelWrapper modelWrapper =
                    embeddingModelFactory.getDefaultEmbeddingModel();

            // 将多模态输入转换为文本描述（简化处理）
            StringBuilder combinedText = new StringBuilder();
            for (MultimodalInput input : inputs) {
                switch (input.getType()) {
                    case "text":
                        combinedText.append(input.getText()).append(" ");
                        break;
                    case "image_url":
                        combinedText.append("[图像: ").append(input.getImageUrl().getUrl()).append("] ");
                        break;
                    case "video_url":
                        combinedText.append("[视频: ").append(input.getVideoUrl().getUrl()).append("] ");
                        break;
                }
            }

            float[] vector = modelWrapper.embed(combinedText.toString().trim());
            return convertToDoubleList(vector);

        } catch (Exception e) {
            log.error("多模态嵌入失败", e);
            throw new RuntimeException("多模态嵌入失败: " + e.getMessage(), e);
        }
    }

    /**
     * 仅处理文本输入（回退方法）
     */
    private List<Double> embedTextOnly(List<MultimodalInput> inputs) {
        StringBuilder textContent = new StringBuilder();

        for (MultimodalInput input : inputs) {
            if ("text".equals(input.getType())) {
                textContent.append(input.getText()).append(" ");
            }
        }

        if (textContent.length() == 0) {
            throw new IllegalArgumentException("没有找到文本内容进行嵌入");
        }

        AiEmbeddingModelFactory.EmbeddingModelWrapper modelWrapper =
                embeddingModelFactory.getDefaultEmbeddingModel();

        float[] vector = modelWrapper.embed(textContent.toString().trim());
        return convertToDoubleList(vector);
    }

    /**
     * 转换float数组为Double列表
     */
    private List<Double> convertToDoubleList(float[] floatArray) {
        List<Double> doubleList = new ArrayList<>();
        for (float value : floatArray) {
            doubleList.add((double) value);
        }
        return doubleList;
    }

    /**
     * 获取模型名称
     */
    public String getModelName() {
        return this.modelName;
    }

    /**
     * 获取默认选项
     */
    public DoubaoEmbeddingOptions getDefaultOptions() {
        return this.defaultOptions;
    }

    /**
     * 检查是否支持多模态
     */
    public boolean isMultimodalSupported() {
        return defaultOptions.getMultimodal() != null && defaultOptions.getMultimodal();
    }

    /**
     * 获取支持的模态类型
     */
    public List<String> getSupportedModalities() {
        List<String> modalities = new ArrayList<>();
        modalities.add("text");

        if (isMultimodalSupported()) {
            modalities.add("image_url");

            // 检查是否支持视频（仅特定版本支持）
            if (modelName.contains("250615") || modelName.compareTo("doubao-embedding-vision-250615") >= 0) {
                modalities.add("video_url");
            }
        }

        return modalities;
    }
}
