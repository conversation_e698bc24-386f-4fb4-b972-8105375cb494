package com.coocaa.ad.cheese.ai.embedding;

import com.volcengine.ark.runtime.service.ArkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.embedding.AbstractEmbeddingModel;
import org.springframework.ai.embedding.Embedding;
import org.springframework.ai.embedding.EmbeddingOptions;
import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;

import org.springframework.ai.model.ModelOptionsUtils;

import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

/**
 * 豆包AI嵌入模型实现
 *
 * 参考Spring AI标准实现（如MiniMaxEmbeddingModel）
 * 基于豆包ArkService实现多模态向量化
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Slf4j
public class DoubaoEmbeddingModel extends AbstractEmbeddingModel {

    private static final String DEFAULT_MODEL_NAME = "doubao-embedding-vision-250615";

    private final ArkService arkService;
    private final String modelName;
    private final DoubaoEmbeddingOptions defaultOptions;
    private final RetryTemplate retryTemplate;
    private final MetadataMode metadataMode;

    /**
     * 构造函数
     *
     * @param arkService ArkService实例
     */
    public DoubaoEmbeddingModel(ArkService arkService) {
        this(arkService, MetadataMode.EMBED);
    }

    /**
     * 构造函数
     *
     * @param arkService ArkService实例
     * @param metadataMode 元数据模式
     */
    public DoubaoEmbeddingModel(ArkService arkService, MetadataMode metadataMode) {
        this(arkService, metadataMode, DoubaoEmbeddingOptions.builder()
                .model(DEFAULT_MODEL_NAME)
                .build());
    }

    /**
     * 构造函数
     *
     * @param arkService ArkService实例
     * @param metadataMode 元数据模式
     * @param options 默认选项
     */
    public DoubaoEmbeddingModel(ArkService arkService, MetadataMode metadataMode, DoubaoEmbeddingOptions options) {
        this(arkService, metadataMode, options, new RetryTemplate());
    }

    /**
     * 完整构造函数
     *
     * @param arkService ArkService实例
     * @param metadataMode 元数据模式
     * @param options 默认选项
     * @param retryTemplate 重试模板
     */
    public DoubaoEmbeddingModel(ArkService arkService, MetadataMode metadataMode,
                               DoubaoEmbeddingOptions options, RetryTemplate retryTemplate) {
        Assert.notNull(arkService, "ArkService不能为null");
        Assert.notNull(metadataMode, "MetadataMode不能为null");
        Assert.notNull(options, "DoubaoEmbeddingOptions不能为null");
        Assert.notNull(retryTemplate, "RetryTemplate不能为null");

        this.arkService = arkService;
        this.metadataMode = metadataMode;
        this.modelName = options.getModel() != null ? options.getModel() : DEFAULT_MODEL_NAME;
        this.defaultOptions = options;
        this.retryTemplate = retryTemplate;
    }

    @Override
    public float[] embed(Document document) {
        Assert.notNull(document, "Document must not be null");
        return embed(document.getFormattedContent(metadataMode));
    }

    @Override
    public EmbeddingResponse call(EmbeddingRequest request) {
        log.debug("开始调用豆包嵌入模型，输入数量: {}", request.getInstructions().size());

        DoubaoEmbeddingOptions requestOptions = mergeOptions(request.getOptions(), defaultOptions);

        return retryTemplate.execute(ctx -> {
            try {
                // 调用豆包多模态嵌入API
                List<String> inputs = request.getInstructions();
                List<Embedding> embeddings = new ArrayList<>();

                // 目前使用模拟实现，实际需要调用豆包API
                for (int i = 0; i < inputs.size(); i++) {
                    float[] vector = embedSingle(inputs.get(i));
                    embeddings.add(new Embedding(vector, i));
                }

                log.debug("豆包嵌入模型调用完成，返回{}个向量", embeddings.size());
                return new EmbeddingResponse(embeddings);

            } catch (Exception e) {
                log.error("豆包嵌入模型调用失败", e);
                throw new RuntimeException("嵌入模型调用失败: " + e.getMessage(), e);
            }
        });
    }

    /**
     * 单个文本嵌入（私有方法）
     */
    private float[] embedSingle(String text) {
        // 目前使用模拟实现，实际需要调用豆包多模态嵌入API
        // TODO: 集成真实的豆包多模态嵌入API
        return generateMockVector(3072, text);
    }

    /**
     * 合并选项
     */
    private DoubaoEmbeddingOptions mergeOptions(EmbeddingOptions requestOptions, DoubaoEmbeddingOptions defaultOptions) {
        if (requestOptions == null) {
            return defaultOptions;
        }

        DoubaoEmbeddingOptions mergedOptions = DoubaoEmbeddingOptions.builder()
                .from(defaultOptions)
                .build();

        // 使用ModelOptionsUtils合并选项
        return ModelOptionsUtils.merge(requestOptions, mergedOptions, DoubaoEmbeddingOptions.class);
    }

    /**
     * 生成模拟向量（用于演示）
     */
    private float[] generateMockVector(int dimension, String seed) {
        int hash = seed.hashCode();
        java.util.Random random = new java.util.Random(hash);

        float[] vector = new float[dimension];
        for (int i = 0; i < dimension; i++) {
            vector[i] = (float) (random.nextGaussian() * 0.1);
        }

        // 归一化
        float norm = 0.0f;
        for (float v : vector) {
            norm += v * v;
        }
        norm = (float) Math.sqrt(norm);

        if (norm > 0) {
            for (int i = 0; i < vector.length; i++) {
                vector[i] /= norm;
            }
        }

        return vector;
    }
}
