package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiRoleEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiRoleService;
import com.coocaa.ad.cheese.ai.convert.AiRoleConvert;
import com.coocaa.ad.cheese.ai.vo.AiRoleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI角色业务服务
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiRoleService {

    private final IAiRoleService aiRoleService;

    /**
     * 获取所有可用角色列表
     *
     * @return 角色列表
     */
    public List<AiRoleVO> getAllRoles() {
        log.info("获取所有可用角色列表");
        
        LambdaQueryWrapper<AiRoleEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiRoleEntity::getDeleteFlag, 0)
               .orderByAsc(AiRoleEntity::getId);
        
        List<AiRoleEntity> entities = aiRoleService.list(wrapper);
        List<AiRoleVO> roles = entities.stream()
                .map(AiRoleConvert.INSTANCE::toVo)
                .collect(Collectors.toList());
        
        log.info("获取角色列表成功，共{}个角色", roles.size());
        return roles;
    }

    /**
     * 根据ID获取角色
     *
     * @param roleId 角色ID
     * @return 角色信息，如果不存在返回null
     */
    public AiRoleVO getRoleById(Long roleId) {
        if (roleId == null) {
            return null;
        }
        
        log.info("根据ID获取角色，角色ID: {}", roleId);
        
        AiRoleEntity entity = aiRoleService.getById(roleId);
        if (entity == null || entity.getDeleteFlag() == 1) {
            log.warn("角色不存在或已删除，角色ID: {}", roleId);
            return null;
        }
        
        AiRoleVO role = AiRoleConvert.INSTANCE.toVo(entity);
        log.info("获取角色成功，角色ID: {}, 角色名称: {}", roleId, role.getRoleName());
        return role;
    }
}
