package com.coocaa.ad.cheese.authority.common.db.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class TaskSearchDTO {

    /**
     * 规则编号
     */
    private Integer ruleCode;

    /**
     * 任务状态
     */
    private List<String> status;

    /**
     * 用户ID，默认为当前登陆人
     */
    private Integer userId;

    /**
     * 是否有有用户筛选，默认为true，有用户筛选
     */
    private Boolean userFlag = true;

    /**
     * 排序字段，开始时间：startTime,默认开始时间
     */
    private String sortFiled = "startTime";

    /**
     * 排序字段，顺序：asc,倒序：desc，默认倒序
     */
    private String sortRule = "desc";


}
