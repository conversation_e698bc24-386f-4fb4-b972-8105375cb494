package com.coocaa.ad.cheese.authority;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.coocaa.ad.cheese.authority.common.db.entity.ResourceEntity;
import com.coocaa.ad.cheese.authority.common.db.service.IResourceService;
import com.coocaa.ad.cheese.authority.common.tools.enums.ResourceTypeEnum;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import com.coocaa.ad.cheese.authority.service.ResourceService;
import com.coocaa.ad.cheese.authority.vo.ResourceVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Consumer;

/**
 * 资源管理测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-28
 */
@DisplayName("资源管理")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ResourceServiceTest extends BaseTest {
    @Autowired
    private ResourceService sysResourceService;

    @Autowired
    private IResourceService resourceService;


    @Test
    @DisplayName("资源创建")
    public void testCreateAndDeleteResource() {
        String resourceCode = "TEST-CODE-" + ThreadLocalRandom.current().nextInt(11, 99);

        ResourceVO resource = generateResource(vo -> {
            vo.setParentId(1);
            vo.setName("用于测试的角色");
            vo.setCode(resourceCode);
            vo.setType(ResourceTypeEnum.MENU.getCode());
            vo.setUri("/sys/role/manage");
        });

        ResultTemplate<Boolean> result = sysResourceService.createResource(resource);
        Assertions.assertNotNull(result.getSuccess(), "创建成功");
        Assertions.assertTrue(result.getData(), "创建资源");

        // 创建后查出来检查
        ResourceEntity entity = resourceService.getOne(Wrappers.<ResourceEntity>lambdaQuery().eq(ResourceEntity::getCode, resourceCode));
        Assertions.assertEquals(resource.getName(), entity.getName(), "名称");
        Assertions.assertEquals(resource.getCode(), entity.getCode(), "编码");
        Assertions.assertEquals(resource.getType(), entity.getType(), "类型");
        Assertions.assertEquals(resource.getUri(), entity.getUri(), "地址");

        // 删除测试数据
        resource.setId(entity.getId());
        sysResourceService.deleteResource(resource);
    }


    @Test
    @DisplayName("资源修改")
    public void testUpdateResource() {
        String resourceCode = "TEST-CODE-" + ThreadLocalRandom.current().nextInt(11, 99);

        ResourceVO resource = generateResource(vo -> {
            vo.setParentId(1);
            vo.setName("用于测试的资源");
            vo.setCode(resourceCode);
            vo.setType(ResourceTypeEnum.MENU.getCode());
            vo.setUri("/sys/resource/manage");
        });

        ResultTemplate<Boolean> result = sysResourceService.createResource(resource);
        Assertions.assertNotNull(result.getSuccess(), "创建成功");
        Assertions.assertTrue(result.getData(), "创建资源");

        // 创建后查出来检查
        ResourceEntity entity = resourceService.getOne(Wrappers.<ResourceEntity>lambdaQuery().eq(ResourceEntity::getCode, resourceCode));
        resource.setId(entity.getId());
        resource.setName("用于测试的角色-修改");
        resource.setCode("UPDATE-" + resource.getCode());
        result = sysResourceService.updateResource(resource);
        Assertions.assertNotNull(result.getSuccess(), "修改成功");
        Assertions.assertTrue(result.getData(), "修改资源");


        // 将修改后的信息查出来检查
        entity = resourceService.getById(entity.getId());
        Assertions.assertEquals(resource.getName(), entity.getName(), "名称");
        Assertions.assertEquals(resource.getCode(), entity.getCode(), "编码");
        Assertions.assertEquals(resource.getType(), entity.getType(), "类型");
        Assertions.assertEquals(resource.getUri(), entity.getUri(), "地址");

        // 删除测试数据
        resource.setId(entity.getId());
        sysResourceService.deleteResource(resource);
    }

    /**
     * 创建资源，自定义属性优先
     */
    private ResourceVO generateResource(Consumer<ResourceVO> consumer) {
        ResourceVO vo = new ResourceVO();
        vo.setStatus(true);
        vo.setDeleteFlag(false);
        vo.setCreateTime(NOW);
        vo.setCreator(USER_ID);
        vo.setUpdateTime(NOW);
        vo.setOperator(USER_ID);

        // 可自定义一些属性
        Optional.ofNullable(consumer).ifPresent(c -> c.accept(vo));

        return vo;
    }
}
