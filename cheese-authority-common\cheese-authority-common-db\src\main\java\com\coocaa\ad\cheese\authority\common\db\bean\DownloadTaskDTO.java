
package com.coocaa.ad.cheese.authority.common.db.bean;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 下载任务表
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Data
@Accessors(chain = true)
public class DownloadTaskDTO implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 系统类型
     */
    private String sysType;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类别
     */
    private String category;

    /**
     * 状态
     */
    private String status;

    /**
     * 执行时的参数
     */
    private String executeParams;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}