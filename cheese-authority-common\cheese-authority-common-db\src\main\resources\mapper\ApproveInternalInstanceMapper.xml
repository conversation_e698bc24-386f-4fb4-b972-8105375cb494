<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalInstanceMapper">


    <select id="listForTask"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalInstanceEntity">
        select id,rule_id as ruleId,rule_code as ruleCode,instance_code as instanceCode,approval_name as approvalName
        ,approval_result as approvalResult,approval_status as approvalStatus,cancel_reason as cancelReason,user_id as userId
        ,end_time as endTime,create_time as createTime
        from approve_internal_instance
        where user_id = #{dto.userId}
        <if test="dto.status != null and dto.status.size > 0">
            and approval_status in
            <foreach collection="dto.status" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="dto.ruleCode != null">
            and rule_code = #{dto.ruleCode}
        </if>
        order by ${dto.sortFiled} ${dto.sortRule}
    </select>
</mapper>
