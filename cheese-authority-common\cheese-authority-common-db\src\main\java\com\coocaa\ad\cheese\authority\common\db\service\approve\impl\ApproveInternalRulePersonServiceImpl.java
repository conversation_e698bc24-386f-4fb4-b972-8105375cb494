package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRulePersonEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRulePersonMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalRulePersonService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalRulePersonServiceImpl extends ServiceImpl<ApproveInternalRulePersonMapper, ApproveInternalRulePersonEntity> implements IApproveInternalRulePersonService {

    @Override
    public List<ApproveInternalRulePersonEntity> getPersonByRuleId(Integer ruleId) {
        return this.baseMapper.getPersonByRuleId(ruleId);
    }

    @Override
    public void updateAfterRank(Integer ruleId, Integer rank) {
        this.baseMapper.updateAfterRank(ruleId, rank);
    }
}
