package com.coocaa.ad.cheese.authority.common.db.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InstanceSearchDTO {

    /**
     * 规则编号
     */
    private Integer ruleCode;

    /**
     * 任务状态
     */
    private List<String> status;

    /**
     * 用户ID，默认为当前登陆人
     */
    private Integer userId;

    /**
     * 排序字段，创建时间：createTime,默认创建时间
     */
    private String sortFiled = "createTime";

    /**
     * 排序字段，顺序：asc,倒序：desc，默认倒序
     */
    private String sortRule = "desc";


}
