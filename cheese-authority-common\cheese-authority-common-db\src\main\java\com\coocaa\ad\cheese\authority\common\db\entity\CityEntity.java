package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.coocaa.ad.cheese.authority.common.tools.translate.StatusTranslate;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.ChangeExtract;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.CompareField;
import com.coocaa.ad.cheese.authority.common.tools.annotation.EncryptField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 城市
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("city")
public class CityEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 父ID
     */
    private Integer parentId;

    /**
     * 名称
     */
    @CompareField(description = "区县名称")
    private String name;

    /**
     * 国标编码
     */
    private String gbCode;

    /**
     * 业务编码
     */
    private String bzCode;

    /**
     * 状态 [0:禁用, 1:启用]
     */
    @CompareField(description = "状态",translateType = StatusTranslate.class)
    private Boolean status;

    /**
     * 排序(自然顺序，越小越靠前)
     */
    private Integer rank;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 删除标记
     * */
    private Boolean deleteFlag;

    /**
     * 层级 [-1：省级，1:城市, 2:区县]
     */
    private int level;

    /**
     * ids
     * */
    private String parentIds;

    /**
     * 经度（AES加密存储）
     */
    @EncryptField(EncryptField.EncryptType.AES)
    private String longitude;

    /**
     * 纬度（AES加密存储）
     */
    @EncryptField(EncryptField.EncryptType.AES)
    private String latitude;

}
