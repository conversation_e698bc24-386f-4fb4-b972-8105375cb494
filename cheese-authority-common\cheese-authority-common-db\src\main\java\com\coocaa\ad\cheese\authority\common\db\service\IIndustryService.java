package com.coocaa.ad.cheese.authority.common.db.service;

import com.coocaa.ad.cheese.authority.common.db.entity.IndustryEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 行业 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IIndustryService extends IService<IndustryEntity> {

    IndustryEntity queryFirstLevelByName(String name);

    int firstLevelIndustryCount();

    void updateIndustryByParentId(Boolean enable, Integer id);

    List<IndustryEntity> listIndustry(String name);

    int getMaxSecondByParentId(Integer parentId);

    List<IndustryEntity> listSubIndustry(Integer parentId, String name);

    List<IndustryEntity> listByParentId(Integer id);

    /**
     * 通过行业编码数组查询行业信息
     * @param codes 行业编码数组
     * @return 行业信息列表
     */
    List<IndustryEntity> listByCodes(List<String> codes);

    List<IndustryEntity> listSecond();

    IndustryEntity querySecondLevelByName(String name);

    /**
     * 获取所有二级行业
     * @return 二级行业列表
     */
    List<IndustryEntity> listAllSecondWithParent();
}
