package com.coocaa.ad.cheese.ai.common.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI模型实体
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@TableName("ai_model")
public class AiModelEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型标识符（API调用时使用）
     */
    private String modelKey;

    /**
     * 模型提供商
     */
    private String provider;

    /**
     * 模型类型（chat-聊天模型，embedding-嵌入模型，image-图像模型等）
     */
    private String modelType;

    /**
     * 模型描述
     */
    private String description;

    /**
     * 最大上下文长度
     */
    private Integer maxTokens;

    /**
     * 输入价格（每1000tokens）
     */
    private BigDecimal inputPrice;

    /**
     * 输出价格（每1000tokens）
     */
    private BigDecimal outputPrice;

    /**
     * 是否启用（0-禁用，1-启用）
     */
    private Integer enabled;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 删除标记（0-正常，1-删除）
     */
    private Integer deleteFlag;
}
