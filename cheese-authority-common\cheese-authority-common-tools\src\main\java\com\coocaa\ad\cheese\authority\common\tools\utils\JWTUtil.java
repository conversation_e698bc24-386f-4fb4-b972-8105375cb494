package com.coocaa.ad.cheese.authority.common.tools.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/10/22
 */

@Component
public class JWTUtil implements InitializingBean {

    @Value("${jwt.secret}")
    private String secretKey;

    @Value("${jwt.exp}")
    private  int exp;

    @Value("${jwt.h5.exp}")
    private  int h5Exp;

    private static int expireTime ;

    private static int h5ExpireTime ;

    private static String key;

    private static Algorithm algorithm;

    @Value("${jwt.refresh.exp}")
    private  int refreshExp;

    @Value("${jwt.h5.refresh.exp}")
    private  int refreshExpH5;
    private static int refreshExpTime ;
    private static int h5RefreshExpTime ;
    @PostConstruct
    public void init() {
        expireTime = exp;
        h5ExpireTime = h5Exp;
        this.refreshExpTime = refreshExp;
        this.h5RefreshExpTime = refreshExpH5;
    }

    public static String getToken(String name, Integer id,String userCode,String plat) {
        return getExpireCommon(name, id,plat.equals("h5")?h5ExpireTime:expireTime,userCode);
    }

    public static String getExpireCommon(String name, Integer id, int exp,String userCode) {
        // 生成Token
        return JWT.create()
                .withIssuer("auth0")
                .withClaim("id", id)
                .withClaim("name", name)
                .withClaim("userCode",userCode)
                .withClaim("timestamp",System.currentTimeMillis())
                .withExpiresAt(Instant.now().plusSeconds(exp))
                .sign(algorithm);
    }

    public static DecodedJWT  parseToken(String token) {
        Algorithm algorithm = Algorithm.HMAC256(key);
        JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer("auth0")
                .build();

        return verifier.verify(token);

    }

    public static Integer getUserIdByToken(String token){
        return parseToken(token).getClaim("id").asInt();
    }

    public static String getRefreshToken(String name, Integer id,String userCode,String plat) {
        //24小时过期
        return getExpireCommon(name, id,plat.equals("h5")?h5RefreshExpTime:refreshExpTime ,userCode);
    }

    /**
     * 判断是否过期
     * */
    public static boolean expire(long exp){
        return System.currentTimeMillis()>exp;
    }
    @Override
    public void afterPropertiesSet() throws Exception {
       // key = Keys.hmacShaKeyFor(secretKey.getBytes(StandardCharsets.UTF_8));
        this.key = secretKey;
        this.algorithm = Algorithm.HMAC256(key);
    }

}
