package com.coocaa.ad.cheese.ai.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.ai.common.db.bean.AiChatDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiChatEntity;

/**
 * AI Chat 消息服务接口
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
public interface IAiChatService extends IService<AiChatEntity> {

    /**
     * 分页查询
     *
     * @param aiChat 查询条件
     * @param page   分页对象
     * @return 分页结果
     */
    IPage<AiChatEntity> pageList(IPage<AiChatEntity> page, AiChatDTO aiChat);
}