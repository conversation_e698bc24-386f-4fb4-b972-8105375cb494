package com.coocaa.ad.cheese.authority.common.tools.query.utils;

/**
 * 类HumpLineUtil实现描述:
 * 驼峰与下划线互转
 *
 * <AUTHOR>
 * @date 2022年05月08 17:37
 */
public class HumpLineUtil {

    /**
     * 驼峰转下划线,最后转为小写
     *
     * @param str
     * @return
     */
    public static String humpToLine(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        StringBuilder sb = new StringBuilder(str.length() + 5);
        for (char c : str.toCharArray()) {
            if (Character.isUpperCase(c)) {
                sb.append('_').append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 下划线转驼峰,正常输出
     *
     * @param str
     * @return
     */
    public static String lineToHump(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        StringBuilder sb = new StringBuilder(str.length());
        boolean nextUpperCase = false;
        for (char c : str.toCharArray()) {
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    sb.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    sb.append(c);
                }
            }
        }
        return sb.toString();
    }

}
