package com.coocaa.ad.cheese.authority.common.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.RoleEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserRoleEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserRoleMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IRoleService;
import com.coocaa.ad.cheese.authority.common.db.service.IUserRoleService;
import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRoleEntity> implements IUserRoleService {
    private final IRoleService roleService;

    @Override
    public List<RoleEntity> listValidRoles(Integer userId, boolean expand) {
        Objects.requireNonNull(userId, "用户ID不能为空");

        // 超级管理员，直接返回特殊角色
        if (Objects.equals(userId, SysConstant.DEFAULT_SUPER_ADMIN_ID)) {
            return getAllRoles(expand);
        }

        // 查询用户角色关系
        List<UserRoleEntity> userRoles = list(Wrappers.<UserRoleEntity>lambdaQuery()
                .select(UserRoleEntity::getUserId, UserRoleEntity::getRoleId)
                .eq(UserRoleEntity::getUserId, userId));
        if (CollectionUtil.isEmpty(userRoles)) return Collections.emptyList();
        Set<Integer> roleIds = userRoles.stream().map(UserRoleEntity::getRoleId).collect(Collectors.toSet());

        // 授予了全部角色
        if (roleIds.contains(SysConstant.DEFAULT_ALL_ID)) {
            return getAllRoles(expand);
        }

        // 没有角色
        roleIds.remove(SysConstant.DEFAULT_ALL_ID);
        if (CollectionUtil.isEmpty(roleIds)) {
            return Collections.emptyList();
        }

        // 查询有效的角色
        return roleService.list(Wrappers.<RoleEntity>lambdaQuery()
                .select(RoleEntity::getId, RoleEntity::getName, RoleEntity::getDescription)
                .in(RoleEntity::getId, roleIds)
                .eq(RoleEntity::getStatus, true));
    }

    @Override
    public Map<Integer, List<RoleEntity>> listValidRoles(Collection<Integer> userIds) {
        Objects.requireNonNull(userIds, "用户ID不能为空");

        // 查询用户的角色
        List<UserRoleEntity> userRoles = lambdaQuery()
                .select(UserRoleEntity::getUserId, UserRoleEntity::getRoleId)
                .in(UserRoleEntity::getUserId, userIds).list();
        if (CollectionUtil.isEmpty(userRoles)) {
            return Collections.emptyMap();
        }

        // 提取用户角色对应的角色ID
        Set<Integer> roleIds = Sets.newHashSetWithExpectedSize(userRoles.size());
        Map<Integer, Set<Integer>> userRoleIdMap = userRoles.stream()
                .filter(item -> Objects.nonNull(item.getRoleId()))
                .peek(item -> roleIds.add(item.getRoleId()))
                .collect(Collectors.groupingBy(UserRoleEntity::getUserId,
                        Collectors.mapping(UserRoleEntity::getRoleId, Collectors.toSet())));

        // 查询有效的角色
        roleIds.remove(SysConstant.DEFAULT_ALL_ID);
        List<RoleEntity> roles = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(roleIds)) {
            roles = roleService.list(Wrappers.lambdaQuery(RoleEntity.class)
                    .select(RoleEntity::getId, RoleEntity::getName, RoleEntity::getDescription)
                    .in(CollectionUtils.isNotEmpty(roleIds), RoleEntity::getId, roleIds)
                    .eq(RoleEntity::getStatus, true)
            );
        }

        // 组装用户角色数据
        Map<Integer, RoleEntity> roleMap = CollectionUtils.isEmpty(roles)
                ? Collections.emptyMap()
                : roles.stream().collect(Collectors.toMap(RoleEntity::getId, Function.identity()));

        Map<Integer, List<RoleEntity>> userRoleMap = new HashMap<>();
        userRoleIdMap.forEach((userId, ids) -> {
            if (ids.contains(SysConstant.DEFAULT_ALL_ID)) {
                userRoleMap.put(userId, getAllRoles(false));
            } else {
                userRoleMap.put(userId, ids.stream().map(roleMap::get).filter(Objects::nonNull).toList());
            }
        });
        return userRoleMap;
    }

    /**
     * 获取所有角色
     */
    private List<RoleEntity> getAllRoles(boolean expand) {
        if (expand) {
            // 查询有效的角色
            return roleService.list(Wrappers.<RoleEntity>lambdaQuery()
                    .select(RoleEntity::getId, RoleEntity::getName, RoleEntity::getDescription)
                    .eq(RoleEntity::getStatus, true));
        } else {
            RoleEntity role = new RoleEntity();
            role.setId(SysConstant.DEFAULT_ALL_ID);
            role.setName("全部");
            return Collections.singletonList(role);
        }
    }
}
