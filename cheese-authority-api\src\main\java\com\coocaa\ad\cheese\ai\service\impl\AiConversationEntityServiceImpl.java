package com.coocaa.ad.cheese.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.ai.entity.AiConversationEntity;
import com.coocaa.ad.cheese.ai.mapper.AiConversationMapper;
import com.coocaa.ad.cheese.ai.service.AiConversationEntityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * AI对话服务实现
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Slf4j
@Service
public class AiConversationEntityServiceImpl extends ServiceImpl<AiConversationMapper, AiConversationEntity> 
        implements AiConversationEntityService {

    @Override
    public AiConversationEntity createConversationForUser(Long userId) {
        log.info("为用户创建新对话，用户ID: {}", userId);
        
        AiConversationEntity conversation = new AiConversationEntity();
        conversation.setUserId(userId);
        conversation.setCreateTime(LocalDateTime.now());
        conversation.setUpdateTime(LocalDateTime.now());
        conversation.setCreateBy("system");
        conversation.setUpdateBy("system");
        conversation.setDeleteFlag(0);
        
        save(conversation);
        
        log.info("对话创建成功，对话ID: {}, 用户ID: {}", conversation.getId(), userId);
        return conversation;
    }

    @Override
    public AiConversationEntity getOrCreateConversationByUserId(Long userId) {
        log.info("获取或创建用户对话，用户ID: {}", userId);
        
        // 查询用户最新的对话
        LambdaQueryWrapper<AiConversationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiConversationEntity::getUserId, userId)
               .eq(AiConversationEntity::getDeleteFlag, 0)
               .orderByDesc(AiConversationEntity::getCreateTime)
               .last("LIMIT 1");
        
        AiConversationEntity conversation = getOne(wrapper);
        
        if (conversation == null) {
            log.info("用户没有现有对话，创建新对话，用户ID: {}", userId);
            conversation = createConversationForUser(userId);
        } else {
            log.info("找到用户现有对话，对话ID: {}, 用户ID: {}", conversation.getId(), userId);
        }
        
        return conversation;
    }

    @Override
    public boolean validateConversationOwnership(Long conversationId, Long userId) {
        log.info("验证对话所有权，对话ID: {}, 用户ID: {}", conversationId, userId);
        
        LambdaQueryWrapper<AiConversationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiConversationEntity::getId, conversationId)
               .eq(AiConversationEntity::getUserId, userId)
               .eq(AiConversationEntity::getDeleteFlag, 0);
        
        long count = count(wrapper);
        boolean isValid = count > 0;
        
        log.info("对话所有权验证结果: {}, 对话ID: {}, 用户ID: {}", isValid, conversationId, userId);
        return isValid;
    }
}
