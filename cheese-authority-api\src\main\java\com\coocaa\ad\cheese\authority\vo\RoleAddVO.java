package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
@Data
public class RoleAddVO {
    @NotBlank(message = "角色名称不能为空")
    @Schema(description = "角色名称", type = "String", example = "销售")
    private String name;

    @Schema(description = "角色描述", type = "String", example = "主要应用于销售人员")
    private String description;

    @Schema(description = "角色拥有的资源ID列表", type = "List")
    private List<Integer> resourceIds;


    @Schema(description = "角色拥有的数据资源ID列表", type = "List")
    private List<Integer> resourceRoleIds;

}
