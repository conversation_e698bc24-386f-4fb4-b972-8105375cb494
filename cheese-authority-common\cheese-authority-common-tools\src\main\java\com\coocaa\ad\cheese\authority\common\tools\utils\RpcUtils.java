package com.coocaa.ad.cheese.authority.common.tools.utils;


import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;

/**
 * <AUTHOR>
 * @since 2024/11/1
 */
public class RpcUtils {
    public static <T> T unBox(ResultTemplate<T> resultTemplate) {
        String code = resultTemplate.getCode();
        if (!code.equals("1")) {
            throw new CommonException(resultTemplate.getMsg());
        }
        return resultTemplate.getData();
    }
}
