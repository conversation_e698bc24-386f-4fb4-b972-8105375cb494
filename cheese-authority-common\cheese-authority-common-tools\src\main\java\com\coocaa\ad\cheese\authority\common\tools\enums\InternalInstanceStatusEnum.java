package com.coocaa.ad.cheese.authority.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@AllArgsConstructor
public enum InternalInstanceStatusEnum {
    WAITING("0141-1", "待审批"),
    PENDING("0141-2", "审批中"),
    CANCEL("0141-3", "已取消"),
    DONE("0141-4", "已完成"),
    ROLLBACK("0141-5", "已驳回");

    private final String code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static InternalInstanceStatusEnum getByCode(String code) {
        for (InternalInstanceStatusEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
