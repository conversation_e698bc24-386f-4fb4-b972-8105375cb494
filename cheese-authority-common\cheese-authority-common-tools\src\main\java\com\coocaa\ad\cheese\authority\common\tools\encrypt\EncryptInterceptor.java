package com.coocaa.ad.cheese.authority.common.tools.encrypt;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.resultset.ResultSetHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * MyBatis加解密拦截器
 * 自动处理数据库操作时的字段加解密
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = ResultSetHandler.class, method = "handleResultSets", args = {Statement.class})
})
public class EncryptInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object target = invocation.getTarget();
        
        if (target instanceof Executor) {
            return handleExecutorUpdate(invocation);
        } else if (target instanceof ResultSetHandler) {
            return handleResultSetQuery(invocation);
        }
        
        return invocation.proceed();
    }
    
    /**
     * 处理更新操作（INSERT、UPDATE、DELETE）
     * 在写入数据库前进行加密
     */
    private Object handleExecutorUpdate(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];
        
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        
        // 只处理INSERT和UPDATE操作
        if (sqlCommandType == SqlCommandType.INSERT || sqlCommandType == SqlCommandType.UPDATE) {
            if (parameter != null) {
                log.info("执行{}操作前进行字段加密，参数类型: {}, MappedStatement ID: {}",
                        sqlCommandType, parameter.getClass().getSimpleName(), mappedStatement.getId());

                if (parameter instanceof Map) {
                    // 处理MyBatis-Plus更新操作时，参数可能被包装在Map中
                    Map<?, ?> map = (Map<?, ?>) parameter;
                    // MyBatis-Plus通常将实体对象放在"et"这个key中
                    if (map.containsKey("et")) {
                        Object entity = map.get("et");
                        if (entity != null) {
                            log.info("从Map中提取到实体对象: {}", entity.getClass().getSimpleName());
                            EncryptUtils.encryptFields(entity);
                        }
                    } else {
                        log.warn("参数是Map类型，但未找到key为'et'的实体对象");
                    }
                } else if (parameter instanceof List) {
                    // 批量操作
                    log.info("批量操作，处理{}个对象", ((List<?>) parameter).size());
                    EncryptUtils.encryptFieldsList((List<?>) parameter);
                } else {
                    // 单个对象操作
                    log.info("单个对象操作，对象: {}", parameter.getClass().getSimpleName());
                    EncryptUtils.encryptFields(parameter);
                }
            }
        }
        
        return invocation.proceed();
    }
    
    /**
     * 处理查询结果
     * 在返回结果前进行解密
     */
    private Object handleResultSetQuery(Invocation invocation) throws Throwable {
        Object result = invocation.proceed();
        
        if (result instanceof List) {
            List<?> list = (List<?>) result;
            if (!list.isEmpty()) {
                log.debug("查询结果解密，结果数量: {}, 类型: {}", 
                         list.size(), list.get(0).getClass().getSimpleName());
                EncryptUtils.decryptFieldsList(list);
            }
        }
        
        return result;
    }
    
    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor || target instanceof ResultSetHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }
    
    @Override
    public void setProperties(Properties properties) {
        // 可以通过properties配置拦截器参数
        log.info("EncryptInterceptor初始化完成");
    }
}
