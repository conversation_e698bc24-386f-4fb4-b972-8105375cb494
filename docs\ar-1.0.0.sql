-- cheese_authority.ai_chat definition

CREATE TABLE `ai_chat`
(
    `id`              BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '编号，作为每条聊天记录的唯一标识符',
    `conversation_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '对话编号',
    `reply_id`        BIGINT(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '回复编号',
    `open_id`         VARCHAR(32)         NOT NULL DEFAULT '0' COMMENT '用户openId',
    `content` TEXT COMMENT '聊天内容',
    `message_type`    VARCHAR(10)         NOT NULL DEFAULT '0' COMMENT '消息类型',
    `user_context` TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否携带上下文',
    `create_time`     DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`         INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_time`     DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator`        INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '操作人',
    `delete_flag`     TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记 0正常 1删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='AI Chat 消息';


-- cheese_authority.ai_user definition

CREATE TABLE `ai_user`
(
    `id`          BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `open_id`     VARCHAR(32)         NOT NULL DEFAULT '0' COMMENT '用户ID',
    `name`        VARCHAR(32)         NOT NULL DEFAULT '' COMMENT '名称',
    `avatar`      VARCHAR(200)                 DEFAULT '' COMMENT '头像',
    `mobile`      VARCHAR(200)                 DEFAULT '' COMMENT '手机',
    `create_time` DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `creator`     INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_time` DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `operator`    INT(11) UNSIGNED    NOT NULL DEFAULT '0' COMMENT '操作人',
    `delete_flag` TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记 0正常 1删除',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT ='AI Chat 对话';