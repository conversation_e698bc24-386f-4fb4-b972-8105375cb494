package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.bean.TaskSearchDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalTaskEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalTaskMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalTaskService;
import com.coocaa.ad.cheese.authority.common.db.vo.approve.TaskDealCountSelectVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalTaskServiceImpl extends ServiceImpl<ApproveInternalTaskMapper, ApproveInternalTaskEntity> implements IApproveInternalTaskService {


    @Override
    public IPage<ApproveInternalTaskEntity> listForTask(IPage<ApproveInternalTaskEntity> page, TaskSearchDTO dto) {
        return this.baseMapper.listForTask(page, dto);
    }

    @Override
    public List<TaskDealCountSelectVO> getUserDealCount(Integer userId, List<Long> codes) {
        return this.baseMapper.getUserDealCount(userId, codes);
    }
}
