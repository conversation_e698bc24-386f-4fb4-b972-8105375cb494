package com.coocaa.ad.cheese.authority.common.tools.utils.date;

import org.joda.time.DateTime;
import org.joda.time.Days;

import java.util.Date;

/**
 * 类DateUtil实现描述:
 *
 * <AUTHOR>
 * @date 2019年04月25 10:54
 */
public class DateUtil {


    /**
     * 获取当前时间
     *
     * @return 日期
     */
    public static Date date() {

        return new DateTime().toDate();
    }
    /**
     * Long类型时间转换日期
     * 毫秒级别
     *
     * @param date
     * @return 日期
     */
    public static Date date(long date) {

        return new DateTime(date).toDate();
    }


    /**
     * 按指定格式当前日期
     *
     * @param strFormat
     * @return
     */
    public static String format(String strFormat) {

        return format(date(), strFormat);
    }

    /**
     * 格式化日期,格式: yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String format(Date date) {

        DateTime dateTime = new DateTime(date);
        return dateTime.toString(DatePattern.PATTERN_NORM_DATETIME);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @param strFormat
     * @return
     */
    public static String format(Date date, String strFormat) {

        String strDate;
        if (strFormat == null) {
            strDate = format(date);
        } else {
            DateTime dateTime = new DateTime(date);
            strDate = dateTime.toString(strFormat);
        }
        return strDate;
    }

    /**
     * 计算二个日期间隔的天数，以指定日期开始时间为准
     * @param beginDate
     * @param endDate
     * @return
     */
    public static int betweenDays2(Date beginDate,Date endDate){

        DateTime begin = new DateTime(beginDate);
        DateTime end = new DateTime(endDate);
        return Days.daysBetween(begin.withTimeAtStartOfDay(),end.withTimeAtStartOfDay()).getDays();
    }



}
