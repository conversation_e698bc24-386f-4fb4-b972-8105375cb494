<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.IndustryMapper">

    <select id="listAllSecondWithParent" resultType="com.coocaa.ad.cheese.authority.common.db.entity.IndustryEntity">
        SELECT *
        FROM industry
        WHERE parent_id != 0
        AND status = 1
        ORDER BY rank, parent_id
    </select>

</mapper>
