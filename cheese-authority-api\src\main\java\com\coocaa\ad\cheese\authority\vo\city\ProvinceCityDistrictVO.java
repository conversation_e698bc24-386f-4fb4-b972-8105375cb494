package com.coocaa.ad.cheese.authority.vo.city;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
public class ProvinceCityDistrictVO {

    @Schema(description = "省份")
    private Integer province;
    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市")
    private Integer city;
    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区县")
    private Integer district;
    @Schema(description = "区县名称")
    private String districtName;
}
