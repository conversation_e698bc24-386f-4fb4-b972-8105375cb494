package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 微信小程序用户信息响应VO
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "微信小程序用户信息响应")
public class WechatUserInfoRespVO {

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "用户头像URL")
    private String avatarUrl;

    @Schema(description = "用户性别，0-未知，1-男，2-女")
    private String gender;

    @Schema(description = "用户所在国家")
    private String country;

    @Schema(description = "用户所在省份")
    private String province;

    @Schema(description = "用户所在城市")
    private String city;

    @Schema(description = "用户语言")
    private String language;

    @Schema(description = "用户OpenID")
    private String openId;

    @Schema(description = "用户UnionID")
    private String unionId;

    @Schema(description = "水印信息")
    private WatermarkInfo watermark;

    /**
     * 水印信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "水印信息")
    public static class WatermarkInfo {

        @Schema(description = "小程序AppID")
        private String appid;

        @Schema(description = "时间戳")
        private Long timestamp;
    }
}
