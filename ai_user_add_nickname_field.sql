-- AI用户表添加昵称字段
-- 执行时间：2025-07-17

-- 1. 添加昵称字段
ALTER TABLE `ai_user` 
ADD COLUMN `nick_name` varchar(100) DEFAULT NULL COMMENT '昵称' AFTER `name`;

-- 2. 查看表结构（验证字段是否添加成功）
DESC `ai_user`;

-- 3. 如果需要为现有数据设置默认昵称，可以执行以下语句（可选）
-- UPDATE `ai_user` SET `nick_name` = `name` WHERE `nick_name` IS NULL AND `name` IS NOT NULL;

-- 4. 如果需要创建索引（可选，根据查询需求决定）
-- CREATE INDEX `idx_ai_user_nick_name` ON `ai_user` (`nick_name`);

-- 表结构说明：
-- id: 主键ID，自增
-- open_id: 用户OpenID，微信用户唯一标识
-- name: 用户真实姓名或显示名称
-- nick_name: 用户昵称（新增字段）
-- avatar: 用户头像URL
-- mobile: 用户手机号
-- create_time: 创建时间
-- creator: 创建人ID
-- update_time: 更新时间
-- operator: 操作人ID
-- delete_flag: 删除标记，0-正常，1-已删除

-- 字段使用说明：
-- name: 通常存储用户的真实姓名或微信昵称
-- nick_name: 存储用户自定义的昵称，可以与name不同
-- 
-- 使用场景：
-- 1. 用户注册时，name和nick_name可以都设置为微信昵称
-- 2. 用户后续可以修改nick_name为自定义昵称，但name保持不变
-- 3. 显示时优先使用nick_name，如果为空则使用name

-- 回滚语句（如果需要删除字段）：
-- ALTER TABLE `ai_user` DROP COLUMN `nick_name`;
