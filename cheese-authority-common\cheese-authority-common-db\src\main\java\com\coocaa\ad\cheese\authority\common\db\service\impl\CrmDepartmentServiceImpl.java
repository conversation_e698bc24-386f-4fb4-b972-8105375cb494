package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.coocaa.ad.cheese.authority.common.db.entity.CrmDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.CrmDepartmentMapper;
import com.coocaa.ad.cheese.authority.common.db.service.ICrmDepartmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * crm部门信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Service
public class CrmDepartmentServiceImpl extends ServiceImpl<CrmDepartmentMapper, CrmDepartmentEntity> implements ICrmDepartmentService {

}
