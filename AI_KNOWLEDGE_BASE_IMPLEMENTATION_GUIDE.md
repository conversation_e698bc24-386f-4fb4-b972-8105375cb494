# AI知识库功能实现指南

## 概述

基于Spring AI框架实现的AI知识库功能，支持文档上传、向量化存储、语义搜索和RAG对话。当前版本已完成基础架构，支持文本文件处理，为后续集成更多功能做好准备。

## 🏗️ 架构概览

### 核心组件

1. **数据层**
   - `AiKnowledgeEntity` - 知识库实体
   - `AiKnowledgeDocumentEntity` - 文档实体
   - `AiKnowledgeSegmentEntity` - 文档分块实体

2. **服务层**
   - `AiKnowledgeService` - 知识库业务服务
   - `AiVectorService` - 向量存储和搜索服务
   - `DocumentContentExtractor` - 文档内容提取服务
   - `AiEmbeddingModelFactory` - 嵌入模型工厂

3. **配置层**
   - `AiVectorStoreConfig` - 向量存储配置
   - `SpringAiConfig` - Spring AI配置

### 依赖配置

```xml
<!-- Spring AI Redis Vector Store -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-starter-vector-store-redis</artifactId>
    <version>1.0.0-M6</version>
</dependency>

<!-- Spring AI Tika Document Reader -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-tika-document-reader</artifactId>
    <version>1.0.0-M6</version>
</dependency>

<!-- Apache Tika -->
<dependency>
    <groupId>org.apache.tika</groupId>
    <artifactId>tika-core</artifactId>
    <version>2.9.1</version>
</dependency>
```

## 📋 功能特性

### 1. 知识库管理
- ✅ 创建和配置知识库
- ✅ 设置嵌入模型和分块参数
- ✅ 知识库状态管理
- ✅ 统计信息展示

### 2. 文档处理
- ✅ 文本文件上传（TXT、MD、CSV、JSON、XML、HTML）
- ✅ 文本内容直接上传
- ⏳ PDF文档解析（待集成Tika）
- ⏳ Office文档解析（待集成Tika）
- ✅ 智能文档分块

### 3. 向量化存储
- ✅ Redis向量存储架构
- ✅ 嵌入模型工厂设计
- ⏳ 真实嵌入模型集成（OpenAI、HuggingFace）
- ✅ 向量ID管理和索引

### 4. 语义搜索
- ✅ 基于向量的相似度搜索
- ✅ 相似度阈值过滤
- ✅ TopK结果返回
- ✅ 搜索结果排序

## 🎯 API接口

### 知识库管理

#### 获取知识库列表
```bash
GET /sys/ai/knowledge/list
```

**响应示例：**
```json
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "技术文档库",
      "description": "API和技术文档",
      "embeddingModel": "text-embedding-ada-002",
      "vectorDimension": 1536,
      "chunkSize": 1000,
      "chunkOverlap": 200,
      "status": "active",
      "documentCount": 5,
      "segmentCount": 23,
      "createTime": "2025-07-17T10:00:00"
    }
  ]
}
```

#### 获取知识库详情
```bash
GET /sys/ai/knowledge/{knowledgeId}
```

### 文档上传

#### 上传文件
```bash
POST /sys/ai/knowledge/upload/file
Content-Type: multipart/form-data

file: [文件]
knowledgeId: 1
name: "API文档"
processImmediately: true
```

#### 上传文本内容
```bash
POST /sys/ai/knowledge/upload/text
Content-Type: application/json

{
  "knowledgeId": 1,
  "name": "FAQ文档",
  "content": "Q: 如何使用API？\nA: 首先需要获取API密钥...",
  "processImmediately": true
}
```

### 知识库搜索

#### 语义搜索
```bash
POST /sys/ai/knowledge/search
Content-Type: application/json

{
  "knowledgeId": 1,
  "query": "如何获取API密钥",
  "topK": 5,
  "threshold": 0.7
}
```

**响应示例：**
```json
{
  "code": 200,
  "data": {
    "results": [
      {
        "segmentId": 123,
        "documentId": 45,
        "documentName": "API文档",
        "content": "API密钥可以在控制台的设置页面获取...",
        "score": 0.85,
        "position": 2,
        "wordCount": 150
      }
    ],
    "searchTime": 120,
    "totalCount": 3
  }
}
```

## 🔧 配置和部署

### 1. 数据库配置

执行SQL脚本创建知识库相关表：
```sql
-- 执行 ai_knowledge_tables.sql
-- 创建 ai_knowledge, ai_knowledge_document, ai_knowledge_segment 表
```

### 2. Redis配置

```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    
ai:
  vector-store:
    type: redis
    redis:
      index-name: ai_knowledge_index
      prefix: "ai:vector:"
      initialize-schema: true
      dimension: 1536
      distance-metric: COSINE
```

### 3. 嵌入模型配置

```yaml
ai:
  vector-store:
    embedding:
      platform: openai  # openai, huggingface, local
      model: text-embedding-ada-002
      api-key: ${AI_EMBEDDING_API_KEY}
      api-url: ${AI_EMBEDDING_API_URL}
      dimension: 1536
```

## 💡 使用示例

### 1. 创建知识库

```sql
-- 插入知识库记录
INSERT INTO ai_knowledge (name, description, embedding_model, vector_dimension, chunk_size, chunk_overlap, status) 
VALUES ('技术文档库', 'API和技术文档', 'text-embedding-ada-002', 1536, 1000, 200, 'active');
```

### 2. 上传文档

```javascript
// 上传文本内容
const uploadResponse = await fetch('/sys/ai/knowledge/upload/text', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    knowledgeId: 1,
    name: 'API使用指南',
    content: `
# API使用指南

## 1. 获取API密钥
在控制台的设置页面可以获取API密钥。

## 2. 发送请求
使用HTTP POST方法发送请求到API端点。

## 3. 处理响应
API返回JSON格式的响应数据。
    `,
    processImmediately: true
  })
});

const documentId = await uploadResponse.json();
console.log('文档上传成功，ID:', documentId.data);
```

### 3. 搜索知识库

```javascript
// 搜索相关内容
const searchResponse = await fetch('/sys/ai/knowledge/search', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    knowledgeId: 1,
    query: '如何获取API密钥',
    topK: 5,
    threshold: 0.7
  })
});

const searchResults = await searchResponse.json();
console.log('搜索结果:', searchResults.data.results);
```

### 4. 集成到对话系统

```java
// 在对话服务中集成知识库搜索
@Service
public class RagChatService {
    
    @Autowired
    private AiKnowledgeService knowledgeService;
    
    public String enhancePromptWithKnowledge(String userQuery, Long knowledgeId) {
        // 1. 搜索相关知识
        AiKnowledgeSearchReqVO searchReq = new AiKnowledgeSearchReqVO()
            .setKnowledgeId(knowledgeId)
            .setQuery(userQuery)
            .setTopK(3)
            .setThreshold(0.7);
        
        AiKnowledgeSearchRespVO searchResult = knowledgeService.searchKnowledge(searchReq);
        
        // 2. 构建增强提示词
        StringBuilder enhancedPrompt = new StringBuilder();
        enhancedPrompt.append("基于以下知识回答用户问题：\n\n");
        
        for (var result : searchResult.getResults()) {
            enhancedPrompt.append("知识片段：").append(result.getContent()).append("\n\n");
        }
        
        enhancedPrompt.append("用户问题：").append(userQuery);
        return enhancedPrompt.toString();
    }
}
```

## 🔮 下一步计划

### A. 立即可实现（1-2天）
1. **集成真实嵌入模型**
   - OpenAI Embeddings API
   - 配置API密钥和请求处理
   - 错误处理和重试机制

2. **完善文档解析**
   - 集成Apache Tika
   - 支持PDF、Word、Excel等格式
   - 文档内容清理和优化

### B. 短期目标（1周内）
1. **RAG对话集成**
   - 修改现有对话服务
   - 支持知识库增强对话
   - 引用来源追踪

2. **Spring AI完整集成**
   - 使用Spring AI VectorStore
   - 集成Spring AI Document
   - 支持Spring AI的观察和监控

### C. 中期目标（1个月内）
1. **高级搜索功能**
   - 混合搜索（关键词+语义）
   - 多模态搜索支持
   - 搜索结果重排序

2. **性能优化**
   - 批量向量化处理
   - 缓存策略优化
   - 异步处理支持

## 🚨 注意事项

### 1. 当前限制
- 仅支持文本文件格式（TXT、MD、CSV等）
- 使用伪向量进行演示（需要集成真实嵌入模型）
- Spring AI依赖可能需要手动下载

### 2. 生产环境建议
- 配置真实的嵌入模型API
- 设置合适的Redis内存限制
- 配置文件上传大小限制
- 添加用户权限控制

### 3. 性能考虑
- 大文档建议分批处理
- 设置合适的分块大小和重叠
- 监控Redis内存使用情况
- 考虑使用专业向量数据库（如Milvus）

## 总结

AI知识库功能的基础架构已经完成，包括：

🎯 **完整的数据模型**：支持知识库、文档、分块的完整生命周期  
🎯 **灵活的API接口**：支持多种文档上传和搜索方式  
🎯 **可扩展的服务架构**：基于Spring AI的设计模式  
🎯 **Redis向量存储**：高性能的向量存储和搜索  
🎯 **嵌入模型工厂**：支持多种嵌入模型平台  

现在可以开始集成真实的嵌入模型API，并将知识库功能集成到现有的对话系统中！
