<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.channel.ChannelMapper">

    <select id="getUserChannel"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.channel.ChannelEntity">
        SELECT
        c.id,
        c.channel_name,
        c.channel_desc,
        c.agency_id,
        c.source
        FROM
        sys_channel c
        INNER JOIN sys_user_channel u ON c.id = u.channel_id
        WHERE
        u.user_id = #{userId}
        <if test="ids != null and ids.size > 0">
            and
            c.id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY
        c.id ASC
    </select>
</mapper>
