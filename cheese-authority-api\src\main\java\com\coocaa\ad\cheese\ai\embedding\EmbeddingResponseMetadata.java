package com.coocaa.ad.cheese.ai.embedding;

import lombok.Builder;
import lombok.Data;
import org.springframework.ai.model.ResponseMetadata;

/**
 * 嵌入响应元数据
 * 
 * 参考yudao-module-ai-server的元数据设计
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Builder
public class EmbeddingResponseMetadata implements ResponseMetadata {

    /**
     * 使用的模型名称
     */
    private String model;

    /**
     * 使用量信息
     */
    private Usage usage;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 使用量信息
     */
    @Data
    @Builder
    public static class Usage {
        
        /**
         * 提示词token数量
         */
        private Integer promptTokens;

        /**
         * 总token数量
         */
        private Integer totalTokens;

        /**
         * 获取提示词token数量
         */
        public Integer getPromptTokens() {
            return promptTokens != null ? promptTokens : 0;
        }

        /**
         * 获取总token数量
         */
        public Integer getTotalTokens() {
            return totalTokens != null ? totalTokens : 0;
        }
    }

    /**
     * 获取模型名称
     */
    @Override
    public String getModel() {
        return this.model;
    }

    /**
     * 获取使用量信息
     */
    public Usage getUsage() {
        return this.usage;
    }

    /**
     * 获取请求ID
     */
    public String getRequestId() {
        return this.requestId;
    }

    /**
     * 创建空的元数据
     */
    public static EmbeddingResponseMetadata empty() {
        return EmbeddingResponseMetadata.builder().build();
    }

    /**
     * 创建带模型名称的元数据
     */
    public static EmbeddingResponseMetadata of(String model) {
        return EmbeddingResponseMetadata.builder()
                .model(model)
                .build();
    }

    /**
     * 创建完整的元数据
     */
    public static EmbeddingResponseMetadata of(String model, Usage usage) {
        return EmbeddingResponseMetadata.builder()
                .model(model)
                .usage(usage)
                .build();
    }

    /**
     * 创建完整的元数据（包含请求ID）
     */
    public static EmbeddingResponseMetadata of(String model, Usage usage, String requestId) {
        return EmbeddingResponseMetadata.builder()
                .model(model)
                .usage(usage)
                .requestId(requestId)
                .build();
    }
}
