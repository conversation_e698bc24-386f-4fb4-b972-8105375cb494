package com.coocaa.ad.cheese.ai.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.ai.common.db.bean.AiUserDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiUserEntity;
import org.apache.ibatis.annotations.Param;

/**
 * AI Chat 对话接口
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
public interface AiUserMapper extends BaseMapper<AiUserEntity> {

    /**
     * 按条件查询AI Chat 对话列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return AI Chat 对话列表
     */
    IPage<AiUserEntity> pageList(@Param("page") IPage<AiUserEntity> page, @Param("condition") AiUserDTO condition);
}