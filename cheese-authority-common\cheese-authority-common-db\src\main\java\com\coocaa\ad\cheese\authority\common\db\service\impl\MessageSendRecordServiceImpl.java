package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.message.MessageSendRecordEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.message.MessageSendRecordMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IMessageSendRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-04-03
 */
@Service
public class MessageSendRecordServiceImpl extends ServiceImpl<MessageSendRecordMapper, MessageSendRecordEntity> implements IMessageSendRecordService {
}
