package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * AI知识库搜索请求VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiKnowledgeSearchReqVO", description = "AI知识库搜索请求VO")
public class AiKnowledgeSearchReqVO {

    @Schema(description = "知识库ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    @Schema(description = "搜索内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "搜索内容不能为空")
    private String query;

    @Schema(description = "返回结果数量", example = "5")
    private Integer topK = 5;

    @Schema(description = "相似度阈值", example = "0.7")
    private Double threshold = 0.7;
}
