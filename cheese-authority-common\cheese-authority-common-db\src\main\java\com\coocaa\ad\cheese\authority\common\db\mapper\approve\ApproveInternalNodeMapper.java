package com.coocaa.ad.cheese.authority.common.db.mapper.approve;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalNodeEntity;
import com.coocaa.ad.cheese.authority.common.db.vo.approve.InstanceNodeCommonVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Mapper
public interface ApproveInternalNodeMapper extends BaseMapper<ApproveInternalNodeEntity> {

    List<InstanceNodeCommonVO> getInstanceNode(@Param("instanceCode") String instanceCode);

    List<InstanceNodeCommonVO> getInstanceNodeBatch(@Param("instanceCodes") List<String> instanceCodes);
}
