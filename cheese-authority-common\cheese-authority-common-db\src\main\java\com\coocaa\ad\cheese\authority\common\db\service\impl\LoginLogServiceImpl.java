package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.LoginLogEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.LoginLogMapper;
import com.coocaa.ad.cheese.authority.common.db.service.ILoginLogService;
import org.springframework.stereotype.Service;

/**
 * 登陆日志 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-19
 */
@Service
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLogEntity> implements ILoginLogService {

}
