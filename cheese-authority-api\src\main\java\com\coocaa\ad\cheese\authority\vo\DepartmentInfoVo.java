package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.common.db.entity.DepartmentEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DepartmentInfoVo {

    @Schema(description = "是否还有更多选项", type = "boolean", example = "true")
    private boolean hasMore;

    @Schema(description = "分页标记，当 has_more 为 true 时，会同时返回新的 page_token，否则不返回 page_token", type = "string", example = "")
    private String pageToken;


    @Schema(description = "部门列表", type = "list", example = "")
    private List<DepartmentEntity> items;

}
