package com.coocaa.ad.cheese.authority.common.db.entity.approve;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@TableName("approve_internal_rule")
public class ApproveInternalRuleEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 规则名称 */
    private String name;

    /** 规则描述 */
    private String ruleDesc;

    /** 规则编号 */
    private Long code;

    /** 审批任务名称 */
    private String approvalName;

    /** 审批任务url */
    private String approvalUrl;

    /** 审批通过提示 */
    private String passPrompt;

    /** 审批通过url */
    private String passUrl;

    /** 审批拒绝提示 */
    private String rejectPrompt;

    /** 审批拒绝url */
    private String rejectUrl;

    /** 审批驳回提示 */
    private String rollbackPrompt;

    /** 审批驳回url */
    private String rollbackUrl;

    /** 抄送提示 */
    private String copyPrompt;

    /** 抄送url */
    private String copyUrl;

    /** 应用类型编码，字典0124 */
    private String appCode;

    /** 模块类型编码，字典0125 */
    private String moduleCode;

    /** 是否启用。1：启用，0：未启用 */
    private Integer flag;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;
}
