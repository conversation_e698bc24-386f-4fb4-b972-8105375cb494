-- AI对话和聊天表结构更新
-- 将openId改为userId关联user表
-- 执行时间：2025-07-17

-- 1. 更新ai_conversation表结构
-- 注意：如果表中已有数据，需要先备份数据

-- 1.1 备份现有数据（可选）
-- CREATE TABLE `ai_conversation_backup` AS SELECT * FROM `ai_conversation`;

-- 1.2 删除现有表并重新创建（如果表中没有重要数据）
DROP TABLE IF EXISTS `ai_conversation`;

CREATE TABLE `ai_conversation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号，关联ai_user表的id字段',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色编号，关联ai_role表的id字段',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(50) DEFAULT 'system' COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`),
  CONSTRAINT `fk_conversation_user` FOREIGN KEY (`user_id`) REFERENCES `ai_user` (`id`),
  CONSTRAINT `fk_conversation_role` FOREIGN KEY (`role_id`) REFERENCES `ai_role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI对话表';

-- 2. 更新ai_chat表结构
-- 2.1 备份现有数据（可选）
-- CREATE TABLE `ai_chat_backup` AS SELECT * FROM `ai_chat`;

-- 2.2 删除现有表并重新创建（如果表中没有重要数据）
DROP TABLE IF EXISTS `ai_chat`;

CREATE TABLE `ai_chat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号，作为每条聊天记录的唯一标识符',
  `conversation_id` bigint(20) NOT NULL COMMENT '对话编号，关联ai_conversation表',
  `reply_id` bigint(20) DEFAULT NULL COMMENT '回复编号，关联上一条消息',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号，关联ai_user表的id字段',
  `message_type` varchar(20) NOT NULL COMMENT '消息类型：user-用户消息，assistant-AI助手消息，system-系统消息',
  `content` text COMMENT '聊天内容',
  `user_context` tinyint(1) DEFAULT 0 COMMENT '是否携带上下文（0-否，1-是）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '操作人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_reply_id` (`reply_id`),
  KEY `idx_message_type` (`message_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`),
  CONSTRAINT `fk_chat_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `ai_conversation` (`id`),
  CONSTRAINT `fk_chat_user` FOREIGN KEY (`user_id`) REFERENCES `ai_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天消息表';

-- 表结构说明：

-- ai_conversation表：
-- id: 主键ID，自增
-- user_id: 用户编号，关联ai_user表的id字段（外键约束）
-- role_id: 角色编号，关联ai_role表的id字段（外键约束）
-- create_time: 创建时间，自动填充
-- create_by: 创建人，默认为'system'
-- update_time: 更新时间，自动更新
-- update_by: 更新人，默认为'system'
-- delete_flag: 软删除标记，0-正常，1-已删除

-- ai_chat表：
-- id: 主键ID，自增
-- conversation_id: 对话编号，关联ai_conversation表（外键约束）
-- reply_id: 回复编号，关联上一条消息
-- user_id: 用户编号，关联ai_user表的id字段（外键约束）
-- message_type: 消息类型，user/assistant/system
-- content: 聊天内容
-- user_context: 是否携带上下文
-- create_time: 创建时间，自动填充
-- creator: 创建人ID，默认为0
-- update_time: 更新时间，自动更新
-- operator: 操作人ID，默认为0
-- delete_flag: 软删除标记，0-正常，1-已删除

-- 索引说明：
-- 为常用查询字段创建索引，提高查询性能
-- 外键约束确保数据一致性

-- 使用场景：
-- 1. 创建对话时，指定用户ID和角色ID
-- 2. 发送消息时，记录用户ID和对话ID
-- 3. 查询对话历史时，通过用户ID和对话ID查询
-- 4. 实现多角色多轮对话功能

-- 查询示例：
-- 1. 查询用户的所有对话：
-- SELECT c.*, r.role_name FROM ai_conversation c 
-- LEFT JOIN ai_role r ON c.role_id = r.id 
-- WHERE c.user_id = ? AND c.delete_flag = 0 
-- ORDER BY c.create_time DESC;

-- 2. 查询对话的所有消息：
-- SELECT * FROM ai_chat 
-- WHERE conversation_id = ? AND delete_flag = 0 
-- ORDER BY create_time ASC;

-- 3. 查询用户在某个对话中的消息：
-- SELECT * FROM ai_chat 
-- WHERE conversation_id = ? AND user_id = ? AND delete_flag = 0 
-- ORDER BY create_time ASC;
