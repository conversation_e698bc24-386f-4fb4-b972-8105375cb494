package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户部门信息信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-03-07
 */
@Data
@Accessors(chain = true)
public class UserDepartmentVO{
    /**
     * 部门名称
     */
    @Schema(description = "部门名称", type = "String", example = "")
    private String departmentName;

    /**
     * 部门ID
     */
    @Schema(description = "部门openID", type = "String", example = "")
    private String openDepartmentId;
}
