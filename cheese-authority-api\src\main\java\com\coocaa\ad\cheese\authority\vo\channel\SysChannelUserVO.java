package com.coocaa.ad.cheese.authority.vo.channel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
public class SysChannelUserVO {

    private Integer id;

    /**
     * 渠道Id
     */
    @Schema(description = "渠道Id", type = "Int", example = "1")
    private String channelId;

    /**
     * 电话号码
     */
    @Schema(description = "电话号码", type = "String", example = "")
    private String mobile;

    /**
     * 机构ID
     */
    @Schema(description = "机构ID", type = "Int", example = "1")
    private Integer agencyId;

    /**
     * 城市ID集合
     */
    @Schema(description = "城市ID集合", type = "List", example = "")
    private List<Integer> cityList;

    /**
     * 城市名称集合
     */
    @Schema(description = "城市名称", type = "String", example = "")
    private String cityNameList;
    /**
     * 角色ID集合
     */
    @Schema(description = "角色ID集合", type = "List", example = "")
    private List<Integer> roleList;

    /**
     * 城市名称集合
     */
    @Schema(description = "角色名称", type = "String", example = "")
    private String roleNameList;
}
