package com.coocaa.ad.cheese.authority.common.db.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.coocaa.ad.cheese.authority.common.db.entity.IndustryEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.IndustryMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IIndustryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.tools.utils.MathUtils;
import com.coocaa.ad.cheese.authority.common.tools.utils.UserIdUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 行业 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class IndustryServiceImpl extends ServiceImpl<IndustryMapper, IndustryEntity> implements IIndustryService {
    /**
     * 一级行业查找
     * */
    @Override
    public IndustryEntity queryFirstLevelByName(String name) {
        return lambdaQuery().eq(IndustryEntity::getName, name)
                .eq(IndustryEntity::getParentId,0).one();
    }

    @Override
    public int firstLevelIndustryCount() {
        Long count = lambdaQuery().count();

        return count == null ? 0 : count.intValue();
    }

    @Override
    public void updateIndustryByParentId(Boolean enable, Integer parentId) {
        IndustryEntity entity = new IndustryEntity();
        entity.setStatus(enable);
        entity.setOperator(UserIdUtils.getUserId());
        LambdaQueryWrapper<IndustryEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(IndustryEntity::getParentId, parentId);
        update(entity, wrapper);
    }

    @Override
    public List<IndustryEntity> listIndustry(String name) {
        return lambdaQuery().like(StringUtils.isNotBlank(name),
                        IndustryEntity::getName, name)
                .eq(IndustryEntity::getParentId, 0)
                .orderByAsc(IndustryEntity::getRank).list();
    }

    @Override
    public int getMaxSecondByParentId(Integer parentId) {
        Long count = lambdaQuery().eq(IndustryEntity::getParentId, parentId)
                .count();
        return MathUtils.unBox(count);
    }

    @Override
    public List<IndustryEntity> listSubIndustry(Integer parentId, String name) {
        return lambdaQuery().eq(IndustryEntity::getParentId, parentId)
                .like(StringUtils.isNotBlank(name), IndustryEntity::getName, name)
                .orderByAsc(IndustryEntity::getRank).list();
    }

    @Override
    public List<IndustryEntity> listByParentId(Integer id) {
        return lambdaQuery().eq(IndustryEntity::getParentId, id).list();
    }

    @Override
    public List<IndustryEntity> listByCodes(List<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return new ArrayList<>();
        }
        return lambdaQuery()
                .in(IndustryEntity::getCode, codes)
                .orderByAsc(IndustryEntity::getRank)
                .list();
    }

    @Override
    public List<IndustryEntity> listSecond() {
        return lambdaQuery().ne(IndustryEntity::getParentId,0).
                eq(IndustryEntity::getStatus,true).list();
    }

    @Override
    public IndustryEntity querySecondLevelByName(String name) {
        return lambdaQuery().eq(IndustryEntity::getName, name)
                .ne(IndustryEntity::getParentId,0).one();
    }

    @Override
    public List<IndustryEntity> listAllSecondWithParent() {
        return baseMapper.listAllSecondWithParent();
    }
}
