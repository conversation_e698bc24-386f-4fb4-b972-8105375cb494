<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.UserMapper">
    <select id="queryUser" parameterType="map" resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserEntity">

    </select>

    <!-- 用户登陆 -->
    <select id="login" resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserEntity">
        SELECT id, name, mobile, status, password, locked, change_password_time,type,wno,email
        FROM `user`
        <where>
            <if test="wno != null and wno != ''">OR wno = #{wno}</if>
            <if test="userName != null and userName != ''">OR user_name = #{userName}</if>
            <if test="mobile != null and mobile != ''">OR mobile = #{mobile}</if>
            <if test="email !=null and email != ''">OR email = #{email}</if>
        </where>
    </select>

    <!-- 根据条件查询用户列表 -->
    <select id="listByCondition" resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserEntity">
        SELECT u.id, u.name, u.wno, u.mobile, u.email, u.user_name, u.status, u.create_time
        FROM `user` u
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        GROUP BY u.id
    </select>

    <!-- 根据条件统计用户数量 -->
    <select id="listByCondition_COUNT" resultType="java.lang.Integer">
        SELECT count(distinct u.id)
        FROM `user` u
        <include refid="joinSql"/>
        <include refid="whereSql"/>
    </select>


    <!-- 用户列表查询的公共部分提取出来 -->
    <sql id="joinSql">
        <if test="roleIds != null and roleIds.size > 0">
            JOIN user_role ur ON u.id = ur.user_id and ur.role_id IN
            <foreach collection="roleIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="cityGroupIds != null and cityGroupIds.size > 0">
            JOIN user_city_group uc ON u.id = uc.user_id and uc.city_group_id IN
            <foreach collection="cityGroupIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="channelIds != null and channelIds.size > 0">
            JOIN sys_user_channel uc ON u.id = uc.user_id and uc.channel_id IN
            <foreach collection="channelIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
    </sql>

    <sql id="whereSql">
        <where>
            <choose>
                <when test="userId != null">AND u.id = #{userId}</when>
                <otherwise>AND u.id > 0</otherwise>
            </choose>

            <if test="plainKeyword != null and plainKeyword != ''">
                AND (u.wno LIKE concat('%',#{plainKeyword},'%')
                OR u.name LIKE concat('%',#{plainKeyword},'%')
                OR u.user_name like concat('%',#{plainKeyword},'%')
                OR u.email = #{fakeKeyword}
                OR u.mobile = #{fakeKeyword} )
            </if>
        </where>
    </sql>

    <select id="listUserRoleResources3Type" resultType="com.coocaa.ad.cheese.authority.common.db.bean.ResourceDO">
        SELECT
            res.uri,
            res.code
        FROM user u
                 INNER JOIN user_role ur ON u.id = ur.user_id
                 INNER JOIN role ro ON ur.role_id = ro.id
                 INNER JOIN role_resource rr ON ro.id = rr.role_id
                 INNER JOIN resource res ON rr.resource_id = res.id
        WHERE u.id = #{userId}
          AND u.status = 1
          AND u.locked = 0
          AND ro.status = 1
          AND res.status = 1
          AND res.delete_flag = 0
          AND res.type = 3
        <if test="platform != null and platform != ''">
            and res.platform=#{platform}
        </if>
    </select>
    <select id="listUserDepartment"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentEntity">
        SELECT
            u.id,
            u.name,
            u.user_name,
            u.mobile,
            u.status,
            dp.leader_user_id,
            dp.open_department_id,
            dp.leader_user_id,
            dp.`name` as department_name
        FROM
            `user` u
                LEFT JOIN user_department_relation ud ON u.id = ud.user_id
                LEFT JOIN department dp ON dp.open_department_id = ud.department_id
        <where>

            <if test="userId != null and userId != ''">
                and u.id=#{userId}
            </if>
            <if test="departmentName != null and departmentName != ''">
               and dp.name=#{departmentName}
            </if>
            <if test="departmentIdList != null and departmentIdList.size > 0">
                and  dp.id IN
                <foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            </if>
        </where>
    </select>
    <select id="getUserByFsOpenId" resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserEntity">
        SELECT
            u.id,
            u.user_name,
            u.`name`,
            u.mobile,
            u.email,
            u.wno
        FROM
            `user` u
                LEFT JOIN user_feishu uf ON uf.user_id = u.id
        WHERE
           u.status = 1 AND uf.open_id =#{openId}
    </select>
    <select id="listDepartmentByUserId"
    resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentEntity"
    parameterType="java.util.Set">
        SELECT
            u.id,
            de.name as departmentName,
            de.open_department_id as openDepartmentId
        FROM
                `user` u
                LEFT JOIN user_feishu uf ON u.id = uf.user_id
                LEFT JOIN user_department_relation ud ON u.id = ud.user_id
                LEFT JOIN department de ON de.open_department_id = ud.department_id
            <where>
                 de.status="false"
                <if test="userIds != null and userIds.size > 0">
                    and u.id IN
                    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </where>
    </select>
    <select id="listByAgencyId" resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserEntity">
        SELECT
            u.id,
            u.name,
            u.mobile,
            u.email
        FROM
            `user` u
            JOIN sys_user_channel suc ON u.id = suc.user_id
            JOIN sys_channel sc ON suc.channel_id = sc.id
        WHERE
        sc.agency_id = #{agencyId}
          AND u.status = 1
          AND u.locked = 0
        group by u.id
    </select>
</mapper>
