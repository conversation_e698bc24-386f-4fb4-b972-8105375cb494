# AI知识库Spring AI实现总结

## 概述

基于 `yudao-module-ai-server` 的实现参考，完善了AI知识库功能，采用Spring AI框架的设计模式，支持多种嵌入模型和向量存储方案。

## 🏗️ 架构设计

### 核心组件

1. **AiVectorStoreConfig** - 向量存储配置
2. **AiEmbeddingModelFactory** - 嵌入模型工厂
3. **AiDocumentProcessor** - 文档处理器
4. **AiVectorService** - 向量服务（增强版）
5. **DocumentContentExtractor** - 文档内容提取器

### 设计模式

参考Spring AI和yudao-module-ai-server的设计：
- **工厂模式**：嵌入模型创建和管理
- **策略模式**：支持多种向量存储类型
- **模板模式**：统一的文档处理流程
- **包装器模式**：嵌入模型统一接口

## 📋 核心功能实现

### 1. 嵌入模型工厂 (AiEmbeddingModelFactory)

```java
// 支持多种嵌入模型平台
public EmbeddingModelWrapper getOrCreateEmbeddingModel(String platform, String apiKey, String model) {
    // 缓存机制
    // 动态创建模型
    // 统一接口包装
}

// 支持的平台
- OpenAI Embeddings
- HuggingFace Models  
- Local Models
- Default (伪向量)
```

### 2. 向量存储配置 (AiVectorStoreConfig)

```yaml
ai:
  vector-store:
    type: redis
    redis:
      index-name: ai_knowledge_index
      prefix: "ai:vector:"
      dimension: 1536
      distance-metric: COSINE
    embedding:
      platform: openai
      model: text-embedding-ada-002
      api-key: ${AI_EMBEDDING_API_KEY}
```

### 3. 文档处理器 (AiDocumentProcessor)

```java
// Spring AI Document风格的处理流程
public DocumentProcessResult processDocument(AiKnowledgeDocumentEntity document, 
                                           List<AiKnowledgeSegmentEntity> segments) {
    // 1. 创建Spring AI Document对象
    // 2. 向量化处理
    // 3. 存储向量
    // 4. 更新状态
}
```

### 4. 文档内容提取 (DocumentContentExtractor)

```java
// 基于Apache Tika的内容提取
public String extractContent(MultipartFile file) {
    // 支持多种文件格式
    // 智能编码检测
    // 内容清理和标准化
}

// 支持的文件类型
- 文本文件：txt, md, csv, json, xml, html
- PDF文档：pdf
- Office文档：doc, docx, xls, xlsx, ppt, pptx
```

## 🎯 技术特性

### 1. 多模型支持

#### OpenAI Embeddings
```java
private EmbeddingModelWrapper createOpenAIEmbeddingModel(String apiKey, String model) {
    // TODO: 集成真实的OpenAI Embeddings API
    return new EmbeddingModelWrapper("openai", model, 1536) {
        @Override
        public float[] embed(String text) {
            // 调用OpenAI API
            return callOpenAIEmbeddingsAPI(text);
        }
    };
}
```

#### HuggingFace Models
```java
private EmbeddingModelWrapper createHuggingFaceEmbeddingModel(String apiKey, String model) {
    // TODO: 集成HuggingFace模型
    return new EmbeddingModelWrapper("huggingface", model, 768) {
        @Override
        public float[] embed(String text) {
            // 调用HuggingFace API
            return callHuggingFaceAPI(text);
        }
    };
}
```

#### 本地模型
```java
private EmbeddingModelWrapper createLocalEmbeddingModel(String model) {
    // TODO: 集成本地模型
    return new EmbeddingModelWrapper("local", model, 512) {
        @Override
        public float[] embed(String text) {
            // 本地模型推理
            return callLocalModel(text);
        }
    };
}
```

### 2. 向量存储策略

#### Redis向量存储
- 基于Redis Hash存储向量数据
- 使用Redis Set维护知识库索引
- 支持元数据字段配置
- 高性能内存存储

#### 扩展支持
- **Milvus**：大规模向量搜索
- **Qdrant**：高性能向量数据库
- **SimpleVectorStore**：本地文件存储

### 3. 文档处理流程

```mermaid
graph TD
    A[文档上传] --> B[内容提取]
    B --> C[智能分块]
    C --> D[创建AI Document]
    D --> E[向量化处理]
    E --> F[存储向量]
    F --> G[更新状态]
    G --> H[处理完成]
```

## 🚀 API接口

### 知识库管理
```bash
# 获取知识库列表
GET /sys/ai/knowledge/list

# 上传文档
POST /sys/ai/knowledge/upload/file
Content-Type: multipart/form-data

# 上传文本
POST /sys/ai/knowledge/upload/text
{
  "knowledgeId": 1,
  "name": "测试文档",
  "content": "文档内容...",
  "processImmediately": true
}

# 知识库搜索
POST /sys/ai/knowledge/search
{
  "knowledgeId": 1,
  "query": "搜索内容",
  "topK": 5,
  "threshold": 0.7
}
```

## 🔧 配置和部署

### 1. 环境变量配置

```bash
# 嵌入模型配置
export AI_EMBEDDING_API_KEY="your-openai-api-key"
export AI_EMBEDDING_API_URL="https://api.openai.com"

# Redis配置
export REDIS_HOST="localhost"
export REDIS_PORT="6379"
export REDIS_PASSWORD=""
export REDIS_DATABASE="0"
```

### 2. 应用配置

```yaml
ai:
  vector-store:
    type: redis
    embedding:
      platform: openai
      model: text-embedding-ada-002
      api-key: ${AI_EMBEDDING_API_KEY}
    redis:
      index-name: ai_knowledge_index
      prefix: "ai:vector:"
      initialize-schema: true
```

### 3. 依赖配置

```xml
<!-- Spring AI Redis Store -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-redis-store</artifactId>
    <version>${spring-ai.version}</version>
</dependency>

<!-- Apache Tika -->
<dependency>
    <groupId>org.springframework.ai</groupId>
    <artifactId>spring-ai-tika-document-reader</artifactId>
    <version>${spring-ai.version}</version>
</dependency>

<dependency>
    <groupId>org.apache.tika</groupId>
    <artifactId>tika-core</artifactId>
</dependency>
```

## 💡 使用示例

### 1. 创建知识库并上传文档

```java
// 1. 创建知识库
AiKnowledgeEntity knowledge = new AiKnowledgeEntity();
knowledge.setName("技术文档库");
knowledge.setEmbeddingModel("text-embedding-ada-002");
knowledge.setChunkSize(1000);
knowledge.setChunkOverlap(200);

// 2. 上传文档
AiKnowledgeDocumentUploadReqVO uploadReq = new AiKnowledgeDocumentUploadReqVO();
uploadReq.setKnowledgeId(1L);
uploadReq.setName("API文档");
uploadReq.setContent("这是API文档内容...");
uploadReq.setProcessImmediately(true);

Long documentId = knowledgeService.uploadTextContent(uploadReq);
```

### 2. 搜索知识库

```java
// 搜索相关内容
AiKnowledgeSearchReqVO searchReq = new AiKnowledgeSearchReqVO();
searchReq.setKnowledgeId(1L);
searchReq.setQuery("如何使用API");
searchReq.setTopK(5);
searchReq.setThreshold(0.7);

AiKnowledgeSearchRespVO results = knowledgeService.searchKnowledge(searchReq);
```

### 3. 集成到对话系统

```java
// RAG增强对话
public String enhancePromptWithKnowledge(String userQuery, Long knowledgeId) {
    // 1. 搜索相关知识
    AiKnowledgeSearchReqVO searchReq = new AiKnowledgeSearchReqVO()
        .setKnowledgeId(knowledgeId)
        .setQuery(userQuery)
        .setTopK(3);
    
    List<SearchResult> results = vectorService.searchSimilarSegments(searchReq);
    
    // 2. 构建增强提示词
    StringBuilder enhancedPrompt = new StringBuilder();
    enhancedPrompt.append("基于以下知识回答用户问题：\n\n");
    
    for (SearchResult result : results) {
        enhancedPrompt.append("知识片段：").append(result.getContent()).append("\n\n");
    }
    
    enhancedPrompt.append("用户问题：").append(userQuery);
    return enhancedPrompt.toString();
}
```

## 🔮 下一步计划

### A. 立即可实现（1-2天）
1. **集成真实嵌入模型**
   - OpenAI Embeddings API集成
   - HuggingFace模型集成
   - 本地模型服务集成

2. **完善Spring AI集成**
   - 使用Spring AI的Document类
   - 集成Spring AI VectorStore
   - 支持Spring AI的观察和监控

### B. 短期目标（1周内）
1. **RAG对话集成**
   - 修改现有对话服务
   - 支持知识库增强
   - 引用来源追踪

2. **性能优化**
   - 批量向量化处理
   - 缓存策略优化
   - 异步处理支持

### C. 中期目标（1个月内）
1. **多向量存储支持**
   - Milvus集成
   - Qdrant集成
   - 存储策略选择

2. **高级搜索功能**
   - 混合搜索（关键词+语义）
   - 多模态搜索
   - 搜索结果重排序

## 总结

基于yudao-module-ai-server的参考实现，我们完成了：

🎯 **Spring AI架构**：采用Spring AI框架的设计模式和最佳实践  
🎯 **多模型支持**：支持OpenAI、HuggingFace、本地模型等多种嵌入模型  
🎯 **灵活配置**：完整的配置体系，支持多种部署环境  
🎯 **文档处理**：基于Apache Tika的多格式文档内容提取  
🎯 **向量存储**：Redis向量存储，支持扩展到其他向量数据库  
🎯 **完整API**：提供完整的知识库管理和搜索API接口  

现在可以开始集成真实的嵌入模型API，并将知识库功能集成到现有的对话系统中！
