package com.coocaa.ad.cheese.authority.common.db.service.approve;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.bean.TaskSearchDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalTaskEntity;
import com.coocaa.ad.cheese.authority.common.db.vo.approve.TaskDealCountSelectVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IApproveInternalTaskService extends IService<ApproveInternalTaskEntity> {

    /**
     * 分页查询用户任务列表
     */
    IPage<ApproveInternalTaskEntity> listForTask(IPage<ApproveInternalTaskEntity> page, TaskSearchDTO dto);

    List<TaskDealCountSelectVO> getUserDealCount(Integer userId, List<Long> codes);
}
