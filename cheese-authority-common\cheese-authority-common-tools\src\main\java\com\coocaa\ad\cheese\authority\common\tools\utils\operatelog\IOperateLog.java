package com.coocaa.ad.cheese.authority.common.tools.utils.operatelog;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
public interface IOperateLog {
    /**
     * 系统名称
     * 数据字典中配置为 0006-1
     */
    default String getSystemCode() {
        return "0006-1";
    }

    /**
     * 功能名称
     */
    String getFunctionName();

    /**
     * 实体名称 (数据字典中配置)
     */
    String getEntityCode();

    /**
     * 实体ID
     */
    Integer getEntityId();

    /**
     * 操作内容
     */
    String getContent();

    /**
     * 操作人ID
     */
    Integer getOperator();

    /**
     * 操作时间
     */
    default Date getCreateTime() {
        return new Date();
    }
}
