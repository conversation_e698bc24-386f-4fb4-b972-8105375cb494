package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.coocaa.ad.cheese.authority.common.db.entity.ResourceEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.ResourceMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IResourceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class ResourceServiceImpl extends ServiceImpl<ResourceMapper, ResourceEntity> implements IResourceService {

}
