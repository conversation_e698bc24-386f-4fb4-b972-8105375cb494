package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.bean.ResourceDO;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IUserService;
import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import com.coocaa.ad.cheese.authority.common.tools.utils.AesUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class UserServiceImpl extends ServiceImpl<UserMapper, UserEntity> implements IUserService {

    @Override
    public UserEntity login(String loginName, String password) {
        // 明文: [登陆名、工号];  密文: [手机、邮箱]
        String encrypted = AesUtils.encryptHex(loginName);
        List<UserEntity> userEntityList= getBaseMapper().login(loginName, loginName, encrypted, encrypted);
        if(CollectionUtils.isEmpty(userEntityList)){
            throw new CommonException("用户不存在");
        }
        List<UserEntity> userEntities=userEntityList.stream().filter(userEntity -> userEntity.getStatus() && !userEntity.getLocked()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userEntities)){
            throw new CommonException(10002,"用户已被禁用或锁定");
        }
        if(userEntities.size()>1){
            throw new CommonException("用户多个账号，请联系管理员");
        }

        return userEntities.get(0);
    }

    /**
     * 根据条件查询用户列表
     *
     * @param page         分页信息
     * @param userId       用户ID
     * @param keyword      查询关键字
     * @param roleIds      角色IDs
     * @param cityGroupIds 城市组IDs
     * @return 用户列表
     */
    @Override
    public Page<UserEntity> listByCondition(Page<UserEntity> page, Integer userId, String keyword,
                                            List<Integer> roleIds, List<Integer> cityGroupIds, List<Integer> channelIds) {
        // 明文: [姓名、登陆名、工号];  密文: [手机、邮箱]
        String fakeKeyword = StringUtils.isBlank(keyword) ? null : AesUtils.encryptHex(keyword);
        roleIds = CollectionUtils.isEmpty(roleIds) ? null : roleIds.stream().filter(Objects::nonNull).distinct().toList();
        cityGroupIds = CollectionUtils.isEmpty(cityGroupIds) ? null : cityGroupIds.stream().filter(Objects::nonNull).distinct().toList();

        return getBaseMapper().listByCondition(page, userId, keyword, fakeKeyword, roleIds, cityGroupIds, channelIds);
    }

    @Override
    public List<ResourceDO> listUserRoleResources3Type(Integer userId,String platform) {
        return getBaseMapper().listUserRoleResources3Type(userId,platform);
    }

    @Override
    public List<UserDepartmentEntity> listUserDepartment(Integer userId, String departmentName,List<Integer> departmentIdList) {
        return getBaseMapper().listUserDepartment(userId,departmentName,departmentIdList);
    }

    @Override
    public UserEntity getUserByFsOpenId(String openId) {
        return getBaseMapper().getUserByFsOpenId(openId);
    }
    public List<UserDepartmentEntity> listDepartmentByUserId(Set<Integer> userIds) {
        return getBaseMapper().listDepartmentByUserId(userIds);
    }

    // ... existing code ...

    @Override
    public Page<UserEntity> listByAgencyId(Page<UserEntity> page, Integer agencyId) {
        return   getBaseMapper().listByAgencyId(page, agencyId);
    }

}
