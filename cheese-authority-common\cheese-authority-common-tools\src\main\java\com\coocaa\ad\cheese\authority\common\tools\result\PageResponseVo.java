package com.coocaa.ad.cheese.authority.common.tools.result;

import java.io.Serializable;
import java.util.List;

/**
 * 类PageResponseVo实现描述:
 *
 * <AUTHOR>
 * @date 2021年09月18 11:23 上午
 */
public class PageResponseVo<T> implements Serializable {
    /**
     * 当前页
     * <p>
     * 将Integer改为Long的原因：假如你的总页数远远大于整数的最大数，
     * 那么这样就很难处理了。但修改之后，并不会影响我们现有的业务
     * </p>
     */
    private Long currentPage;

    /**
     * 每页显示条数
     */
    private Long pageSize;

    /**
     * 总条数
     */
    private Long totalRows;

    /**
     * 总页数
     */
    private Long totalPages;

    /**
     * 响应数据
     */
    private List<T> rows;
    /**
     * 合计数据
     */
    private Object total;


    /**
     * 无参数构造方法
     */
    public PageResponseVo() {
    }

    /**
     * 构造方法
     *
     * @param currentPage 当前页
     * @param pageSize    分页大小
     * @param totalRows   总行数
     * @param totalPages  总页数
     * @param rows     内容
     */
    public PageResponseVo(Long currentPage, Long pageSize, Long totalRows, Long totalPages, List<T> rows, Object total) {
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalRows = totalRows;
        this.totalPages = totalPages;
        this.rows = rows;
        this.total = total;
    }

    /**
     * 获取当前页
     *
     * @return 返回当前页
     */
    public Long getCurrentPage() {
        return currentPage;
    }

    /**
     * 设置当前页
     *
     * @param currentPage 当前页
     * @return PageResponseVo
     */
    public PageResponseVo<T> setCurrentPage(Long currentPage) {
        this.currentPage = currentPage;
        return this;
    }

    /**
     * 获取分页大小
     *
     * @return 分页大小
     */
    public Long getPageSize() {
        return pageSize;
    }

    /**
     * 设置分页大小
     *
     * @param pageSize 分页大小
     * @return PageResponseVo
     */
    public PageResponseVo<T> setPageSize(Long pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    /**
     * 获取总条数
     *
     * @return 总条数
     */
    public Long getTotalRows() {
        return totalRows;
    }

    /**
     * 设置总条数
     *
     * @param totalRows 总条数
     * @return PageResponseVo
     */
    public PageResponseVo<T> setTotalRows(Long totalRows) {
        this.totalRows = totalRows;
        return this;
    }

    /**
     * 获取总页数
     *
     * @return 总页数
     */
    public Long getTotalPages() {
        return totalPages;
    }

    /**
     * 设置总页数
     *
     * @param totalPages 总页数
     * @return PageResponseVo
     */
    public PageResponseVo<T> setTotalPages(Long totalPages) {
        this.totalPages = totalPages;
        return this;
    }

    /**
     * 获取内容
     *
     * @return 内容
     */
    public List<T> getRows() {
        return rows;
    }

    /**
     * 设置内容
     *
     * @param rows 内容
     * @return PageResponseVo
     */
    public PageResponseVo<T> setRows(List<T> rows) {
        this.rows = rows;
        return this;
    }

    /**
     * 获取合计数据
     * @return 合计数据
     */
    public Object getTotal() {
        return total;
    }

    /**
     * 设置合计数据
     *
     * @param total 合计数据
     * @return PageResponseVo
     */
    public PageResponseVo<T> setTotal(Object total) {
        this.total = total;
        return this;
    }

    /**
     * PageResponse构造者，可通过该类构造出PageResponseVo
     *
     * @param <T> 范型
     */
    public static class Builder<T> implements IBuilder<PageResponseVo<T>> {

        // PageResponseVo 对象
        private final PageResponseVo<T> pageResponseVo;

        /**
         * 无参数构造方法，new一个 PageResponseVo 对象
         */
        public Builder() {
            pageResponseVo = new PageResponseVo<>();
        }

        /**
         * 设置当前页
         *
         * @param currentPage 当前页
         * @return Builder
         */
        public Builder<T> currentPage(long currentPage) {
            this.pageResponseVo.currentPage = currentPage;
            return this;
        }

        /**
         * 设置分页大小
         *
         * @param pageSize 分页大小
         * @return Builder
         */
        public Builder<T> pageSize(Long pageSize) {
            this.pageResponseVo.pageSize = pageSize;
            return this;
        }

        /**
         * 设置总条数
         *
         * @param totalRows 总条数
         * @return Builder
         */
        public Builder<T> totalRows(long totalRows) {
            this.pageResponseVo.totalRows = totalRows;
            return this;
        }

        /**
         * 设置总页数
         *
         * @param totalPages 总页数
         * @return Builder
         */
        public Builder<T> totalPages(long totalPages) {
            this.pageResponseVo.totalPages = totalPages;
            return this;
        }

        /**
         * 给属性赋值完成，调该方法可构造出PageResponseVo的对象
         *
         * @return PageResponseVo
         */
        @Override
        public PageResponseVo<T> build() {
            return this.pageResponseVo;
        }
    }

    /**
     * toString方法
     *
     * @return 将属性及对应值拼接成字符串
     */
    @Override
    public String toString() {
        return "PageResponseVo{" +
                "currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                ", totalRows=" + totalRows +
                ", totalPages=" + totalPages +
                ", rows=" + rows +
                ", total=" + total +
                '}';
    }
}
