spring.application.name=cheese-authority-api

server.port=8001

#\u5141\u8BB8\u5FAA\u73AF\u4F9D\u8D56 springboot2.4\u4E4B\u540E\u9ED8\u8BA4\u7981\u6B62\u5FAA\u73AF\u4F9D\u8D56
spring.main.allow-circular-references=true

#\u6587\u4EF6\u4E0A\u4F20\u5927\u5C0F\u914D\u7F6E
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

logging.level.root= info
logging.level.com.coocaa = info
#logback\u7684\u65E5\u5FD7\u6253\u5370\u683C\u5F0F
logging.pattern.console=%clr(${LOG_LEVEL_PATTERN:[%level]}) [%d{yyyy-MM-dd HH:mm:ss.SSS}] %clr([${PID:-}]){faint} %clr([%thread]){magenta} %clr([%-40.40logger{80}:%line]){cyan} %msg%n

#\u63A5\u53E3\u6587\u6863\u914D\u7F6E
springdoc.api-docs.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html

mybatis-plus.mapper-locations=classpath:/mapper/**.xml
mybatis-plus.configuration.map-underscore-to-camel-case=true


management.endpoints.web.exposure.include=prometheus,health,metrics
management.metrics.enable.all=true
management.metrics.tags.application=${spring.application.name}
management.server.port=7111
# Doubao AI Configuration
doubao.base-url=${DOUBAO_API_BASE_URL:https://ark.cn-beijing.volces.com/}
doubao.api-key=${DOUBAO_API_KEY:c257efd9-0476-4911-b6aa-f67ae5699a57}
doubao.model=${DOUBAO_API_MODEL:bot-20250710154233-s5mkt}
# Wechat MiniProgram Configuration
wechat.mini.app-id=${WECHAT_MINIPROGRAM_APP_ID:wx10d1ad7d45cae2d6}
wechat.mini.app-secret=${WECHAT_MINIPROGRAM_APP_SECRET:47fb506e0cea11df5d02b23756c89273}
wechat.mini.language=zh_CN
wechat.mini.format=mp3
wechat.mini.max-retries=3
wechat.mini.retry-interval=1000
wechat.mini.max-file-size=10485760


