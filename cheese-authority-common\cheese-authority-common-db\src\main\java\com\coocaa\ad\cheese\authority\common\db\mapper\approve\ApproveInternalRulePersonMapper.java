package com.coocaa.ad.cheese.authority.common.db.mapper.approve;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRulePersonEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Mapper
public interface ApproveInternalRulePersonMapper extends BaseMapper<ApproveInternalRulePersonEntity> {
    List<ApproveInternalRulePersonEntity> getPersonByRuleId(@Param("ruleId") Integer ruleId);

    void updateAfterRank(@Param("ruleId") Integer ruleId, @Param("rank") Integer rank);
}
