package com.coocaa.ad.cheese.ai.embedding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;
import org.springframework.ai.embedding.EmbeddingOptions;

/**
 * 豆包AI嵌入模型选项
 * 
 * 参考yudao-module-ai-server的选项设计
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Builder
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DoubaoEmbeddingOptions implements EmbeddingOptions {

    /**
     * 嵌入向量的维度
     * 如果不指定，将使用模型的默认维度
     */
    @JsonProperty("dimensions")
    private Integer dimensions;

    /**
     * 编码格式
     * 可选值：float, base64
     */
    @JsonProperty("encoding_format")
    private String encodingFormat;

    /**
     * 用户标识
     * 用于监控和滥用检测
     */
    @JsonProperty("user")
    private String user;

    /**
     * 模型名称
     */
    @JsonProperty("model")
    private String model;

    /**
     * 是否支持多模态（图像、视频）
     */
    @JsonProperty("multimodal")
    private Boolean multimodal;

    /**
     * 图像处理配置
     */
    @JsonProperty("image_config")
    private ImageConfig imageConfig;

    /**
     * 视频处理配置
     */
    @JsonProperty("video_config")
    private VideoConfig videoConfig;

    /**
     * 创建默认选项
     */
    public static DoubaoEmbeddingOptions create() {
        return DoubaoEmbeddingOptions.builder().build();
    }

    /**
     * 从现有选项创建新的构建器
     */
    public static DoubaoEmbeddingOptionsBuilder from(DoubaoEmbeddingOptions options) {
        return DoubaoEmbeddingOptions.builder()
                .dimensions(options.getDimensions())
                .encodingFormat(options.getEncodingFormat())
                .user(options.getUser())
                .model(options.getModel());
    }

    /**
     * 设置维度
     */
    public DoubaoEmbeddingOptions withDimensions(Integer dimensions) {
        return DoubaoEmbeddingOptions.builder()
                .from(this)
                .dimensions(dimensions)
                .build();
    }

    /**
     * 设置编码格式
     */
    public DoubaoEmbeddingOptions withEncodingFormat(String encodingFormat) {
        return DoubaoEmbeddingOptions.builder()
                .from(this)
                .encodingFormat(encodingFormat)
                .build();
    }

    /**
     * 设置用户标识
     */
    public DoubaoEmbeddingOptions withUser(String user) {
        return DoubaoEmbeddingOptions.builder()
                .from(this)
                .user(user)
                .build();
    }

    /**
     * 设置模型名称
     */
    public DoubaoEmbeddingOptions withModel(String model) {
        return DoubaoEmbeddingOptions.builder()
                .from(this)
                .model(model)
                .build();
    }

    /**
     * 图像配置
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageConfig {
        /**
         * 最大图像数量
         */
        @JsonProperty("max_images")
        private Integer maxImages;

        /**
         * 图像质量设置
         */
        @JsonProperty("quality")
        private String quality;

        /**
         * 是否支持Base64编码
         */
        @JsonProperty("support_base64")
        private Boolean supportBase64;
    }

    /**
     * 视频配置
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class VideoConfig {
        /**
         * 最大视频数量
         */
        @JsonProperty("max_videos")
        private Integer maxVideos;

        /**
         * 视频帧率设置
         */
        @JsonProperty("fps")
        private Integer fps;

        /**
         * 最大视频时长（秒）
         */
        @JsonProperty("max_duration")
        private Integer maxDuration;

        /**
         * 是否支持Base64编码
         */
        @JsonProperty("support_base64")
        private Boolean supportBase64;
    }

    /**
     * 构建器类的扩展方法
     */
    public static class DoubaoEmbeddingOptionsBuilder {

        /**
         * 从现有选项复制设置
         */
        public DoubaoEmbeddingOptionsBuilder from(DoubaoEmbeddingOptions options) {
            if (options != null) {
                this.dimensions = options.getDimensions();
                this.encodingFormat = options.getEncodingFormat();
                this.user = options.getUser();
                this.model = options.getModel();
                this.multimodal = options.getMultimodal();
                this.imageConfig = options.getImageConfig();
                this.videoConfig = options.getVideoConfig();
            }
            return this;
        }
    }
}
