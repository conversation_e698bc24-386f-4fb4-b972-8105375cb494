package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.bean.DownloadTaskDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.DownloadTaskEntity;

/**
 * 下载任务表服务接口
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
public interface IDownloadTaskService extends IService<DownloadTaskEntity> {

    /**
     * 分页查询
     *
     * @param downloadTask 查询条件
     * @param page         分页对象
     * @return 分页结果
     */
    IPage<DownloadTaskEntity> pageList(IPage<DownloadTaskEntity> page, DownloadTaskDTO downloadTask);
}