package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.bean.DownloadAttachmentDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.DownloadAttachmentEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.DownloadAttachmentMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IDownloadAttachmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * 下载附件 服务实现类
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class DownloadAttachmentServiceImpl extends ServiceImpl<DownloadAttachmentMapper, DownloadAttachmentEntity> implements IDownloadAttachmentService {

    @Override
    public IPage<DownloadAttachmentEntity> pageList(IPage<DownloadAttachmentEntity> page, DownloadAttachmentDTO downloadAttachment) {
        return baseMapper.pageList(page, downloadAttachment);
    }
}