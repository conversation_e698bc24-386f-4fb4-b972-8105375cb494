package com.coocaa.ad.cheese.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Redis向量数据库配置
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "redis.vector")
public class RedisVectorConfig {

    /**
     * 向量索引前缀
     */
    private String indexPrefix = "vector:index:";

    /**
     * 向量数据前缀
     */
    private String dataPrefix = "vector:data:";

    /**
     * 默认向量维度
     */
    private Integer defaultDimension = 1536;

    /**
     * 默认相似度算法（COSINE, IP, L2）
     */
    private String defaultMetric = "COSINE";

    /**
     * 搜索超时时间（毫秒）
     */
    private Long searchTimeout = 5000L;

    /**
     * 批量操作大小
     */
    private Integer batchSize = 100;
}
