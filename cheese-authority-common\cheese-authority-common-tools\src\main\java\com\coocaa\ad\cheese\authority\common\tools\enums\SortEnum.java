package com.coocaa.ad.cheese.authority.common.tools.enums;

import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/10/27
 */
@Getter
public enum SortEnum {
    UP("up"),

    DOWN("down");

    private final String sort;

    SortEnum(String sort) {
        this.sort = sort;
    }
    public SortEnum getSort(String sort){
        SortEnum[] values = SortEnum.values();
        for(SortEnum sortEnum : values){
            if(sortEnum.sort.equals(sort)){
                return sortEnum;
            }
        }
        throw new CommonException("没有找到排序规则");
    }
}
