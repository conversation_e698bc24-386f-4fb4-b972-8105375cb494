package com.coocaa.ad.cheese.authority.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 资源类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-22
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {
    SYSTEM(1, "系统"),
    MENU(2, "菜单"),
    INTERNAL(3, "内部"),
    DATA(4, "数据");

    private final Integer code;
    private final String name;

    public static ResourceTypeEnum get(Integer code) {
        for (ResourceTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
