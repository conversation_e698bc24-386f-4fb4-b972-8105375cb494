# 对话表结构重构总结

## 概述

将对话表和聊天表的 `openId` 字段改为 `userId` 字段，直接关联 `ai_user` 表的主键，并添加角色编号字段，实现更规范的数据库设计和多角色对话功能。

## 主要变更

### 1. 数据库表结构变更

#### AiConversationEntity (ai_conversation表)
**变更前：**
```java
// 没有明确的用户关联字段
```

**变更后：**
```java
/**
 * 用户编号
 * 
 * 关联 ai_user 表的 id 字段
 */
@TableField("user_id")
private Long userId;

/**
 * 角色编号
 *
 * 关联 ai_role 表的 id 字段
 */
@TableField("role_id")
private Long roleId;
```

#### AiChatEntity (ai_chat表)
**变更前：**
```java
/**
 * 用户openId
 */
private String openId;
```

**变更后：**
```java
/**
 * 用户编号
 * 
 * 关联 ai_user 表的 id 字段
 */
@TableField("user_id")
private Long userId;
```

### 2. 业务逻辑变更

#### AiConversationService.createConversation()
**变更前：**
```java
// 4. 创建对话初始记录
createConversationRecord(conversationEntity.getId(), userInfoVO.getOpenId(), title);
```

**变更后：**
```java
// 4. 创建对话初始记录
createConversationRecord(conversationEntity.getId(), userInfoVO.getId(), title);
```

#### AiConversationService.createConversationRecord()
**变更前：**
```java
private void createConversationRecord(Long conversationId, String openId, String title) {
    conversationStart.setOpenId(openId);
    // ...
}
```

**变更后：**
```java
private void createConversationRecord(Long conversationId, Long userId, String title) {
    conversationStart.setUserId(userId);
    // ...
}
```

#### AiConversationService.sendChatMessageStream()
**变更前：**
```java
userMessage.setOpenId(userInfo.getOpenId());
assistantMessage.setOpenId(userInfo.getOpenId());
```

**变更后：**
```java
userMessage.setUserId(userInfo.getId());
assistantMessage.setUserId(userInfo.getId());
```

### 3. 数据库表结构

#### ai_conversation表
```sql
CREATE TABLE `ai_conversation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号，关联ai_user表的id字段',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色编号，关联ai_role表的id字段',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(50) DEFAULT 'system' COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  CONSTRAINT `fk_conversation_user` FOREIGN KEY (`user_id`) REFERENCES `ai_user` (`id`),
  CONSTRAINT `fk_conversation_role` FOREIGN KEY (`role_id`) REFERENCES `ai_role` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI对话表';
```

#### ai_chat表
```sql
CREATE TABLE `ai_chat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号，作为每条聊天记录的唯一标识符',
  `conversation_id` bigint(20) NOT NULL COMMENT '对话编号，关联ai_conversation表',
  `reply_id` bigint(20) DEFAULT NULL COMMENT '回复编号，关联上一条消息',
  `user_id` bigint(20) NOT NULL COMMENT '用户编号，关联ai_user表的id字段',
  `message_type` varchar(20) NOT NULL COMMENT '消息类型：user-用户消息，assistant-AI助手消息，system-系统消息',
  `content` text COMMENT '聊天内容',
  `user_context` tinyint(1) DEFAULT 0 COMMENT '是否携带上下文（0-否，1-是）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '操作人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_chat_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `ai_conversation` (`id`),
  CONSTRAINT `fk_chat_user` FOREIGN KEY (`user_id`) REFERENCES `ai_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天消息表';
```

## 重构优势

### 1. 数据一致性
✅ **外键约束**：通过外键约束确保数据一致性  
✅ **规范化设计**：使用主键关联而非字符串字段  
✅ **级联操作**：支持级联删除和更新  

### 2. 查询性能
✅ **索引优化**：数字主键查询比字符串查询更快  
✅ **JOIN优化**：直接通过主键JOIN，性能更好  
✅ **存储优化**：数字字段占用空间更小  

### 3. 业务逻辑
✅ **角色支持**：新增角色字段支持多角色对话  
✅ **用户管理**：统一通过用户ID管理用户信息  
✅ **扩展性**：为后续功能扩展提供更好的基础  

### 4. 数据安全
✅ **引用完整性**：外键约束防止孤立数据  
✅ **数据校验**：数据库层面的数据校验  
✅ **事务支持**：更好的事务一致性保证  

## API接口影响

### 创建对话接口
```json
POST /sys/ai/conversation/create
{
  "openId": "wx_user_123",
  "title": "新的对话",
  "roleId": 1,  // 新增角色ID字段
  "name": "用户昵称",
  "avatar": "头像URL",
  "mobile": "手机号"
}
```

### 发送消息接口
```json
POST /sys/ai/conversation/sendChatMessageStream
{
  "conversationId": 1,
  "content": "你好",
  "roleId": 1,  // 新增角色ID字段
  "useContext": true
}
```

## 数据迁移

如果现有系统中有数据，需要进行数据迁移：

1. **备份现有数据**
2. **创建用户映射**：将 openId 映射到 userId
3. **数据转换**：将现有对话和聊天记录的 openId 转换为 userId
4. **验证数据**：确保迁移后数据完整性

## 使用示例

### 1. 创建带角色的对话
```java
AiConversationCreateReqVO reqVO = new AiConversationCreateReqVO();
reqVO.setOpenId("wx_user_123");
reqVO.setTitle("编程助手对话");
reqVO.setRoleId(2L); // 编程助手角色
AiConversationCreateRespVO respVO = conversationService.createConversation(reqVO);
```

### 2. 查询用户对话
```sql
-- 查询用户的所有对话及其角色
SELECT c.id, c.user_id, c.role_id, r.role_name, c.create_time
FROM ai_conversation c 
LEFT JOIN ai_role r ON c.role_id = r.id 
WHERE c.user_id = ? AND c.delete_flag = 0 
ORDER BY c.create_time DESC;
```

### 3. 查询对话消息
```sql
-- 查询对话的所有消息
SELECT * FROM ai_chat 
WHERE conversation_id = ? AND delete_flag = 0 
ORDER BY create_time ASC;
```

## 总结

通过这次重构，我们实现了：

🎯 **规范化的数据库设计**：使用主键关联替代字符串字段  
🎯 **多角色对话支持**：每个对话可以指定不同的AI角色  
🎯 **更好的性能**：数字主键查询和JOIN性能更优  
🎯 **数据一致性**：外键约束确保数据完整性  
🎯 **扩展性**：为后续功能扩展提供良好基础  

这为实现更复杂的AI对话功能（如知识库集成、工具调用等）奠定了坚实的基础！
