package com.coocaa.ad.cheese.authority.common.tools.utils;

import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import com.coocaa.ad.cheese.authority.common.tools.translate.DefaultTranslate;
import com.coocaa.ad.cheese.authority.common.tools.translate.Translate;
import com.coocaa.ad.cheese.authority.common.tools.utils.annotaion.CompareField;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2024/10/27
 */
@Slf4j
public class CompareUtils {

    private CompareUtils() {

    }

    /**
     * (字段名)改为(xxxx)
     */
    public static <T> String compare(T source, T target) {
        Class<?> clazz = source.getClass();
        StringBuilder stringBuilder = new StringBuilder();

        Field[] declaredFields = clazz.getDeclaredFields();

        for (Field field : declaredFields) {
            CompareField compareField = field.getAnnotation(CompareField.class);

            if (compareField != null) {
                field.setAccessible(true);
                String desc = compareField.description();
                try {
                    String sd = field.get(source).toString();
                    String td = field.get(target).toString();
                    if (!sd.equals(td)) {
                        Class<? extends Translate> t = compareField.translateType();
                        if (t != DefaultTranslate.class) {
                            Translate translate = t.getDeclaredConstructor().newInstance();
                            td = translate.translate(td);
                        }
                        stringBuilder.append("(").append(desc).append(")改为(")
                                .append(td).append("),");
                    }

                } catch (Exception e) {
                    log.error("字段对比功能出错:", e);
                    throw new CommonException("系统错误");
                }

            }
        }

        if (!stringBuilder.isEmpty()) {
            return stringBuilder.substring(0, stringBuilder.length() - 1);
        }
        return "";
    }
}
