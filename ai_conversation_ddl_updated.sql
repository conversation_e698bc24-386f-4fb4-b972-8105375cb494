-- AI对话表DDL
-- 用于存储AI对话会话信息，与ai_user表关联

CREATE TABLE `ai_conversation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID（关联ai_user表的id）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(50) NOT NULL DEFAULT 'system' COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI对话表';

-- 添加外键约束（可选，根据实际需要决定是否添加）
-- ALTER TABLE `ai_conversation` ADD CONSTRAINT `fk_ai_conversation_user_id` 
-- FOREIGN KEY (`user_id`) REFERENCES `ai_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 创建索引优化查询性能
CREATE INDEX `idx_ai_conversation_user_create` ON `ai_conversation` (`user_id`, `create_time` DESC);
CREATE INDEX `idx_ai_conversation_delete_create` ON `ai_conversation` (`delete_flag`, `create_time` DESC);

-- 插入示例数据（可选）
-- INSERT INTO `ai_conversation` (`user_id`, `create_by`, `update_by`) VALUES 
-- (1, 'system', 'system'),
-- (2, 'system', 'system'),
-- (3, 'system', 'system');

-- 表结构说明：
-- 1. id: 对话的唯一标识符，自增主键
-- 2. user_id: 关联ai_user表的用户ID，建立用户与对话的关系
-- 3. create_time: 对话创建时间，用于排序和查询
-- 4. create_by: 创建人，通常为'system'或具体的用户标识
-- 5. update_time: 最后更新时间，自动更新
-- 6. update_by: 最后更新人
-- 7. delete_flag: 软删除标记，0表示正常，1表示已删除

-- 使用场景：
-- 1. 创建新对话时，插入一条记录关联用户
-- 2. 查询用户的所有对话
-- 3. 根据对话ID查找对应的用户
-- 4. 软删除对话记录

-- 查询示例：
-- 1. 查询用户的所有对话：
-- SELECT * FROM ai_conversation WHERE user_id = ? AND delete_flag = 0 ORDER BY create_time DESC;

-- 2. 查询对话详情：
-- SELECT c.*, u.open_id, u.name FROM ai_conversation c 
-- LEFT JOIN ai_user u ON c.user_id = u.id 
-- WHERE c.id = ? AND c.delete_flag = 0;

-- 3. 验证对话所有权：
-- SELECT COUNT(*) FROM ai_conversation WHERE id = ? AND user_id = ? AND delete_flag = 0;
