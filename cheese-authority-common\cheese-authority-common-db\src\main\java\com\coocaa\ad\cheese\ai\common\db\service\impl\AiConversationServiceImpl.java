package com.coocaa.ad.cheese.ai.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.ai.common.db.entity.AiConversationEntity;
import com.coocaa.ad.cheese.ai.common.db.mapper.AiConversationMapper;
import com.coocaa.ad.cheese.ai.common.db.service.IAiConversationService;
import org.springframework.stereotype.Service;

/**
 * AI对话服务实现
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
public class AiConversationServiceImpl extends ServiceImpl<AiConversationMapper, AiConversationEntity> 
        implements IAiConversationService {
}
