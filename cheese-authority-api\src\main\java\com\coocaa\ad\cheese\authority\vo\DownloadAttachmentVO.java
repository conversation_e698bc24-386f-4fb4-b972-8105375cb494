
package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.DownloadConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 下载附件VO
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Data
@Accessors(chain = true)
@Schema(name = "DownloadAttachmentVO", description = "下载附件VO")
public class DownloadAttachmentVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "下载任务ID")
    private Long taskId;

    @Schema(description = "附件名称", maxLength = 100)
    private String name;

    @Schema(description = "附件全路径", maxLength = 200)
    private String url;

    @Schema(description = "大小(字节)")
    private Long size;

    @Schema(description = "文件类型 (pdf, doc,...)", maxLength = 10)
    private String fileType;

    @Schema(description = "删除标记  [0:否, 1:是]")
    private Integer deleteFlag;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = DownloadConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;
}