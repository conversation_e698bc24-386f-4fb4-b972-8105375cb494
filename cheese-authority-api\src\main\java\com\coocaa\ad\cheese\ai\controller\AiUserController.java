
package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.bean.AiUserParam;
import com.coocaa.ad.cheese.ai.service.AiUserService;
import com.coocaa.ad.cheese.ai.vo.AiUserVO;
import com.coocaa.ad.cheese.authority.common.tools.result.PageRequestVo;
import com.coocaa.ad.cheese.authority.common.tools.result.PageResponseVo;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI Chat 用户 控制器
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@RestController
@RequestMapping("/sys/ai-users")
@Tag(name = "AI Chat 用户", description = "AI Chat 用户管理")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiUserController {
    private final AiUserService aiUserService;

    /**
     * AI Chat 用户列表(分页)
     */
    @Operation(summary = "AI Chat 用户列表(分页)")
    @PostMapping("/page")
    public ResultTemplate<PageResponseVo<AiUserVO>> pageList(@RequestBody PageRequestVo<AiUserParam> pageRequest) {
        return ResultTemplate.success(aiUserService.pageList(pageRequest));
    }

    /**
     * AI Chat 用户详情
     */
    @Operation(summary = "AI Chat 用户详情")
    @Parameter(name = "id", description = "AI Chat 用户ID", required = true, in = ParameterIn.PATH, schema = @Schema(type = "int"))
    @GetMapping("/{id}")
    public ResultTemplate<AiUserVO> getDetail(@PathVariable("id") Long id) {
        return ResultTemplate.success(aiUserService.getDetail(id));
    }

    /**
     * AI Chat 用户创建
     */
    @Operation(summary = "创建AI Chat 用户")
    @PostMapping
    public ResultTemplate<Boolean> create(@RequestBody @Validated AiUserParam param) {
        return ResultTemplate.success(aiUserService.create(param));
    }

    /**
     * AI Chat 用户修改
     */
    @Operation(summary = "AI Chat 用户修改")
    @PutMapping("/{id}")
    public ResultTemplate<Boolean> update(@PathVariable("id") Long id, @RequestBody AiUserParam param) {
        return ResultTemplate.success(aiUserService.update(id, param));
    }

    /**
     * AI Chat 用户删除
     */
    @Operation(summary = "AI Chat 用户删除")
    @DeleteMapping("/{id}")
    public ResultTemplate<Boolean> delete(@PathVariable("id") Long id) {
        return ResultTemplate.success(aiUserService.deleteById(id));
    }
}