package com.coocaa.ad.cheese.authority.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coocaa.ad.cheese.authority.common.db.bean.DownloadAttachmentDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.DownloadAttachmentEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 下载附件接口
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
public interface DownloadAttachmentMapper extends BaseMapper<DownloadAttachmentEntity> {

    /**
     * 按条件查询下载附件列表
     *
     * @param page      分页信息
     * @param condition 查询条件
     * @return 下载附件列表
     */
    IPage<DownloadAttachmentEntity> pageList(@Param("page") IPage<DownloadAttachmentEntity> page, @Param("condition") DownloadAttachmentDTO condition);
}