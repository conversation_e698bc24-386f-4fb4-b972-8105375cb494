package com.coocaa.ad.cheese.authority.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.coocaa.ad.cheese.authority.vo.channel.SysChannelVO;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-23
 */
@Data
@Accessors(chain = true)
@JsonPropertyOrder({"id", "name", "wno", "mobile", "email", "userName", "status", "roleNames", "cityGroupNames", "roles", "cityGroups"})
public class UserVO {
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "CC2362")
    private String wno;

    @Schema(description = "手机号", type = "String", example = "13012345678")
    private String mobile;

    @Schema(description = "邮箱", type = "String", example = "<EMAIL>")
    private String email;

    @Schema(description = "登陆名", type = "String", example = "admin")
    private String userName;

    @Schema(description = "密码, 需要对明文进行MD5", type = "String", hidden = true)
    private String password;

    @Schema(description = "状态 [false:禁用, true:启用]", type = "Boolean", example = "true")
    private Boolean status;

    @Schema(description = "用户类型 [1:内部用户, 2:外部代理商]", type = "Integer", example = "1")
    private Integer type;

    @Schema(description = "创建时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date createTime;

    @Schema(description = "创建人", type = "Integer", example = "1")
    private Integer creator;

    @Schema(description = "更新时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date updateTime;

    @Schema(description = "操作人", type = "Integer", example = "1")
    private Integer operator;

    @Schema(description = "角色名称", type = "String", example = "管理员角色, 销售角色")
    private String roleNames;

    @Schema(description = "城市组名称", type = "String", example = "华南战区, 西部战区")
    private String cityGroupNames;

    @Schema(description = "飞书openId", type = "String")
    private String openId;

    @Schema(description = "角色信息", type = "List")
    private List<RoleVO> roles;

    @Schema(description = "城市组信息", type = "List")
    private List<CityGroupVO> cityGroups;

    @Schema(description = "渠道信息", type = "List")
    private List<SysChannelVO> channels;

    @Schema(description = "渠道名称", type = "String", example = "1")
    private String channelNames;

    @Schema(description = "数据权限类型：1-个人;2-自己及下属；3-城市所有;4-全部", type = "String", example = "1")
    private Integer permissionType;

    @Schema(description = "用户部门OpenId", type = "String")
//    @JsonInclude
    private String departmentOpenId;

    @Schema(description = "用户部门名称", type = "String")
    private String departmentName;

    @Schema(description = "用户部门信息", type = "List")
    private List<UserDepartmentVO> userDepartments;


    @Schema(description = "机构Id", type = "Integer", example = "1")
    private Integer agencyId;

}
