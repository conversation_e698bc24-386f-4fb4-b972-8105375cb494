package com.coocaa.ad.cheese.authority.common.tools.utils;

public class UserIdUtils {
    private static final ThreadLocal<Integer> threadLocal = new ThreadLocal<>();

    public static Integer getUserId(){
        return threadLocal.get();
    }
    public static void setUserId(Integer userId){
         threadLocal.set(userId);
    }
    public static void remove(){
        threadLocal.remove();
    }


}
