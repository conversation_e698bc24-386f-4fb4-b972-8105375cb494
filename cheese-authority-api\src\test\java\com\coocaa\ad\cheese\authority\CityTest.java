package com.coocaa.ad.cheese.authority;

import com.alibaba.fastjson2.JSONObject;
import com.coocaa.ad.cheese.authority.common.db.entity.CityBaseMessageEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.CityEntity;
import com.coocaa.ad.cheese.authority.common.db.service.ICityBaseMessageService;
import com.coocaa.ad.cheese.authority.common.db.service.ICityService;
import com.coocaa.ad.cheese.authority.service.UserQueryService;
import com.coocaa.ad.cheese.authority.util.TencentCloudSmsUtil;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/10/31
 */
@Slf4j
@ActiveProfiles("local")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CityTest {
    @Autowired
    private ICityBaseMessageService cityBaseMessageService;
    @Autowired
    private ICityService cityService;
    @Autowired
    private UserQueryService userQueryService;

    @Test
    public void addCity() {
        List<CityBaseMessageEntity> list = cityBaseMessageService.lambdaQuery().ge(CityBaseMessageEntity::getLevel, 2)
                .le(CityBaseMessageEntity::getLevel, 3).list();

        Map<String, List<CityBaseMessageEntity>> map =
                list.stream().collect(Collectors.groupingBy(CityBaseMessageEntity::getParentcode));
        AtomicInteger i = new AtomicInteger();
        map.forEach((k, v) -> {
            CityBaseMessageEntity cityBaseMessageEntity;
            try {
                cityBaseMessageEntity = list.stream().filter(e -> e.getCode().equals(k))
                        .findAny().get();
                CityEntity city = saveParent(cityBaseMessageEntity, i);
                v.forEach(vv -> {
                    CityEntity sub = new CityEntity();
                    sub.setName(vv.getName());
                    sub.setStatus(false);
                    sub.setRank(i.getAndIncrement());
                    sub.setOperator(0);
                    sub.setDeleteFlag(false);
                    sub.setParentId(city.getId());
                    sub.setGbCode(vv.getCode());
                    sub.setBzCode("");
                    cityService.save(sub);
                });
            } catch (Exception e) {
                System.out.println("错误______________________:" + k);
            }

        });

    }

    private CityEntity saveParent(CityBaseMessageEntity cityBaseMessageEntity, AtomicInteger i) {
        CityEntity city = new CityEntity();
        city.setName(cityBaseMessageEntity.getName());
        city.setStatus(false);
        city.setRank(i.getAndIncrement());
        city.setOperator(0);
        city.setDeleteFlag(false);
        city.setParentId(0);
        city.setGbCode(cityBaseMessageEntity.getCode());
        cityService.save(city);
        return city;
    }

    @Test
    public void testChannel(){
        System.out.println(userQueryService.listCityAndChannelByUserId(18));

    }

    @Autowired
    TencentCloudSmsUtil tencentCloudSmsUtil;

    @Test
    public void sendSms(){
        String[] phoneNumberSet = {"+8615208427597","+8613881836255"};
        String[] templateParamSet = {"张彬鲜","2025-05-09","合同审批"};
        SendSmsResponse smsResponse = tencentCloudSmsUtil.sendSms("2425395", phoneNumberSet, templateParamSet, "广东创视科技");
        log.info("短信发送结果：{}", AbstractModel.toJsonString(smsResponse));
    }
}
