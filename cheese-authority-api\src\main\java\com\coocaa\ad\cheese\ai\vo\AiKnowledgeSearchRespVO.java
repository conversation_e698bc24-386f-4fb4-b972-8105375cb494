package com.coocaa.ad.cheese.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * AI知识库搜索结果VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiKnowledgeSearchRespVO", description = "AI知识库搜索结果VO")
public class AiKnowledgeSearchRespVO {

    @Schema(description = "搜索结果列表")
    private List<SearchResult> results;

    @Schema(description = "搜索耗时（毫秒）")
    private Long searchTime;

    @Schema(description = "总结果数量")
    private Integer totalCount;

    /**
     * 搜索结果项
     */
    @Data
    @Accessors(chain = true)
    @Schema(name = "SearchResult", description = "搜索结果项")
    public static class SearchResult {

        @Schema(description = "分块ID")
        private Long segmentId;

        @Schema(description = "文档ID")
        private Long documentId;

        @Schema(description = "文档名称")
        private String documentName;

        @Schema(description = "分块内容")
        private String content;

        @Schema(description = "相似度分数")
        private Double score;

        @Schema(description = "分块位置")
        private Integer position;

        @Schema(description = "字数")
        private Integer wordCount;
    }
}
