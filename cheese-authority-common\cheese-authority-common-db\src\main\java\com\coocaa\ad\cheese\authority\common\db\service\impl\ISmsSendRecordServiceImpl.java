package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.sms.SmsSendRecord;
import com.coocaa.ad.cheese.authority.common.db.mapper.sms.SmsSendRecordMapper;
import com.coocaa.ad.cheese.authority.common.db.service.sms.ISmsSendRecordService;
import org.springframework.stereotype.Service;

/**
 * @program: cheese-authority
 * @ClassName ISmsSendRecordServiceImpl
 * @description:
 * @author: zhangbinxian
 * @create: 2025-05-08 10:06
 * @Version 1.0
 **/
@Service
public class ISmsSendRecordServiceImpl extends ServiceImpl<SmsSendRecordMapper, SmsSendRecord> implements ISmsSendRecordService {

}
