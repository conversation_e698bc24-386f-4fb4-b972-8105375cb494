package com.coocaa.ad.cheese.authority.common.db.entity.approve;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("approve_internal_task_record")
public class ApproveInternalTaskRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 规则编号 */
    private Integer ruleCode;

    /** 审批实例code */
    private String instanceCode;

    /** 审批任务表ID */
    private Integer taskId;

    /** 审批节点表ID */
    private Integer nodeId;

    /** 审批人员ID */
    private Integer userId;

    /** 审批结果，字典0144 */
    private String operate;

    /** 审批意见 */
    private String comment;

    /** 任务名称 */
    private String taskName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;
}
