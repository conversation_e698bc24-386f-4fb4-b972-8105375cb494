package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/15
 */
@Data
public class CityStatusVO {

    private Integer id;

    @Schema(description = "城市或者区县名称")
    private String name;

    @Schema(description = "国际编码")
    private String gbCode;

    @Schema(description = "业务编码")
    private String bzCode;

    @Schema(description = "是否开通")
    private Boolean status;


}
