package com.coocaa.ad.cheese.authority.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("department")
public class DepartmentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID", type = "String", example = "")
    private String departmentId;

    /**
     * 父部门ID
     */
    @Schema(description = "父部门ID", type = "String", example = "")
    private String parentDepartmentId;

    /**
     * 部门的 open_department_id
     */
    @Schema(description = "部门的 open_department_id", type = "String", example = "")
    private String openDepartmentId;

    /**
     * 所有部门信息
     */
    @Schema(description = "所有部门信息", type = "String", example = "")
    private String parentDepartmentList;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称", type = "String", example = "")
    private String name;

    /**
     * 部门主管的用户 ID
     */
    @Schema(description = "部门主管的用户 ID", type = "String", example = "")
    private String leaderUserId;

    /**
     * 部门的排序,取值越小排序越靠前。
     */
    @Schema(description = "部门的排序,取值越小排序越靠前。", type = "String", example = "")
    private String rank;

    /**
     * 是否被删除:true-是；false-否
     */
    @Schema(description = "是否被删除:true-是；false-否", type = "String", example = "")
    private String status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", type = "String", example = "")
    private Date createTime;

    /**
     * 创建人
     */
    @Schema(description = "创建人", type = "String", example = "")
    private Integer creator;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", type = "String", example = "")
    private Date updateTime;

    /**
     * 操作人
     */
    @Schema(description = "操作人", type = "String", example = "")
    private Integer operator;

    /**
     * 公司下成员
     */
    @Schema(description = "公司下成员", type = "List", example = "")
    private List<UserVO> userList;

}
