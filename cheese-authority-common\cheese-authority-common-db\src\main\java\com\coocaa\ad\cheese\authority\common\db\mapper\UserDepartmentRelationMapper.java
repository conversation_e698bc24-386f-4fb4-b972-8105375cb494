package com.coocaa.ad.cheese.authority.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.authority.common.db.entity.UserCompanyDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户部门关联信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-07
 */
public interface UserDepartmentRelationMapper extends BaseMapper<UserDepartmentRelationEntity> {
    /**
     * 获取用户公司部门信息
     * @param userId
     * @return
     */
    List<UserCompanyDepartmentEntity> getUserCompanyDepartmentList(@Param("userId")Integer userId);
}
