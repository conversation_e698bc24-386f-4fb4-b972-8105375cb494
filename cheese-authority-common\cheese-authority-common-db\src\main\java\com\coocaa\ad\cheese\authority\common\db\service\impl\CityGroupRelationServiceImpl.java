package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.ad.cheese.authority.common.db.entity.CityGroupRelationEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.CityGroupRelationMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.service.ICityGroupRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 城市组包含的城市 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class CityGroupRelationServiceImpl extends ServiceImpl<CityGroupRelationMapper, CityGroupRelationEntity> implements ICityGroupRelationService {

    @Override
    public List<CityGroupRelationEntity> listByGroupId(Integer id) {
        return lambdaQuery().eq(CityGroupRelationEntity::getGroupId, id)
                .list();
    }

    @Override
    public void removeByGroupId(Integer id) {
        LambdaQueryWrapper<CityGroupRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CityGroupRelationEntity::getGroupId,id);
        remove(wrapper);
    }

    @Override
    public List<CityGroupRelationEntity> listByGroupIds(List<Integer> ids) {
        if(CollectionUtils.isNotEmpty(ids)){
            return lambdaQuery().in(CityGroupRelationEntity::getGroupId,ids).list();
        }
        return new ArrayList<>();
    }

    @Override
    public void removeByGroupIdAndCityId(List<Integer> groupIds, Set<Integer> setCityIds) {
        LambdaQueryWrapper<CityGroupRelationEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CityGroupRelationEntity::getGroupId,groupIds)
                .in(CityGroupRelationEntity::getCityId,setCityIds);
        remove(wrapper);
    }
}
