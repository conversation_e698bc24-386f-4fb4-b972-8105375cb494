package com.coocaa.ad.cheese.authority.common.tools.utils;

/**
 * <AUTHOR>
 * @since 2024-10-22
 */

public class StringUtils extends org.apache.commons.lang3.StringUtils {
    private StringUtils() {
    }

    public static String fileCode(int code) {
        int length = (code + "").length();
        return ("0".repeat(Math.max(0, 4 - length))) + code;
    }

    public static String getEnableMsg(Boolean enable) {
        return enable ? "启用" : "禁用";
    }

    public static String file2Code(int code) {
        int length = (code + "").length();
        return ("0".repeat(Math.max(0, 2 - length))) + code;
    }
    /**
     * 对邮箱进行脱敏
     * <EMAIL>          => a*<EMAIL>
     * <EMAIL>           => a*@qq.com
     * <EMAIL>            => *@qq.com
     * <EMAIL>  => a*********<EMAIL>
     *
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String desensitizedEmail(String email) {
        if (isBlank(email)) return org.apache.commons.lang3.StringUtils.EMPTY;
        int index = org.apache.commons.lang3.StringUtils.indexOf(email, '@');

        String prefix = email.substring(0, index);
        String suffix = email.substring(index);

        if (index <= 1) return "*" + suffix;
        if (prefix.length() <= 2) return prefix.charAt(0) + "*" + suffix;
        return prefix.charAt(0) + repeat("*", prefix.length() - 2) + prefix.charAt(prefix.length() - 1) + suffix;
    }
}
