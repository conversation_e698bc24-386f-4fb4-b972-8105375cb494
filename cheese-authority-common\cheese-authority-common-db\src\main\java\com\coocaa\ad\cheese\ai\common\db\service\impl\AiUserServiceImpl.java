package com.coocaa.ad.cheese.ai.common.db.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.ai.common.db.bean.AiUserDTO;
import com.coocaa.ad.cheese.ai.common.db.entity.AiUserEntity;
import com.coocaa.ad.cheese.ai.common.db.mapper.AiUserMapper;
import com.coocaa.ad.cheese.ai.common.db.service.IAiUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

/**
 * AI Chat 对话 服务实现类
 *
 * <AUTHOR>
 * @since 2025-7-8
 */
@Slf4j
@Service
@Primary
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class AiUserServiceImpl extends ServiceImpl<AiUserMapper, AiUserEntity> implements IAiUserService {

    @Override
    public IPage<AiUserEntity> pageList(IPage<AiUserEntity> page, AiUserDTO aiUser) {
        return baseMapper.pageList(page, aiUser);
    }
}