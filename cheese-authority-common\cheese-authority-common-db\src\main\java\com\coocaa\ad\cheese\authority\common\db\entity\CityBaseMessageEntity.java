package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("city_base_message")
public class CityBaseMessageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private String code;

    private String parentcode;

    private String level;

    private String name;

    private String parentcodes;

    private String province;

    private String city;

    private String district;

    private String town;

    private String pinyin;

    private String jianpin;

    private String firstchar;

    private String tel;

    private String zip;

    private String lng;

    private String lat;


}
