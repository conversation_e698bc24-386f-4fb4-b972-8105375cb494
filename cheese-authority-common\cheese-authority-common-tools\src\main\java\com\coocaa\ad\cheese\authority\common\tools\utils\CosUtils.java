package com.coocaa.ad.cheese.authority.common.tools.utils;

import java.util.TreeMap;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.tencent.cloud.CosStsClient;
import com.tencent.cloud.Response;

import lombok.extern.slf4j.Slf4j;

/*
 * COS工具类
 */
@Slf4j
@Component
public class CosUtils {

    private static String secretId;
    private static String secretKey;
    private static String bucket;
    private static String region;
    private static String allowPrefixes;

    private static String secretId2;
    private static String secretKey2;
    private static String bucket2;
    private static String region2;
    private static String allowPrefixes2;

    @Value("${cos.secret-id}")
    public void setSecretId(String secretId) {
        CosUtils.secretId = secretId;
    }

    @Value("${cos.secret-key}")
    public void setSecretKey(String secretKey) {
        CosUtils.secretKey = secretKey;
    }

    @Value("${cos.bucket-name}")
    public void setBucket(String bucket) {
        CosUtils.bucket = bucket;
    }

    @Value("${cos.region}")
    public void setRegion(String region) {
        CosUtils.region = region;
    }

    @Value("${cos.allow-prefixes}")
    public void setAllowPrefixes(String allowPrefixes) {
        CosUtils.allowPrefixes = allowPrefixes;
    }

    @Value("${cos2.secret-id}")
    public void setSecretId2(String secretId) {
        CosUtils.secretId2 = secretId;
    }

    @Value("${cos2.secret-key}")
    public void setSecretKey2(String secretKey) {
        CosUtils.secretKey2 = secretKey;
    }

    @Value("${cos2.bucket-name}")
    public void setBucket2(String bucket) {
        CosUtils.bucket2 = bucket;
    }

    @Value("${cos2.region}")
    public void setRegion2(String region) {
        CosUtils.region2 = region;
    }

    @Value("${cos2.allow-prefixes}")
    public void setAllowPrefixes2(String allowPrefixes) {
        CosUtils.allowPrefixes2 = allowPrefixes;
    }

    // 获取cos临时密钥
    public static Response getCosSts() {
        TreeMap<String, Object> config = new TreeMap<String, Object>();

        try {
			 // 云 api 密钥 SecretId
             config.put("secretId", secretId);
             // 云 api 密钥 SecretKey
             config.put("secretKey", secretKey);

            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", 1800);

            // bucket
            config.put("bucket", bucket);
            // 地域
            config.put("region", region);

            // 可以通过 allowPrefixes 指定前缀数组
            config.put("allowPrefixes", allowPrefixes.split(","));

             // 权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[] {
                     "name/cos:*"
            };
            config.put("allowActions", allowActions);

            return CosStsClient.getCredential(config);
        } catch (Exception e) {
        	log.error("getCosSts error {}", e);
            throw new IllegalArgumentException("no valid secret");
        }
    }

    // 获取cos私有读写桶临时密钥
    public static Response getCosSts2() {
        TreeMap<String, Object> config = new TreeMap<String, Object>();

        try {
			 // 云 api 密钥 SecretId
             config.put("secretId", secretId2);
             // 云 api 密钥 SecretKey
             config.put("secretKey", secretKey2);

            // 临时密钥有效时长，单位是秒
            config.put("durationSeconds", 1800);

            // bucket
            config.put("bucket", bucket2);
            // 地域
            config.put("region", region2);

            // 可以通过 allowPrefixes 指定前缀数组
            config.put("allowPrefixes", allowPrefixes2.split(","));

             // 权限列表请看 https://cloud.tencent.com/document/product/436/31923
            String[] allowActions = new String[] {
                    "name/cos:*"
            };
            
            config.put("allowActions", allowActions);

            return CosStsClient.getCredential(config);
        } catch (Exception e) {
        	log.error("getCosSts2 error {}", e);
            throw new IllegalArgumentException("no valid secret");
        }
    }
}
