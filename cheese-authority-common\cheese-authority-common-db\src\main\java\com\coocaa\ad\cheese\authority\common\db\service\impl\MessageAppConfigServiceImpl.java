package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.message.MessageAppConfigEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.message.MessageAppConfigMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IMessageAppConfigService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-04-15
 */
@Service
public class MessageAppConfigServiceImpl extends ServiceImpl<MessageAppConfigMapper, MessageAppConfigEntity> implements IMessageAppConfigService {
}
