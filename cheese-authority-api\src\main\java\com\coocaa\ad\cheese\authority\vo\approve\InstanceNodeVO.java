package com.coocaa.ad.cheese.authority.vo.approve;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InstanceNodeVO {

    /** 主键ID */
    @Schema(description = "节点ID")
    private Integer id;

    /** 规则编号 */
    @Schema(description = "规则编号")
    private Integer ruleCode;

    /** 审批实例code */
    @Schema(description = "审批实例code")
    private String instanceCode;

    /** 规则审批人员表ID */
    @Schema(description = "规则审批人员表ID")
    private Integer personId;

    /** 审核人员排序 */
    @Schema(description = "审核人员排序")
    private Integer rank;

    /** 审批人员ID */
    @Schema(description = "审批人员ID")
    private Integer userId;

    @Schema(description = "审批人员")
    private String userName;

    /** 取消原因（字典0140） */
    @Schema(description = "取消原因（字典0140）")
    private String cancelReason;

    @Schema(description = "取消原因")
    private String cancelReasonName;

    /** 任务状态（字典0139） */
    @Schema(description = "任务状态（字典0139）")
    private String nodeStatus;

    @Schema(description = "任务状态")
    private String nodeStatusName;

    /** 节点开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "节点开始时间")
    private LocalDateTime startTime;

    /** 节点结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "节点结束时间")
    private LocalDateTime endTime;

    /** 是否为审批节点，0：提交人节点，1：审批节点，2：结束节点 */
    @Schema(description = "是否为审批节点，0：提交人节点，1：审批节点，2：结束节点")
    private Integer approvalFlag;

    /** 审批结果，字典0138 */
    @Schema(description = "审批结果，字典0138")
    private String approvalResult;

    @Schema(description = "审批结果")
    private String approvalResultName;

    /** 审批意见 */
    @Schema(description = "审批意见")
    private String comment;

    @Schema(description = "操作，字典0144")
    private String operate;
    
    @Schema(description = "任务名称")
    private String taskName;
}
