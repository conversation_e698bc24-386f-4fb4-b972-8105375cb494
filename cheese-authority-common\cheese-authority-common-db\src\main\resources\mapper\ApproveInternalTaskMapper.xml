<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalTaskMapper">


    <select id="listForTask"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalTaskEntity">
        select t.id as id,t.rule_code as ruleCode,t.instance_code as instanceCode,t.node_id as nodeId,t.user_id as userId
        ,t.approval_result as approvalResult,t.node_status as nodeStatus,t.start_time as startTime,t.end_time as endTime
        ,t.comment as comment,i.create_time as instanceCreateTime,i.user_id as instanceUserId,i.approval_name as approvalName
        ,t.task_name as taskName
        from approve_internal_task t
        inner join approve_internal_instance i on t.instance_code = i.instance_code
        where
        1=1
        <if test="dto.userFlag">
            and t.user_id = #{dto.userId}
        </if>
        <if test="dto.status != null and dto.status.size > 0">
            and t.node_status in
            <foreach collection="dto.status" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="dto.ruleCode != null">
            and t.rule_code = #{dto.ruleCode}
        </if>
        order by ${dto.sortFiled} ${dto.sortRule}
    </select>
    <select id="getUserDealCount"
            resultType="com.coocaa.ad.cheese.authority.common.db.vo.approve.TaskDealCountSelectVO">
        SELECT
        rule_code as code,
        count(*) as count
        FROM
        approve_internal_task
        WHERE
        node_status = '0139-2'
        AND user_id = #{userId}
        AND rule_code IN
        <foreach collection="codes" item="item" open="(" close=")" separator=",">#{item}</foreach>
        GROUP BY
        rule_code
    </select>
</mapper>
