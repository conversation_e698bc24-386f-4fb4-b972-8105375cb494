package com.coocaa.ad.cheese.authority.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coocaa.ad.cheese.authority.common.db.bean.ResourceDO;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface UserMapper extends BaseMapper<UserEntity> {

    UserEntity queryUser(@Param("name") String name, @Param("pwd") String pwd);


    /**
     * 用户登陆
     *
     * @param userName 用户名
     * @param wno      工号
     * @param mobile   手机号
     * @param email    邮箱
     * @return 用户信息
     */
    List<UserEntity> login(@Param("userName") String userName, @Param("wno") String wno,
                     @Param("mobile") String mobile, @Param("email") String email);

    /**
     * 查询用户列表
     *
     * @param page         分页信息
     * @param plainKeyword 明文关键字，应用于: 登陆名、姓名、工号
     * @param fakeKeyword  加密关键字，应用于: 手机、邮箱
     * @param roleIds      角色id
     * @param cityGroupIds 城市组id
     * @return 用户列表
     */
    Page<UserEntity> listByCondition(@Param("page") IPage<UserEntity> page,
                                     @Param("userId") Integer userId,
                                     @Param("plainKeyword") String plainKeyword,
                                     @Param("fakeKeyword") String fakeKeyword,
                                     @Param("roleIds") List<Integer> roleIds,
                                     @Param("cityGroupIds") List<Integer> cityGroupIds,
                                     @Param("channelIds") List<Integer> channelIds);

    List<ResourceDO> listUserRoleResources3Type(@Param("userId") Integer userId, @Param("platform") String platform);

    /**
     *根据用户ID查询部门信息
     * @param userId
     * @return
     */
    List<UserDepartmentEntity> listUserDepartment(@Param("userId") Integer userId,@Param("departmentName") String departmentName,@Param("departmentIdList") List<Integer> departmentIdList);
    List<UserDepartmentEntity> listDepartmentByUserId(@Param("userIds") Set<Integer> userId);

    /**
     *根据飞书openId查询用户信息
     * @param openId
     * @return
     */
    UserEntity getUserByFsOpenId(@Param("openId") String openId);

    /**
     * 查询用户列表
     *
     * @param page         分页信息
     * @param plainKeyword 明文关键字，应用于: 登陆名、姓名、工号
     * @param fakeKeyword  加密关键字，应用于: 手机、邮箱
     * @param roleIds      角色id
     * @param cityGroupIds 城市组id
     * @return 用户列表
     */
    /**
     * 根据机构ID分页查询用户信息
     * @param page
     * @param agencyId
     * @return
     */
    Page<UserEntity> listByAgencyId(@Param("page") IPage<UserEntity> page,
                                     @Param("agencyId") Integer agencyId);
}
