package com.coocaa.ad.cheese.authority.vo.approve;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Data
public class ApproveAppVO {

    /**
     * ID
     */
    private Integer id;

    /**
     * 消息topic
     */
    @Schema(description = "消息主题")
    private String topic;

    /**
     * 审批类型
     */
    @Schema(description = "审批类型code")
    private String approveType;

    @Schema(description = "审批类型")
    private String approveTypeName;

    /**
     * 审批定义 Code
     */
    @Schema(description = "审批定义code")
    private String approvalCode;

    /**
     * 飞书应用ID
     */
    @Schema(description = "飞书应用ID")
    private String appId;

    /**
     * 飞书应用密钥
     */
    @Schema(description = "飞书应用密钥")
    private String appSecret;

}
