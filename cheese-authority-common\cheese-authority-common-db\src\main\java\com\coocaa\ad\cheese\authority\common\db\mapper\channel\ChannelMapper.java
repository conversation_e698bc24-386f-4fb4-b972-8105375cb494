package com.coocaa.ad.cheese.authority.common.db.mapper.channel;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.authority.common.db.entity.channel.ChannelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Mapper
public interface ChannelMapper extends BaseMapper<ChannelEntity> {
    List<ChannelEntity> getUserChannel(@Param("userId") Integer userId, @Param("ids") List<Integer> ids);
}
