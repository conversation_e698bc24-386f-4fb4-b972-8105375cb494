package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.bean.CityDetailParam;
import com.coocaa.ad.cheese.authority.common.db.bean.CityDetailVO;
import com.coocaa.ad.cheese.authority.common.db.bean.CityUpdateCodeParam;
import com.coocaa.ad.cheese.authority.common.db.bean.ProvinceCityDistrictSelectVO;
import com.coocaa.ad.cheese.authority.common.db.entity.CityEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.CityMapper;
import com.coocaa.ad.cheese.authority.common.db.service.ICityService;
import com.coocaa.ad.cheese.authority.common.tools.utils.MathUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class CityServiceImpl extends ServiceImpl<CityMapper, CityEntity> implements ICityService {
    @Override
    public List<CityEntity> queryByName(String name) {
        return lambdaQuery().eq(CityEntity::getName, name).
                eq(CityEntity::getDeleteFlag, false).list();
    }
    //查询一级城市数量

    @Override
    public int countCity() {
        return MathUtils.unBox(lambdaQuery().eq(CityEntity::getLevel, 2)
                .eq(CityEntity::getDeleteFlag, false).count());
    }

    @Override
    public List<CityEntity> queryByParentId(Integer id) {
        return lambdaQuery().eq(CityEntity::getParentId, id).
                eq(CityEntity::getDeleteFlag, false).orderByDesc(CityEntity::getStatus).orderByAsc(CityEntity::getId).list();
    }

    @Override
    public List<CityEntity> selectList(Boolean status) {
        return lambdaQuery().eq(CityEntity::getLevel, 2).eq(status != null, CityEntity::getStatus, true).
                eq(CityEntity::getDeleteFlag, false).list();
    }

    @Override
    public List<CityEntity> listByCodes(Collection<String> codes) {
        return lambdaQuery().in(CityEntity::getBzCode, codes).
                eq(CityEntity::getDeleteFlag, false).list();
    }

    @Override
    public void removeByParentId(Integer id) {
        LambdaQueryWrapper<CityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CityEntity::getParentId, id);
        remove(wrapper);
    }

    @Override
    public List<CityEntity> listByIdsEnable(Collection<Integer> ids, Boolean status) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return lambdaQuery().in(CityEntity::getId, ids).eq(status != null, CityEntity::getStatus, status).list();
        }
        return new ArrayList<>();
    }

    @Override
    public List<CityEntity> listByCityIdEnable(Integer cityId) {
        return lambdaQuery().eq(CityEntity::getParentId, cityId).eq(CityEntity::getStatus, true)
                .list();
    }

    @Override
    public List<CityEntity> queryByNameAndParentId(String name) {
        return lambdaQuery()
                .eq(CityEntity::getName, name)
                .eq(CityEntity::getLevel, 2)
                .eq(CityEntity::getDeleteFlag, false)
                .list();
    }

    @Override
    public List<CityEntity> listByCodesAndParentId(Collection<String> codes) {
        return lambdaQuery()
                .in(CityEntity::getBzCode, codes)
                .eq(CityEntity::getLevel, 2)
                .eq(CityEntity::getDeleteFlag, false)
                .list();
    }

    @Override
    public CityEntity getCityByName(String name) {
        return lambdaQuery().eq(CityEntity::getName, name)
                .eq(CityEntity::getLevel, 2)
                .one();
    }

    @Override
    public CityEntity getCounty(String cityName, String countyName) {
        CityEntity city = getCityByName(cityName);
        Integer id = city.getId();
        return lambdaQuery().eq(CityEntity::getParentId, id)
                .eq(CityEntity::getName, countyName).one();
    }

    @Override
    @Transactional
    public void updateImportCityCode(List<CityUpdateCodeParam> param) {
        if (CollectionUtils.isNotEmpty(param)) {
            Map<String, List<CityUpdateCodeParam>> map = param.stream().collect(Collectors.groupingBy(e -> e.getCity() + "_" + e.getCityCode()));
            map.forEach((cityCounty, cityUpdateCodeParams) -> {
                updateCity(cityCounty);

                updateCounty(cityUpdateCodeParams);
            });

        }
    }

    private void updateCounty(List<CityUpdateCodeParam> cityUpdateCodeParams) {
        cityUpdateCodeParams.forEach(e -> {
            CityEntity county = getCounty(e.getCity(), e.getCounty());
            Integer countyId = county.getId();
            CityEntity uc = new CityEntity();
            uc.setId(countyId);
            uc.setBzCode(e.getCountyCode());
            updateById(uc);
        });
    }

    private void updateCity(String cityCounty) {
        String[] s = cityCounty.split("_");
        String city = s[0];
        String cityCode = s[1];
        CityEntity cityByName = getCityByName(city);
        Integer cityId = cityByName.getId();
        CityEntity updateCity = new CityEntity();
        updateCity.setBzCode(cityCode);
        updateCity.setId(cityId);
        updateById(updateCity);
    }


    @Override
    public List<CityEntity> listByGbCode(List<String> gbCodes) {
        if(CollectionUtils.isNotEmpty(gbCodes)){
            return lambdaQuery().in(CityEntity::getGbCode, gbCodes).list();
        }
        return new ArrayList<>();
    }

    @Override
    public CityEntity getCityByGbCode(String code) {
        CityEntity one = lambdaQuery().eq(CityEntity::getGbCode, code).one();
        Integer parentId = one.getParentId();
        if (one.getLevel() > 2) {
            return getById(parentId);
        }
        return one;
    }

    @Override
    public List<CityEntity> getGbByCityId(Integer cityId) {
        CityEntity entity = getById(cityId);
        List<CityEntity> result = Lists.newArrayList(entity);
        if(entity.getLevel()==2){
            List<CityEntity> subList = listByParentId(cityId);
            result.addAll(subList);
        }
        return result;
    }

    @Override
    public List<CityDetailVO> getDetailByNames(List<CityDetailParam> params) {
        return baseMapper.getDetailByNames(params);
    }

    @Override
    public List<ProvinceCityDistrictSelectVO> getProvinceCity(Set<Integer> districts) {
        return this.baseMapper.getProvinceCity(districts);
    }

    private List<CityEntity> listByParentId(Integer parentId) {
        return lambdaQuery().eq(CityEntity::getParentId,parentId).list();
    }
}
