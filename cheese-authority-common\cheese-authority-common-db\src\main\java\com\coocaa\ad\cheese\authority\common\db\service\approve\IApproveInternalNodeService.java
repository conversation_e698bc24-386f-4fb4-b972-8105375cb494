package com.coocaa.ad.cheese.authority.common.db.service.approve;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalNodeEntity;
import com.coocaa.ad.cheese.authority.common.db.vo.approve.InstanceNodeCommonVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IApproveInternalNodeService extends IService<ApproveInternalNodeEntity> {

    /**
     * 查询审批实例节点
     */
    List<InstanceNodeCommonVO> getInstanceNode(String instanceCode);

    List<InstanceNodeCommonVO> getInstanceNodeBatch(List<String> instanceCodes);
}
