package com.coocaa.ad.cheese.ai.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 向量搜索结果模型
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
public class VectorSearchResult {

    /**
     * 向量ID
     */
    private String vectorId;

    /**
     * 相似度分数
     */
    private Double score;

    /**
     * 向量文档
     */
    private VectorDocument document;

    /**
     * 距离值
     */
    private Double distance;
}
