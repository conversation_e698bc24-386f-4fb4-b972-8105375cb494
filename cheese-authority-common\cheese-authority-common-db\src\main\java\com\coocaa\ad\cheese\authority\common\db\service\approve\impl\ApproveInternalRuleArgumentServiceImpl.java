package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleArgumentEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalRuleArgumentMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalRuleArgumentService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalRuleArgumentServiceImpl extends ServiceImpl<ApproveInternalRuleArgumentMapper, ApproveInternalRuleArgumentEntity> implements IApproveInternalRuleArgumentService {
    @Override
    public List<ApproveInternalRuleArgumentEntity> getArgByRuleNo(Integer ruleNo) {
        return this.baseMapper.getArgByRuleNo(ruleNo);
    }
}
