# 语音识别COS URL接口说明

## 概述

`recognizeSpeechWithSdk` 接口已经修改为支持通过COS URL进行语音识别。前端只需传入COS上的语音文件URL，后端会自动下载文件并进行语音识别处理。

## 功能特性

- ✅ **COS URL支持**：直接传入COS语音文件URL
- ✅ **自动下载**：后端自动从COS下载音频文件
- ✅ **临时文件管理**：自动创建和清理临时文件
- ✅ **文件格式检测**：自动识别音频文件格式
- ✅ **错误处理**：完整的错误处理和日志记录
- ✅ **资源清理**：确保临时文件被正确清理

## API接口

### 接口地址
```
POST /sys/speech/recognize-sdk
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| url | String | 是 | COS语音文件URL |

### 请求示例

#### curl请求
```bash
curl -X POST \
  http://localhost:8001/sys/speech/recognize-sdk \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'url=https://test-1330579985.cos.ap-guangzhou.myqcloud.com/audio/test.mp3'
```

#### JavaScript请求
```javascript
const formData = new FormData();
formData.append('url', 'https://test-1330579985.cos.ap-guangzhou.myqcloud.com/audio/test.mp3');

fetch('/sys/speech/recognize-sdk', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.data.success) {
    console.log('识别结果:', data.data.text);
  } else {
    console.error('识别失败:', data.data.message);
  }
});
```

### 响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "text": "识别出的文字内容",
    "message": "语音识别成功",
    "processingTime": 1500
  }
}
```

#### 失败响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": false,
    "text": null,
    "message": "语音识别失败: 具体错误信息",
    "processingTime": 0
  }
}
```

## 支持的音频格式

- **MP3** (.mp3)
- **WAV** (.wav)
- **M4A** (.m4a)
- **AMR** (.amr)
- **其他微信支持的音频格式**

## 文件大小限制

- **最大文件大小**: 10MB
- **建议文件大小**: 小于5MB以获得更好的处理速度

## 处理流程

```mermaid
graph TD
    A[前端传入COS URL] --> B[验证URL格式]
    B --> C[创建临时文件]
    C --> D[从COS下载文件]
    D --> E[验证下载文件]
    E --> F[调用微信语音识别SDK]
    F --> G[返回识别结果]
    G --> H[清理临时文件]
```

### 详细步骤

1. **URL验证**: 检查传入的COS URL是否为空
2. **临时文件创建**: 根据URL文件扩展名创建临时文件
3. **文件下载**: 使用ObjectUtils从COS下载文件到临时位置
4. **文件验证**: 检查下载的文件是否存在且不为空
5. **语音识别**: 调用微信SDK进行语音识别
6. **结果返回**: 返回识别结果或错误信息
7. **资源清理**: 删除临时文件

## 代码实现

### 控制器方法
```java
@PostMapping("/recognize-sdk")
public ResultTemplate<SpeechRecognitionVO> recognizeSpeechWithSdk(
        @RequestParam("url") String cosUrl) {
    
    File tempFile = null;
    try {
        // 1. 创建临时文件
        String fileExtension = getFileExtensionFromUrl(cosUrl);
        tempFile = File.createTempFile(UUID.fastUUID() + "_speech_", fileExtension);
        
        // 2. 从COS下载文件
        Download download = ObjectUtils.downloadFile(cosUrl, tempFile);
        download.waitForCompletion();
        
        // 3. 调用语音识别
        String recognizedText = wechatSpeechSdkService.recognizeSpeechFromFile(tempFile);
        
        return ResultTemplate.success(SpeechRecognitionVO.success(recognizedText));
        
    } catch (Exception e) {
        return ResultTemplate.success(SpeechRecognitionVO.error("语音识别失败: " + e.getMessage()));
    } finally {
        // 4. 清理临时文件
        if (tempFile != null && tempFile.exists()) {
            tempFile.delete();
        }
    }
}
```

### 服务方法
```java
public String recognizeSpeechFromFile(File file) throws Exception {
    // 验证文件
    if (file == null || !file.exists() || file.length() == 0) {
        throw new RuntimeException("语音文件无效");
    }
    
    // 检查文件大小
    if (file.length() > config.getMaxFileSize()) {
        throw new RuntimeException("文件过大");
    }
    
    // 调用语音识别
    return recognizeSpeechWithRetry(file);
}
```

## 错误处理

### 常见错误类型

| 错误类型 | 错误信息 | 解决方案 |
|----------|----------|----------|
| URL为空 | "COS URL不能为空" | 检查传入的URL参数 |
| 文件下载失败 | "下载的语音文件为空" | 检查COS URL是否可访问 |
| 文件过大 | "文件过大，超过10MB限制" | 压缩音频文件或降低质量 |
| 格式不支持 | "不支持的音频格式" | 转换为支持的格式 |
| SDK未初始化 | "微信SDK未初始化" | 检查AppID和AppSecret配置 |

### 错误日志示例
```
2025-07-17 10:30:15 WARN  - COS URL为空
2025-07-17 10:30:16 ERROR - 语音识别失败（SDK版本），COS URL: https://invalid-url.com/test.mp3
2025-07-17 10:30:17 INFO  - 临时文件清理成功: /tmp/abc123_speech_456.mp3
```

## 测试方法

### 1. 使用测试脚本
```bash
# 使用默认URL测试
./test-speech-recognition-cos.sh

# 使用自定义URL测试
./test-speech-recognition-cos.sh "https://your-cos-url.com/audio/test.mp3"
```

### 2. 手动测试
```bash
# 正常测试
curl -X POST \
  http://localhost:8001/sys/speech/recognize-sdk \
  -d 'url=https://test-1330579985.cos.ap-guangzhou.myqcloud.com/audio/test.mp3'

# 错误测试 - 空URL
curl -X POST \
  http://localhost:8001/sys/speech/recognize-sdk \
  -d 'url='

# 错误测试 - 无效URL
curl -X POST \
  http://localhost:8001/sys/speech/recognize-sdk \
  -d 'url=https://invalid-url.com/test.mp3'
```

## 性能优化

### 1. 文件下载优化
- 使用流式下载，避免内存占用过大
- 设置合理的下载超时时间
- 支持断点续传（如果需要）

### 2. 临时文件管理
- 使用UUID生成唯一文件名，避免冲突
- 在finally块中确保文件被清理
- 定期清理遗留的临时文件

### 3. 并发处理
- 支持多个请求同时处理
- 临时文件使用唯一名称，避免冲突
- 合理设置线程池大小

## 安全考虑

### 1. URL验证
- 验证URL格式的合法性
- 限制允许的域名（如果需要）
- 防止SSRF攻击

### 2. 文件安全
- 限制文件大小和格式
- 临时文件使用安全的存储位置
- 及时清理临时文件

### 3. 错误信息
- 不暴露敏感的系统信息
- 记录详细的错误日志用于调试
- 返回用户友好的错误信息

## 部署配置

### 1. 微信小程序配置
```properties
# 微信小程序配置
wechat.mini.app-id=your_miniprogram_app_id
wechat.mini.app-secret=your_miniprogram_app_secret
wechat.mini.language=zh_CN
wechat.mini.format=mp3
wechat.mini.max-retries=3
wechat.mini.retry-interval=1000
wechat.mini.max-file-size=10485760
```

### 2. COS配置
确保COS存储桶配置正确，允许应用访问音频文件。

### 3. 临时目录配置
确保应用有权限在系统临时目录创建和删除文件。

## 总结

语音识别COS URL接口已经成功实现，主要特点：

✅ **简化前端调用**：只需传入COS URL即可  
✅ **自动文件处理**：后端自动下载和清理文件  
✅ **完整错误处理**：详细的错误信息和日志  
✅ **资源管理**：确保临时文件被正确清理  
✅ **性能优化**：支持多种音频格式和并发处理  

该接口可以直接用于生产环境，为用户提供便捷的语音识别服务！
