package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 角色更新对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
@Data
public class RoleEditVO {
    @NotNull(message = "资源ID不能为空")
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "角色名称", type = "String", example = "销售")
    private String name;

    @Schema(description = "角色描述", type = "String", example = "主要应用于销售人员")
    private String description;

    @Schema(description = "角色拥有的资源ID列表", type = "List")
    private List<Integer> resourceIds;

    @Schema(description = "角色拥有的数据资源ID列表", type = "List")
    private List<Integer> resourceRoleIds;
}
