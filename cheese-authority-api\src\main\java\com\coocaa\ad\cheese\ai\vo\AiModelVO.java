package com.coocaa.ad.cheese.ai.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AI模型VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiModelVO", description = "AI模型VO")
public class AiModelVO {

    @Schema(description = "模型ID")
    private Long id;

    @Schema(description = "模型名称", maxLength = 100)
    private String modelName;

    @Schema(description = "模型标识符", maxLength = 100)
    private String modelKey;

    @Schema(description = "模型提供商", maxLength = 50)
    private String provider;

    @Schema(description = "模型类型", maxLength = 20)
    private String modelType;

    @Schema(description = "模型描述")
    private String description;

    @Schema(description = "最大上下文长度")
    private Integer maxTokens;

    @Schema(description = "输入价格（每1000tokens）")
    private BigDecimal inputPrice;

    @Schema(description = "输出价格（每1000tokens）")
    private BigDecimal outputPrice;

    @Schema(description = "是否启用（0-禁用，1-启用）")
    private Integer enabled;

    @Schema(description = "排序值")
    private Integer sort;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private Integer operator;

    @Schema(description = "删除标记（0-正常，1-删除）")
    private Integer deleteFlag;
}
