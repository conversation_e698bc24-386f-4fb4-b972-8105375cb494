package com.coocaa.ad.cheese.ai.common.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeSegmentEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * AI知识库文档分块Mapper
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
public interface AiKnowledgeSegmentMapper extends BaseMapper<AiKnowledgeSegmentEntity> {

    /**
     * 根据知识库ID查询所有分块
     *
     * @param knowledgeId 知识库ID
     * @return 分块列表
     */
    @Select("SELECT * FROM ai_knowledge_segment WHERE knowledge_id = #{knowledgeId} AND delete_flag = 0 ORDER BY document_id, position")
    List<AiKnowledgeSegmentEntity> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据文档ID查询分块
     *
     * @param documentId 文档ID
     * @return 分块列表
     */
    @Select("SELECT * FROM ai_knowledge_segment WHERE document_id = #{documentId} AND delete_flag = 0 ORDER BY position")
    List<AiKnowledgeSegmentEntity> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据向量ID查询分块
     *
     * @param vectorIds 向量ID列表
     * @return 分块列表
     */
    @Select("<script>" +
            "SELECT * FROM ai_knowledge_segment WHERE vector_id IN " +
            "<foreach collection='vectorIds' item='vectorId' open='(' separator=',' close=')'>" +
            "#{vectorId}" +
            "</foreach>" +
            " AND delete_flag = 0" +
            "</script>")
    List<AiKnowledgeSegmentEntity> selectByVectorIds(@Param("vectorIds") List<String> vectorIds);
}
