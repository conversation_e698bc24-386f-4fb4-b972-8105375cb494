package com.coocaa.ad.cheese.authority.common.tools.utils.date;

/**
 * 类DatePattern实现描述:
 *
 * <AUTHOR>
 * @date 2019年04月25 14:07
 */
public class DatePattern {

    /** 标准日期格式：yyyy-MM-dd */
    public final static String PATTERN_NORM_DATE = "yyyy-MM-dd";

    /** 标准时间格式：HH:mm:ss */
    public final static String PATTERN_NORM_TIME = "HH:mm:ss";

    /** 标准日期时间格式，精确到分：yyyy-MM-dd HH:mm */
    public final static String PATTERN_NORM_DATETIME_MINUTE = "yyyy-MM-dd HH:mm";

    /** 标准日期时间格式，精确到秒：yyyy-MM-dd HH:mm:ss */
    public final static String PATTERN_NORM_DATETIME = "yyyy-MM-dd HH:mm:ss";

    /** 标准日期时间格式，精确到毫秒：yyyy-MM-dd HH:mm:ss.SSS */
    public final static String PATTERN_NORM_DATETIME_MS = "yyyy-MM-dd HH:mm:ss.SSS";

    /** 标准日期格式：yyyy年MM月dd日 */
    public final static String PATTERN_CHINESE_DATE = "yyyy年MM月dd日";


    /** 标准日期格式：yyyyMMdd */
    public final static String PATTERN_PURE_DATE = "yyyyMMdd";

    /** 标准日期格式：HHmmss */
    public final static String PATTERN_PURE_TIME = "HHmmss";

    /** 标准日期格式：yyyyMMddHHmmss */
    public final static String PATTERN_PURE_DATETIME = "yyyyMMddHHmmss";

    /** 标准日期格式：yyyyMMddHHmmssSSS */
    public final static String PATTERN_PURE_DATETIME_MS = "yyyyMMddHHmmssSSS";


}
