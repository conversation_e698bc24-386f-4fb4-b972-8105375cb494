# 微信登录服务重构总结

## 概述

将微信登录相关的业务逻辑从 Controller 移到 `WechatLoginService` 中，让 Controller 尽量简洁，只负责接收请求和返回响应，业务逻辑全部在 Service 层处理。

## 重构内容

### 1. WechatLoginService 新增业务方法

#### getUserInfoAndCreateAiUser
```java
public WechatUserInfoRespVO getUserInfoAndCreateAiUser(WechatUserInfoReqVO userInfoReq) throws WxErrorException {
    // 1. 解密用户信息
    WxMaUserInfo wxUserInfo = wxMaService.getUserService().getUserInfo(
            userInfoReq.getSessionKey(),
            userInfoReq.getEncryptedData(),
            userInfoReq.getIv()
    );

    // 2. 创建或更新AI用户信息
    AiUserInfoVO aiUserInfo = createOrUpdateAiUser(wxUserInfo, userInfoReq.getOpenId());

    // 3. 构建响应
    return WechatUserInfoRespVO.builder()
            .nickName(wxUserInfo.getNickName())
            .avatarUrl(wxUserInfo.getAvatarUrl())
            // ... 其他字段
            .build();
}
```

#### getAiUserInfo
```java
public AiUserInfoVO getAiUserInfo(AiUserInfoReqVO userInfoReq) {
    // 调用AiUserService的业务方法
    return aiUserService.findOrCreateUser(userInfoReq);
}
```

#### updateUserInfo
```java
public Boolean updateUserInfo(AiUserUpdateReqVO updateReq) {
    // 获取现有用户信息
    AiUserVO existingUser = aiUserService.getDetail(updateReq.getId());
    
    // 构建更新参数
    AiUserParam updateParam = new AiUserParam();
    // ... 设置更新字段
    
    return aiUserService.update(updateReq.getId(), updateParam);
}
```

#### createOrUpdateAiUser (私有方法)
```java
private AiUserInfoVO createOrUpdateAiUser(WxMaUserInfo wxUserInfo, String openId) {
    // 构建用户信息请求
    AiUserInfoReqVO userInfoReq = new AiUserInfoReqVO();
    // ... 设置用户信息
    
    // 调用服务层方法查找或创建用户
    return aiUserService.findOrCreateUser(userInfoReq);
}
```

### 2. WechatLoginController 简化

#### 原来的 getUserInfo 方法
```java
// 原来有60多行业务逻辑代码
@PostMapping("/getUserInfo")
public ResultTemplate<WechatUserInfoRespVO> getUserInfo(...) {
    // 大量业务逻辑：解密、转换、创建用户等
}
```

#### 重构后的 getUserInfo 方法
```java
@PostMapping("/getUserInfo")
public ResultTemplate<WechatUserInfoRespVO> getUserInfo(
        @RequestBody @Validated WechatUserInfoReqVO userInfoReq) {
    
    log.info("收到获取用户详细信息请求，OpenID: {}", userInfoReq.getOpenId());

    try {
        // 调用服务层方法，包含用户信息解密和AI用户创建逻辑
        WechatUserInfoRespVO respVO = wechatLoginService.getUserInfoAndCreateAiUser(userInfoReq);
        return ResultTemplate.success(respVO);

    } catch (Exception e) {
        log.error("获取用户详细信息失败，OpenID: {}", userInfoReq.getOpenId(), e);
        return ResultTemplate.fail("获取用户详细信息失败: " + e.getMessage());
    }
}
```

#### getAiUserInfo 方法
```java
@PostMapping("/user/info")
public ResultTemplate<AiUserInfoVO> getAiUserInfo(
        @RequestBody @Validated AiUserInfoReqVO userInfoReq) {
    
    try {
        // 调用服务层方法查找或创建用户
        AiUserInfoVO userInfoVO = wechatLoginService.getAiUserInfo(userInfoReq);
        return ResultTemplate.success(userInfoVO);
        
    } catch (Exception e) {
        return ResultTemplate.fail("获取AI用户信息失败: " + e.getMessage());
    }
}
```

#### updateUserInfo 方法
```java
@PutMapping("/user")
public ResultTemplate<Boolean> updateUserInfo(
        @RequestBody @Validated AiUserUpdateReqVO updateReq) {
    
    try {
        // 调用服务层方法更新用户信息
        Boolean updated = wechatLoginService.updateUserInfo(updateReq);
        return ResultTemplate.success(updated);
        
    } catch (Exception e) {
        return ResultTemplate.fail("更新用户信息失败: " + e.getMessage());
    }
}
```

### 3. 依赖关系调整

#### WechatLoginService 依赖
```java
@Service
public class WechatLoginService {
    private final WechatMiniProgramConfig config;
    private final AiUserService aiUserService; // 新增依赖
    private WxMaService wxMaService;
}
```

#### WechatLoginController 依赖
```java
@RestController
public class WechatLoginController {
    private final WechatLoginService wechatLoginService; // 只保留这一个依赖
    // 删除了 AiUserService 依赖
}
```

## 重构优势

### 1. 职责分离
- **Controller**: 只负责接收请求、参数验证、调用服务、返回响应
- **Service**: 负责具体的业务逻辑处理

### 2. 代码简洁
- Controller 方法从60多行减少到10行左右
- 业务逻辑集中在 Service 层，便于维护

### 3. 可测试性
- Service 层的业务逻辑更容易进行单元测试
- Controller 层逻辑简单，减少测试复杂度

### 4. 可复用性
- Service 层的方法可以被其他 Controller 或服务调用
- 业务逻辑不与 HTTP 层耦合

### 5. 易于维护
- 业务逻辑变更只需修改 Service 层
- Controller 层保持稳定

## API接口

### 1. 获取用户信息（解密微信数据并创建AI用户）
```bash
POST /sys/wechat/getUserInfo
Content-Type: application/json

{
  "sessionKey": "session_key_from_login",
  "encryptedData": "encrypted_user_data",
  "iv": "initialization_vector",
  "openId": "user_open_id"
}
```

### 2. 获取AI用户信息（查找或创建）
```bash
POST /sys/wechat/user/info
Content-Type: application/json

{
  "openId": "user_open_id",
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "gender": 1,
  "country": "中国",
  "province": "广东省",
  "city": "深圳市",
  "language": "zh_CN"
}
```

### 3. 更新用户信息
```bash
PUT /sys/wechat/user
Content-Type: application/json

{
  "id": 1,
  "nickName": "新昵称",
  "avatarUrl": "新头像URL",
  "gender": 2
}
```

## 使用示例

### 1. 微信小程序登录流程
```javascript
// 1. 小程序登录获取code
wx.login({
  success: (res) => {
    // 2. 调用后端登录接口
    wx.request({
      url: '/sys/wechat/login',
      method: 'POST',
      data: { code: res.code }
    })
  }
})

// 3. 获取用户信息
wx.getUserProfile({
  success: (res) => {
    // 4. 调用后端获取用户信息接口（会自动创建AI用户）
    wx.request({
      url: '/sys/wechat/getUserInfo',
      method: 'POST',
      data: {
        sessionKey: 'from_login_response',
        encryptedData: res.encryptedData,
        iv: res.iv,
        openId: 'from_login_response'
      }
    })
  }
})
```

### 2. 直接创建AI用户
```javascript
// 直接创建或获取AI用户信息
wx.request({
  url: '/sys/wechat/user/info',
  method: 'POST',
  data: {
    openId: 'user_open_id',
    nickName: '用户昵称',
    avatarUrl: 'avatar_url'
  }
})
```

## 总结

重构后的代码结构更加清晰：

✅ **Controller 简洁**：只负责HTTP层面的处理  
✅ **Service 集中**：业务逻辑集中在服务层  
✅ **职责分离**：各层职责明确，便于维护  
✅ **易于测试**：业务逻辑与HTTP解耦  
✅ **可复用性**：服务方法可被多处调用  

这种架构更符合Spring Boot的最佳实践，提高了代码的可维护性和可测试性！
