package com.coocaa.ad.cheese.ai.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * AI角色VO
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Accessors(chain = true)
@Schema(name = "AiRoleVO", description = "AI角色VO")
public class AiRoleVO {

    @Schema(description = "角色ID")
    private Long id;

    @Schema(description = "角色名称", maxLength = 100)
    private String roleName;

    @Schema(description = "角色提示词")
    private String rolePrompt;

    @Schema(description = "模型ID")
    private Long modelId;

    @Schema(description = "模型信息")
    private AiModelVO model;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private Integer creator;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    private LocalDateTime updateTime;

    @Schema(description = "更新人")
    private Integer operator;

    @Schema(description = "删除标记（0-正常，1-删除）")
    private Integer deleteFlag;
}
