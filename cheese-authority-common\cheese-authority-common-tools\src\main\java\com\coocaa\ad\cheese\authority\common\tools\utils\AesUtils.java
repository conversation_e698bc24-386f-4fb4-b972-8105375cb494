package com.coocaa.ad.cheese.authority.common.tools.utils;


import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @ClassName AesUtil.java
 * @Description 对称加解密
 * @createTime 2022年03月28日 12:00:00
 */
@Slf4j
public class AesUtils {
    /**
     * 加密秘钥 默认设置一个 官网的获取秘钥的方式（如上注释代码）每次启动都会变化
     */
    private static final byte[] key = {13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13, 13};
    private static final SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, key);

    /**
     * 加密为16进制
     *
     * @param content
     * @return
     */
    public static String encryptHex(String content) {
        return aes.encryptHex(content);
    }

    /**
     * 解密为字符串
     *
     * @param encryptHex
     * @return
     */
    public static String decryptStr(String encryptHex) {
        return aes.decryptStr(encryptHex, CharsetUtil.CHARSET_UTF_8);
    }


    /**
     * 解密为字符串
     *
     * @param encryptHex
     * @return
     */
    public static String decryptSafely(String encryptHex) {
        try {
            return decryptStr(encryptHex);
        } catch (Exception ex) {
            log.warn("解密数据({})失败", encryptHex, ex);
            return encryptHex;
        }
    }
}
