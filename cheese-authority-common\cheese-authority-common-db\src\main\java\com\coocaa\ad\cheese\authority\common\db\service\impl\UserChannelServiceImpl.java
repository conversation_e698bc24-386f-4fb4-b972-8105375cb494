package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.channel.UserChannelEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.channel.UserChannelMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IUserChannelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class UserChannelServiceImpl extends ServiceImpl<UserChannelMapper, UserChannelEntity> implements IUserChannelService {
    @Override
    public List<UserChannelEntity> listByUserId(Integer userId) {
        return lambdaQuery().eq(UserChannelEntity::getUserId,userId)
                .list();
    }
}
