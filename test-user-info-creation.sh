#!/bin/bash

# 测试getUserInfo接口的用户创建功能
# 使用方法: ./test-user-info-creation.sh

BASE_URL="http://localhost:8001"

echo "=== 测试getUserInfo接口用户创建功能 ==="
echo "基础URL: $BASE_URL"
echo ""

# 测试1: 调用getUserInfo接口（会自动创建AI用户）
echo "🧪 测试1: 调用getUserInfo接口"
echo "📤 发送请求..."

REQUEST_DATA=$(cat <<EOF
{
  "sessionKey": "test_session_key_123",
  "encryptedData": "test_encrypted_data",
  "iv": "test_iv_vector",
  "openId": "wx_test_user_001"
}
EOF
)

echo "请求数据:"
echo "$REQUEST_DATA"
echo ""

RESPONSE=$(curl -s -X POST \
  "$BASE_URL/sys/wechat/user/info" \
  -H "Content-Type: application/json" \
  -d "$REQUEST_DATA")

echo "📥 响应内容:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"
echo ""

# 测试2: 调用getAiUserInfo接口
echo "🧪 测试2: 调用getAiUserInfo接口"
echo "📤 发送请求..."

AI_USER_REQUEST=$(cat <<EOF
{
  "openId": "wx_test_user_002",
  "nickName": "测试用户",
  "avatarUrl": "https://example.com/avatar.jpg",
  "gender": 1,
  "country": "中国",
  "province": "广东省",
  "city": "深圳市",
  "language": "zh_CN"
}
EOF
)

echo "请求数据:"
echo "$AI_USER_REQUEST"
echo ""

AI_RESPONSE=$(curl -s -X POST \
  "$BASE_URL/sys/wechat/user/info" \
  -H "Content-Type: application/json" \
  -d "$AI_USER_REQUEST")

echo "📥 响应内容:"
echo "$AI_RESPONSE" | jq '.' 2>/dev/null || echo "$AI_RESPONSE"
echo ""

# 测试3: 更新用户信息
echo "🧪 测试3: 更新用户信息"
echo "📤 发送请求..."

UPDATE_REQUEST=$(cat <<EOF
{
  "id": 1,
  "nickName": "更新后的昵称",
  "avatarUrl": "https://example.com/new_avatar.jpg",
  "gender": 2
}
EOF
)

echo "请求数据:"
echo "$UPDATE_REQUEST"
echo ""

UPDATE_RESPONSE=$(curl -s -X PUT \
  "$BASE_URL/sys/wechat/user" \
  -H "Content-Type: application/json" \
  -d "$UPDATE_REQUEST")

echo "📥 响应内容:"
echo "$UPDATE_RESPONSE" | jq '.' 2>/dev/null || echo "$UPDATE_RESPONSE"
echo ""

echo "=== 测试完成 ==="
echo ""
echo "💡 测试说明:"
echo "1. getUserInfo接口：解密微信用户信息并自动创建AI用户记录"
echo "2. getAiUserInfo接口：根据OpenID查找或创建AI用户"
echo "3. updateUserInfo接口：更新现有用户信息"
echo ""
echo "🔧 如果遇到问题，请检查:"
echo "- 微信小程序配置是否正确"
echo "- 数据库连接是否正常"
echo "- 请求参数格式是否正确"
