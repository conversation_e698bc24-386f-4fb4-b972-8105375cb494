<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.UserDepartmentRelationMapper">

    <select id="getUserCompanyDepartmentList"
            resultType="com.coocaa.ad.cheese.authority.common.db.entity.UserCompanyDepartmentEntity"
            parameterType="java.lang.Integer">
        SELECT
            udr.id AS userDepartmentRelationId,
            d.`name` AS departmentName,
            d.open_department_id AS departmentOpenId,
            d.parent_department_list AS parentDepartmentList
        FROM
            user_department_relation udr
            JOIN department d ON udr.department_id = d.open_department_id
        WHERE
            udr.user_id = #{userId} and d.`status`=false
    </select>
</mapper>
