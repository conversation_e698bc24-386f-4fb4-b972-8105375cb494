package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.RoleEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserRoleEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户角色 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IUserRoleService extends IService<UserRoleEntity> {

    /**
     * 根据用户查询有效的用户角色
     *
     * @param userId 用户ID
     * @param expand 是否展开角色信息
     * @return 角色列表
     */
    List<RoleEntity> listValidRoles(Integer userId, boolean expand);

    /**
     * 根据用户查询有效的用户角色
     *
     * @param userIds 用户ID列表
     * @return 角色列表
     */
    Map<Integer, List<RoleEntity>> listValidRoles(Collection<Integer> userIds);

}
