<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.DownloadTaskMapper">
    <!-- 按条件查询列表 -->
    <select id="pageList" resultType="com.coocaa.ad.cheese.authority.common.db.entity.DownloadTaskEntity">
        SELECT do.* 
        FROM download_task do
        <where>
            AND do.delete_flag = 0
            <if test="condition.id != null">
                 AND do.id = #{condition.id}
            </if>
            <if test="condition.creator != null">
                 AND do.creator = #{condition.creator}
            </if>
            <if test="condition.sysType != null and condition.sysType != ''">
                AND do.sys_type = #{condition.sysType}
            </if>
            <if test="condition.type != null and condition.type != ''">
                AND do.type = #{condition.type}
            </if>
            <if test="condition.name != null and condition.name != ''">
                AND do.name like concat('%',#{condition.name},'%')
            </if>
             <if test="condition.category != null and condition.category != ''">
                 AND do.category = #{condition.category}
             </if>
            <if test="condition.status != null and condition.status != ''">
                AND do.status = #{condition.status}
            </if>
        </where>
        ORDER BY do.update_time DESC
    </select>
</mapper>