package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.channel.ChannelEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
public interface IChannelService extends IService<ChannelEntity> {

    /**
     * 获取用户渠道权限
     * @param userId
     * @return
     */
    List<ChannelEntity> getUserChannel(Integer userId, List<Integer> ids);
}
