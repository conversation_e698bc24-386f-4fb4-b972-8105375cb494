package com.coocaa.ad.cheese.authority;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

//@EnableWebMvc
@SpringBootApplication(scanBasePackages = "com.coocaa.ad.cheese")
//@EnableAsync
//@EnableScheduling
@MapperScan({"com.coocaa.ad.cheese.authority.common.db.mapper", "com.coocaa.ad.cheese.ai.common.db.mapper"})
@EnableFeignClients(basePackages = {"com.coocaa.ad.cheese.authority.rpc"})
public class CheeseAuthorityApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(CheeseAuthorityApiApplication.class, args);
    }

}
