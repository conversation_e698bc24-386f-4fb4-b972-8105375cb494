package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-22
 * @desc <p></p>
 */
@Data
@JsonPropertyOrder({"id", "parentId", "name", "code", "type", "uri", "rank",  "status", "children"})
public class ResourceVO implements Serializable {
    @NotNull(message = "资源ID不能为空", groups = ValidationGroup.GroupUpdate.class)
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "父ID", type = "Integer", example = "1")
    private Integer parentId;

    @NotBlank(message = "资源名称不能为空", groups = ValidationGroup.GroupCreate.class)
    @Schema(description = "资源名称", type = "String", example = "权限系统")
    private String name;

    @Schema(description = "资源编码", type = "String", example = "QXXT")
    private String code;

    @NotNull(message = "资源类型不能为空", groups = ValidationGroup.GroupCreate.class)
    @Schema(description = "资源类型 [1:系统, 2:菜单, 3:内部, 4:数据]", type = "Integer", example = "1")
    private Integer type;

    @Schema(description = "资源地址", type = "String", example = "/auth/add")
    private String uri;

    @Schema(description = "资源排序", type = "Integer", example = "1")
    private Integer rank;

    @Schema(description = "层级 (系统:1级)", type = "Integer", example = "1")
    private Integer level;

    @Schema(description = "资源状态 [false:禁用, true:启用]", type = "Boolean", example = "true")
    private Boolean status;

    @JsonIgnore
    @Schema(description = "删除标记 [false:未删除, true:已删除]", type = "Boolean", example = "true")
    private Boolean deleteFlag;

    @Schema(description = "平台字典", type = "String", example = "")
    private String platform;

    @Schema(description = "平台字典名称", type = "String", example = "")
    private String platformName;

    @JsonIgnore
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    @Schema(description = "创建时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date createTime;

    @JsonIgnore
    @Schema(description = "创建人", type = "Integer", example = "1")
    private Integer creator;

    @JsonIgnore
    @JsonFormat(pattern = SysConstant.DATE_TIME_FORMAT)
    @Schema(description = "更新时间", type = "Date", example = "2024-10-22 15:26:37")
    private Date updateTime;

    @JsonIgnore
    @Schema(description = "操作人", type = "Integer", example = "1")
    private Integer operator;

    @Schema(description = "是否选中[true:选中, false:未选中]", type = "Integer", example = "1")
    private Boolean checked;

    @Schema(description = "叶子节点数量", type = "Integer", example = "1")
    private Integer innerTotalCount;

    @Schema(description = "叶子节点选中数量", type = "Integer", example = "0")
    private Integer innerCheckedCount;

    @Schema(description = "已经选择的资源", type = "Integer", example = "0")
    private List<Integer> resourceIds;

    @Schema(description = "下级资源", type = "List")
    private List<ResourceVO> children;

    public void addChild(ResourceVO resource) {
        if (Objects.isNull(children)) {
            children = new ArrayList<>();
        }
        Optional.ofNullable(resource).ifPresent(item -> children.add(resource));
    }
}
