package com.coocaa.ad.cheese.authority;

import com.coocaa.ad.cheese.authority.bean.dict.DictSecondParam;
import com.coocaa.ad.cheese.authority.common.db.entity.DictEntity;
import com.coocaa.ad.cheese.authority.common.tools.utils.JWTUtil;
import com.coocaa.ad.cheese.authority.common.tools.utils.StringUtils;
import com.coocaa.ad.cheese.authority.service.SysDictService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@ActiveProfiles("local")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DictServiceTest {

    @Autowired
    private SysDictService sysDictService;


    /**
     * 01-99
     * B1-B9
     * M1-M9
     * */
    @Test
    public void addDict(){
        String parentCode = "77";
        for(int i=1;i<=99;i++){
            DictSecondParam dictSecondParam = new DictSecondParam();
            dictSecondParam.setName(StringUtils.file2Code(i));
            dictSecondParam.setParentCode(parentCode);
            sysDictService.addSecondLevel(dictSecondParam);
        }

        for(int i=1;i<=9;i++){
            DictSecondParam dictSecondParam = new DictSecondParam();
            dictSecondParam.setName("B"+i);
            dictSecondParam.setParentCode(parentCode);
            sysDictService.addSecondLevel(dictSecondParam);
        }

        for(int i=1;i<=9;i++){
            DictSecondParam dictSecondParam = new DictSecondParam();
            dictSecondParam.setName("M"+i);
            dictSecondParam.setParentCode(parentCode);
            sysDictService.addSecondLevel(dictSecondParam);
        }
    }

    @Test
    public void t(){
        System.out.println(JWTUtil.getExpireCommon("测试",0,360000000,"CC0000"));
    }
}
