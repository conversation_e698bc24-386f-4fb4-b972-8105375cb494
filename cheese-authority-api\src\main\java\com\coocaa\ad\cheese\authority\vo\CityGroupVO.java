package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/10/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CityGroupVO {
    private Integer id;
    private String name;
    private Integer parentId;
    private Integer rank;
    @Schema(description = "启用禁用状态")
    private Boolean enable;
}
