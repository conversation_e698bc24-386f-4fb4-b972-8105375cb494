package com.coocaa.ad.cheese.authority.common.db.service;

import com.coocaa.ad.cheese.authority.common.db.entity.CityGroupRelationEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 城市组包含的城市 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface ICityGroupRelationService extends IService<CityGroupRelationEntity> {

    List<CityGroupRelationEntity> listByGroupId(Integer id);

    void removeByGroupId(Integer id);

    List<CityGroupRelationEntity> listByGroupIds(List<Integer> ids);

    void removeByGroupIdAndCityId(List<Integer> groupIds, Set<Integer> setCityIds);
}
