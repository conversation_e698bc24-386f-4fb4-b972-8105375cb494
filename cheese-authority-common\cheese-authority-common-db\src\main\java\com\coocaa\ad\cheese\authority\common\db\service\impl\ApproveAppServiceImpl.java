package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.ApproveAppInfoEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.ApproveAppInfoMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IApproveAppInfoService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class ApproveAppServiceImpl extends ServiceImpl<ApproveAppInfoMapper, ApproveAppInfoEntity> implements IApproveAppInfoService {
}
