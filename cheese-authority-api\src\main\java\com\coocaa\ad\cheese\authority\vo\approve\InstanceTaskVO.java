package com.coocaa.ad.cheese.authority.vo.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-05-06
 */
@Data
public class InstanceTaskVO {

    @Schema(description = "审批人用户ID")
    private Integer userId;

    @Schema(description = "任务ID")
    private Integer taskId;

    /** 审批任务名称 */
    @Schema(description = "审批任务名称")
    private String approvalName;

    /**
     * 审批实例创建时间
     */
    @Schema(description = "审批实例创建时间")
    private LocalDateTime instanceCreateTime;

    /**
     * 审批实例创建人
     */
    @Schema(description = "审批实例创建人")
    private Integer instanceUserId;
}
