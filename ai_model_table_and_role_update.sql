-- AI模型表创建和角色表更新
-- 执行时间：2025-07-17

-- 1. 创建AI模型表
CREATE TABLE `ai_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_key` varchar(100) NOT NULL COMMENT '模型标识符（API调用时使用）',
  `provider` varchar(50) NOT NULL COMMENT '模型提供商',
  `model_type` varchar(20) NOT NULL DEFAULT 'chat' COMMENT '模型类型（chat-聊天模型，embedding-嵌入模型，image-图像模型等）',
  `description` text COMMENT '模型描述',
  `max_tokens` int(11) DEFAULT NULL COMMENT '最大上下文长度',
  `input_price` decimal(10,6) DEFAULT NULL COMMENT '输入价格（每1000tokens）',
  `output_price` decimal(10,6) DEFAULT NULL COMMENT '输出价格（每1000tokens）',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用（0-禁用，1-启用）',
  `sort` int(11) DEFAULT 0 COMMENT '排序值',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_key` (`model_key`),
  KEY `idx_provider` (`provider`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_sort` (`sort`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型表';

-- 2. 插入默认模型数据
INSERT INTO `ai_model` (`model_name`, `model_key`, `provider`, `model_type`, `description`, `max_tokens`, `input_price`, `output_price`, `enabled`, `sort`) VALUES 
('豆包-4o', 'doubao-pro-4o', 'ByteDance', 'chat', '字节跳动豆包大模型，支持多轮对话', 32768, 0.0008, 0.002, 1, 1),
('豆包-32k', 'doubao-pro-32k', 'ByteDance', 'chat', '字节跳动豆包大模型，32k上下文', 32768, 0.0005, 0.001, 1, 2),
('豆包-128k', 'doubao-pro-128k', 'ByteDance', 'chat', '字节跳动豆包大模型，128k上下文', 131072, 0.001, 0.002, 1, 3),
('GPT-4', 'gpt-4', 'OpenAI', 'chat', 'OpenAI GPT-4 模型', 8192, 0.03, 0.06, 0, 10),
('GPT-3.5 Turbo', 'gpt-3.5-turbo', 'OpenAI', 'chat', 'OpenAI GPT-3.5 Turbo 模型', 4096, 0.0015, 0.002, 0, 11),
('Claude-3', 'claude-3-sonnet', 'Anthropic', 'chat', 'Anthropic Claude-3 Sonnet 模型', 200000, 0.003, 0.015, 0, 20),
('文心一言', 'ernie-bot-4', 'Baidu', 'chat', '百度文心一言大模型', 8192, 0.002, 0.004, 0, 30),
('通义千问', 'qwen-max', 'Alibaba', 'chat', '阿里巴巴通义千问大模型', 8192, 0.002, 0.006, 0, 40);

-- 3. 为ai_role表添加model_id字段
ALTER TABLE `ai_role` 
ADD COLUMN `model_id` bigint(20) DEFAULT NULL COMMENT '模型编号，关联ai_model表的id字段' AFTER `role_prompt`;

-- 4. 创建模型ID索引
CREATE INDEX `idx_model_id` ON `ai_role` (`model_id`);

-- 5. 添加外键约束（可选，根据需要决定是否添加）
-- ALTER TABLE `ai_role` ADD CONSTRAINT `fk_role_model` FOREIGN KEY (`model_id`) REFERENCES `ai_model` (`id`);

-- 6. 更新现有角色数据，设置默认模型（可选）
-- 将所有现有角色设置为使用豆包-4o模型
UPDATE `ai_role` SET `model_id` = 1 WHERE `model_id` IS NULL AND `delete_flag` = 0;

-- 7. 查看表结构（验证字段是否添加成功）
DESC `ai_model`;
DESC `ai_role`;

-- 表结构说明：

-- ai_model表：
-- id: 主键ID，自增
-- model_name: 模型名称，如"豆包-4o"、"GPT-4"等
-- model_key: 模型标识符，API调用时使用，如"doubao-pro-4o"
-- provider: 模型提供商，如"ByteDance"、"OpenAI"等
-- model_type: 模型类型，chat-聊天模型，embedding-嵌入模型等
-- description: 模型描述
-- max_tokens: 最大上下文长度
-- input_price: 输入价格（每1000tokens）
-- output_price: 输出价格（每1000tokens）
-- enabled: 是否启用，0-禁用，1-启用
-- sort: 排序值，用于前端显示排序
-- create_time: 创建时间，自动填充
-- creator: 创建人ID，默认为0
-- update_time: 更新时间，自动更新
-- operator: 更新人ID，默认为0
-- delete_flag: 软删除标记，0-正常，1-已删除

-- ai_role表新增字段：
-- model_id: 模型编号，关联ai_model表的id字段

-- 索引说明：
-- uk_model_key: model_key唯一索引，确保模型标识符唯一
-- idx_provider: 提供商索引，用于按提供商查询
-- idx_model_type: 模型类型索引，用于按类型查询
-- idx_enabled: 启用状态索引，用于过滤启用的模型
-- idx_sort: 排序索引，用于排序查询
-- idx_delete_flag: 删除标记索引，用于过滤已删除记录
-- idx_model_id: 角色表的模型ID索引

-- 使用场景：
-- 1. 创建角色时选择关联的模型
-- 2. 根据模型类型筛选可用模型
-- 3. 模型价格计算和统计
-- 4. 模型性能监控和管理

-- 查询示例：
-- 1. 查询所有可用的聊天模型：
-- SELECT * FROM ai_model WHERE model_type = 'chat' AND enabled = 1 AND delete_flag = 0 ORDER BY sort, id;

-- 2. 查询角色及其关联的模型：
-- SELECT r.role_name, r.role_prompt, m.model_name, m.provider 
-- FROM ai_role r 
-- LEFT JOIN ai_model m ON r.model_id = m.id 
-- WHERE r.delete_flag = 0 AND (m.delete_flag = 0 OR m.id IS NULL);

-- 3. 查询某个提供商的所有模型：
-- SELECT * FROM ai_model WHERE provider = 'ByteDance' AND delete_flag = 0 ORDER BY sort;

-- 4. 统计各模型的使用情况：
-- SELECT m.model_name, COUNT(r.id) as role_count 
-- FROM ai_model m 
-- LEFT JOIN ai_role r ON m.id = r.model_id AND r.delete_flag = 0 
-- WHERE m.delete_flag = 0 
-- GROUP BY m.id, m.model_name 
-- ORDER BY role_count DESC;
