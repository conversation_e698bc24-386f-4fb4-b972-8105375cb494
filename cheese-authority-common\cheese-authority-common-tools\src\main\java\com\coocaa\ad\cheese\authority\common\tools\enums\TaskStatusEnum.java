package com.coocaa.ad.cheese.authority.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 下载中心-任务状态
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-19
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum implements IEnumType<String> {
    /**
     * 待执行
     */
    PENDING("0157-1", "待执行"),

    /**
     * 执行中
     */
    EXECUTING("0157-2", "执行中"),

    /**
     * 已完成
     */
    COMPLETED("0157-3", "已完成"),

    /**
     * 执行失败
     */
    FAILED("0157-4", "执行失败"),

    /**
     * 已下载
     */
    DOWNLOADED("0157-5", "已下载");

    private final String code;
    private final String desc;

    /**
     * 将代码转成枚举
     */
    public static TaskStatusEnum parse(String code) {
        return parse(code, null);
    }

    /**
     * 将代码转成枚举
     */
    public static TaskStatusEnum parse(String code, TaskStatusEnum defaultValue) {
        for (TaskStatusEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return defaultValue;
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code) {
        return getDesc(code, null);
    }

    /**
     * 根据代码获取描述
     */
    public static String getDesc(String code, String defaultValue) {
        TaskStatusEnum enumValue = parse(code);
        if (enumValue != null) {
            return enumValue.getDesc();
        }
        return StringUtils.isBlank(defaultValue) ? StringUtils.EMPTY : defaultValue;
    }
} 