<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.authority.common.db.mapper.CityMapper">

    <select id="getDetailByNames" resultType="com.coocaa.ad.cheese.authority.common.db.bean.CityDetailVO">
        SELECT
        a.id AS cityId,
        a.NAME AS cityName,
        b.id AS districtId,
        b.NAME AS districtName
        FROM
        city a
        LEFT JOIN city b ON b.parent_id = a.id
        WHERE
        b.`level` = 3
        <if test="params != null and params.size > 0">
            <foreach collection="params" item="param" open="AND (" separator=" OR " close=")">
                (a.NAME LIKE CONCAT(#{param.city}, '%')
                AND b.NAME LIKE CONCAT(#{param.district}, '%'))
            </foreach>
        </if>
    </select>
    <select id="getProvinceCity"
            resultType="com.coocaa.ad.cheese.authority.common.db.bean.ProvinceCityDistrictSelectVO">
        SELECT
        d.id AS district,
        d.NAME AS districtName,
        c.id AS city,
        c.NAME AS cityName,
        p.id AS province,
        p.NAME AS provinceName
        FROM
        city d
        INNER JOIN city c ON d.parent_id = c.id
        INNER JOIN city p ON c.parent_id = p.id
        WHERE
        d.STATUS = 1
        AND c.STATUS = 1
        AND p.STATUS = 1
        AND d.id IN
        <foreach collection="districts" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>
