-- AI角色表DDL
-- 用于存储AI聊天角色信息

CREATE TABLE `ai_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `role_prompt` text COMMENT '角色提示词',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `creator` int(11) DEFAULT 0 COMMENT '创建人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` int(11) DEFAULT 0 COMMENT '更新人',
  `delete_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记（0-正常，1-删除）',
  PRIMARY KEY (`id`),
  KEY `idx_role_name` (`role_name`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_delete_flag` (`delete_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色表';

-- 插入默认角色数据
INSERT INTO `ai_role` (`role_name`, `role_prompt`) VALUES 
('默认助手', '你是一个有用的AI助手，请友好、准确地回答用户的问题。'),
('编程助手', '你是一个专业的编程助手，擅长各种编程语言和技术问题。请提供准确、实用的编程建议和代码示例。'),
('写作助手', '你是一个专业的写作助手，擅长各种文体的写作。请帮助用户改进文章结构、语言表达和内容质量。'),
('翻译助手', '你是一个专业的翻译助手，精通多种语言。请提供准确、自然的翻译，并注意语境和文化差异。'),
('学习导师', '你是一个耐心的学习导师，擅长解释复杂概念。请用简单易懂的方式回答问题，并提供学习建议。');

-- 表结构说明：
-- id: 主键ID，自增
-- role_name: 角色名称，如"编程助手"、"写作助手"等
-- role_prompt: 角色提示词，定义角色的行为和特点
-- create_time: 创建时间，自动填充
-- creator: 创建人ID，默认为0
-- update_time: 更新时间，自动更新
-- operator: 更新人ID，默认为0
-- delete_flag: 软删除标记，0-正常，1-已删除

-- 索引说明：
-- idx_role_name: 角色名称索引，用于按名称查询
-- idx_create_time: 创建时间索引，用于时间范围查询
-- idx_delete_flag: 删除标记索引，用于过滤已删除记录

-- 使用场景：
-- 1. 创建对话时选择角色
-- 2. 发送消息时应用角色提示词
-- 3. 角色管理和配置

-- 查询示例：
-- 1. 查询所有可用角色：
-- SELECT * FROM ai_role WHERE delete_flag = 0 ORDER BY create_time DESC;

-- 2. 根据角色名称查询：
-- SELECT * FROM ai_role WHERE role_name LIKE '%助手%' AND delete_flag = 0;

-- 3. 根据ID查询角色：
-- SELECT * FROM ai_role WHERE id = ? AND delete_flag = 0;
