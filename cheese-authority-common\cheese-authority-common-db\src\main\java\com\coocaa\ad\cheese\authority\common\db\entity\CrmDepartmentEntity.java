package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * crm部门信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("crm_department")
public class CrmDepartmentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 上级部门ID，0为最上级
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门负责人
     */
    private Long ownerUserId;

    /**
     * 部门类型 1 公司 2 部门
     */
    private Integer type;

    /**
     * 加密KEY，查询其他企业部门时使用
     */
    private String secureKey;

    /**
     * 当前部门在职人数
     */
    private Integer currentNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;


}
