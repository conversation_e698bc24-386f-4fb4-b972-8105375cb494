package com.coocaa.ad.cheese.authority.common.db.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.DepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.DepartmentMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IDepartmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 部门信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, DepartmentEntity> implements IDepartmentService {

   }
