package com.coocaa.ad.cheese.authority.vo.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
public class InternalPersonRuleVO {

    @Schema(description = "规则ID")
    private Integer id;

    @Schema(description = "规则名称")
    private String name;

    @Schema(description = "规则审批人员")
    private List<InternalPersonVO> persons = Collections.emptyList();
}
