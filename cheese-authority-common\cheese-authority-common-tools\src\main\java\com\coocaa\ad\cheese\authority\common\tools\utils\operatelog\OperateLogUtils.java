package com.coocaa.ad.cheese.authority.common.tools.utils.operatelog;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.function.BiConsumer;

/**
 * 操作日志工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-04
 */
@Slf4j
@Component
public final class OperateLogUtils implements InitializingBean, ApplicationContextAware {
    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();
    private static KafkaTemplate<String,String> kafkaTemplate;
    private static final String topicName = "cheese-operate-log";

    private ApplicationContext applicationContext;
    @Override
    public void afterPropertiesSet() throws Exception {
        this.kafkaTemplate = applicationContext.getBean(KafkaTemplate.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext =applicationContext;
    }

    /**
     * 记录操作日志
     *
     * @param operateLog 操作日志
     * @return true:调用成功
     */
    public static boolean log(IOperateLog operateLog) {
        return log(operateLog, null);
    }

    /**
     * 记录操作日志
     *
     * @param operateLog 操作日志
     * @param callback   回调函数
     * @return true:调用成功
     */
    public static boolean log(IOperateLog operateLog, BiConsumer<RecordMetadata, Throwable> callback) {
        if (Objects.isNull(operateLog) || !isInvalid(operateLog)) {
            return false;
        }
        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(topicName, operateLog.getSystemCode(), JSON_MAPPER.writeValueAsString(operateLog));

            log.info("记录权限系统操作日志: {}", record.value());
            if (Objects.isNull(callback)) {
                kafkaTemplate.send(record);
            } else {

                kafkaTemplate.send(record)
                        .whenComplete((sendResult, throwable) -> callback.accept(sendResult.getRecordMetadata(), throwable));
            }

            return true;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return false;
        }
    }


    private OperateLogUtils() {
    }


    /**
     * 检查操作日志是否有效
     */
    private static boolean isInvalid(IOperateLog bizLog) {
        if (StringUtils.isBlank(bizLog.getSystemCode())
                || StringUtils.isBlank(bizLog.getFunctionName())
                || StringUtils.isBlank(bizLog.getEntityCode())
                || Objects.isNull(bizLog.getEntityId())) {
            log.warn("[操作日志] - 系统名称/功能名称/实体类型/实体ID 不能为空！ 详情: {}", bizLog);
            return false;
        }

        return true;
    }


}
