package com.coocaa.ad.cheese.authority.common.db.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.CityGroupEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserCityGroupEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.UserCityGroupMapper;
import com.coocaa.ad.cheese.authority.common.db.service.ICityGroupService;
import com.coocaa.ad.cheese.authority.common.db.service.IUserCityGroupService;
import com.coocaa.ad.cheese.authority.common.tools.constant.SysConstant;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户城市 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class UserCityGroupServiceImpl extends ServiceImpl<UserCityGroupMapper, UserCityGroupEntity> implements IUserCityGroupService {
    private final ICityGroupService cityGroupService;

    @Override
    public Map<Integer, List<CityGroupEntity>> listUserValidCityGroups(Collection<Integer> userIds) {
        Objects.requireNonNull(userIds, "用户ID不能为空");

        // 查询用户的城市组
        List<UserCityGroupEntity> userCityGroups = lambdaQuery()
                .select(UserCityGroupEntity::getUserId, UserCityGroupEntity::getCityGroupId)
                .in(UserCityGroupEntity::getUserId, userIds).list();
        if (CollectionUtil.isEmpty(userCityGroups)) {
            return Collections.emptyMap();
        }

        // 提取用户城市组对应的城市组ID
        Set<Integer> cityGroupIds = Sets.newHashSetWithExpectedSize(userCityGroups.size());
        Map<Integer, Set<Integer>> userCityGroupIdMap = userCityGroups.stream()
                .filter(item -> Objects.nonNull(item.getCityGroupId()))
                .peek(item -> cityGroupIds.add(item.getCityGroupId()))
                .collect(Collectors.groupingBy(UserCityGroupEntity::getUserId,
                        Collectors.mapping(UserCityGroupEntity::getCityGroupId, Collectors.toSet())));

        // 查询有效的城市组
        cityGroupIds.remove(SysConstant.DEFAULT_ALL_ID);
        List<CityGroupEntity> cityGroups = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(cityGroupIds)) {
            cityGroups = cityGroupService.list(Wrappers.lambdaQuery(CityGroupEntity.class)
                    .select(CityGroupEntity::getId, CityGroupEntity::getName)
                    .in(CityGroupEntity::getId, cityGroupIds)
                    .eq(CityGroupEntity::getStatus, Boolean.TRUE)
            );
        }

        // 组装数据
        Map<Integer, CityGroupEntity> cityGroupMap = CollectionUtils.isEmpty(cityGroups)
                ? Collections.emptyMap()
                : cityGroups.stream().collect(Collectors.toMap(CityGroupEntity::getId, Function.identity()));

        Map<Integer, List<CityGroupEntity>> userCityGroupMap = Maps.newHashMapWithExpectedSize(userIds.size());
        userCityGroupIdMap.forEach((userId, ids) -> {
            if (ids.contains(SysConstant.DEFAULT_ALL_ID)) {
                userCityGroupMap.put(userId, Collections.singletonList(getDefaultCityGroup()));
            } else {
                userCityGroupMap.put(userId, ids.stream()
                        .map(cityGroupMap::get)
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(CityGroupEntity::getRank, Comparator.nullsFirst(Integer::compareTo)))
                        .toList());
            }
        });
        return userCityGroupMap;
    }

    @Override
    public List<UserCityGroupEntity> listByUserId(Integer userId) {
        return lambdaQuery().eq(UserCityGroupEntity::getUserId, userId).list();
    }

    /**
     * 获取默认城市组
     */
    private CityGroupEntity getDefaultCityGroup() {
        CityGroupEntity cityGroup = new CityGroupEntity();
        cityGroup.setId(SysConstant.DEFAULT_ALL_ID);
        cityGroup.setRank(0);
        cityGroup.setName("全部");
        return cityGroup;
    }
}
