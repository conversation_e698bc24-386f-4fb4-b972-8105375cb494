# AI向量存储配置示例
# 参考yudao-module-ai-server的配置结构

# AI向量存储配置
ai:
  vector-store:
    # 向量存储类型：redis, milvus, qdrant, simple
    type: redis
    
    # Redis向量存储配置
    redis:
      # 索引名称
      index-name: ai_knowledge_index
      # 键前缀
      prefix: "ai:vector:"
      # 是否初始化Schema
      initialize-schema: true
      # 向量维度
      dimension: 1536
      # 距离算法：COSINE, L2, IP
      distance-metric: COSINE
    
    # 嵌入模型配置
    embedding:
      # 嵌入模型平台：openai, huggingface, local
      platform: openai
      # 模型名称
      model: text-embedding-ada-002
      # API密钥（从环境变量获取）
      api-key: ${AI_EMBEDDING_API_KEY:}
      # API地址
      api-url: ${AI_EMBEDDING_API_URL:https://api.openai.com}
      # 向量维度
      dimension: 1536
      # 批处理大小
      batch-size: 100
    
    # 元数据字段配置
    metadata-fields:
      knowledgeId: NUMERIC
      documentId: NUMERIC
      segmentId: NUMERIC
      documentName: TEXT
      fileType: TAG
      wordCount: NUMERIC
      position: NUMERIC
      createTime: NUMERIC

# Redis配置
spring:
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DATABASE:0}
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

# 如果使用Spring AI Redis Vector Store
spring:
  ai:
    vectorstore:
      redis:
        # 索引名称
        index-name: ai_knowledge_index
        # 键前缀
        prefix: "ai:vector:"
        # 是否初始化Schema
        initialize-schema: true

# 文档处理配置
document:
  processing:
    # 支持的文件类型
    supported-types:
      - txt
      - md
      - pdf
      - doc
      - docx
      - csv
      - json
      - xml
      - html
    
    # 最大文件大小（字节）
    max-file-size: 10485760  # 10MB
    
    # 分块配置
    chunking:
      # 默认分块大小
      default-chunk-size: 1000
      # 默认重叠大小
      default-overlap: 200
      # 最小分块大小
      min-chunk-size: 100
      # 最大分块大小
      max-chunk-size: 2000

# 知识库配置
knowledge:
  # 默认知识库设置
  defaults:
    # 嵌入模型
    embedding-model: text-embedding-ada-002
    # 向量维度
    vector-dimension: 1536
    # 分块大小
    chunk-size: 1000
    # 分块重叠
    chunk-overlap: 200
  
  # 搜索配置
  search:
    # 默认返回结果数量
    default-top-k: 5
    # 默认相似度阈值
    default-threshold: 0.7
    # 最大返回结果数量
    max-top-k: 20
    # 搜索超时时间（毫秒）
    timeout: 5000

# 日志配置
logging:
  level:
    com.coocaa.ad.cheese.ai: DEBUG
    org.springframework.ai: INFO
    redis.clients.jedis: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 环境变量说明：
# AI_EMBEDDING_API_KEY: 嵌入模型API密钥
# AI_EMBEDDING_API_URL: 嵌入模型API地址
# REDIS_HOST: Redis主机地址
# REDIS_PORT: Redis端口
# REDIS_PASSWORD: Redis密码
# REDIS_DATABASE: Redis数据库编号

# 使用示例：
# 1. 设置环境变量
# export AI_EMBEDDING_API_KEY="your-openai-api-key"
# export REDIS_HOST="localhost"
# export REDIS_PORT="6379"

# 2. 启动应用
# java -jar your-app.jar --spring.config.location=ai-vector-store-config.yml

# 3. 测试配置
# curl -X POST http://localhost:8080/sys/ai/knowledge/upload/text \
#   -H "Content-Type: application/json" \
#   -d '{
#     "knowledgeId": 1,
#     "name": "测试文档",
#     "content": "这是一个测试文档内容",
#     "processImmediately": true
#   }'

# 4. 搜索测试
# curl -X POST http://localhost:8080/sys/ai/knowledge/search \
#   -H "Content-Type: application/json" \
#   -d '{
#     "knowledgeId": 1,
#     "query": "测试",
#     "topK": 5,
#     "threshold": 0.7
#   }'
