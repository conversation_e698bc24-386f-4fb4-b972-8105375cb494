<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coocaa.ad.cheese.ai.common.db.mapper.AiChatMapper">
    <!-- 按条件查询列表 -->
    <select id="pageList" resultType="com.coocaa.ad.cheese.ai.common.db.entity.AiChatEntity">
        SELECT ai.* 
        FROM ai_chat ai
        <where>
            AND ai.delete_flag = 0
        </where>
        ORDER BY ai.update_time DESC
    </select>
</mapper>