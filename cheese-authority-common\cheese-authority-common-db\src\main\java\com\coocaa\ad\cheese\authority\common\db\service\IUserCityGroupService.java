package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.CityGroupEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserCityGroupEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户城市 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IUserCityGroupService extends IService<UserCityGroupEntity> {

    /**
     * 查询用户城市组信息
     *
     * @param userIds 用户IDs
     * @return 用户对应的城市组信息
     */
    Map<Integer, List<CityGroupEntity>> listUserValidCityGroups(Collection<Integer> userIds);

    List<UserCityGroupEntity> listByUserId(Integer userId);
}
