package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiModelEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiModelService;
import com.coocaa.ad.cheese.ai.convert.AiModelConvert;
import com.coocaa.ad.cheese.ai.vo.AiModelVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI模型业务服务
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiModelService {

    private final IAiModelService aiModelService;

    /**
     * 获取所有可用模型列表
     *
     * @return 模型列表
     */
    public List<AiModelVO> getAllModels() {
        log.info("获取所有可用模型列表");
        
        LambdaQueryWrapper<AiModelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiModelEntity::getDeleteFlag, 0)
               .eq(AiModelEntity::getEnabled, 1)
               .orderByAsc(AiModelEntity::getSort)
               .orderByAsc(AiModelEntity::getId);
        
        List<AiModelEntity> entities = aiModelService.list(wrapper);
        List<AiModelVO> models = entities.stream()
                .map(AiModelConvert.INSTANCE::toVo)
                .collect(Collectors.toList());
        
        log.info("获取模型列表成功，共{}个模型", models.size());
        return models;
    }

    /**
     * 根据ID获取模型
     *
     * @param modelId 模型ID
     * @return 模型信息，如果不存在返回null
     */
    public AiModelVO getModelById(Long modelId) {
        if (modelId == null) {
            return null;
        }
        
        log.info("根据ID获取模型，模型ID: {}", modelId);
        
        AiModelEntity entity = aiModelService.getById(modelId);
        if (entity == null || entity.getDeleteFlag() == 1 || entity.getEnabled() == 0) {
            log.warn("模型不存在、已删除或已禁用，模型ID: {}", modelId);
            return null;
        }
        
        AiModelVO model = AiModelConvert.INSTANCE.toVo(entity);
        log.info("获取模型成功，模型ID: {}, 模型名称: {}", modelId, model.getModelName());
        return model;
    }

    /**
     * 根据模型标识符获取模型
     *
     * @param modelKey 模型标识符
     * @return 模型信息，如果不存在返回null
     */
    public AiModelVO getModelByKey(String modelKey) {
        if (modelKey == null || modelKey.trim().isEmpty()) {
            return null;
        }
        
        log.info("根据标识符获取模型，模型标识符: {}", modelKey);
        
        LambdaQueryWrapper<AiModelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiModelEntity::getModelKey, modelKey)
               .eq(AiModelEntity::getDeleteFlag, 0)
               .eq(AiModelEntity::getEnabled, 1);
        
        AiModelEntity entity = aiModelService.getOne(wrapper);
        if (entity == null) {
            log.warn("模型不存在或已禁用，模型标识符: {}", modelKey);
            return null;
        }
        
        AiModelVO model = AiModelConvert.INSTANCE.toVo(entity);
        log.info("获取模型成功，模型标识符: {}, 模型名称: {}", modelKey, model.getModelName());
        return model;
    }

    /**
     * 根据类型获取模型列表
     *
     * @param modelType 模型类型
     * @return 模型列表
     */
    public List<AiModelVO> getModelsByType(String modelType) {
        log.info("根据类型获取模型列表，模型类型: {}", modelType);
        
        LambdaQueryWrapper<AiModelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiModelEntity::getModelType, modelType)
               .eq(AiModelEntity::getDeleteFlag, 0)
               .eq(AiModelEntity::getEnabled, 1)
               .orderByAsc(AiModelEntity::getSort)
               .orderByAsc(AiModelEntity::getId);
        
        List<AiModelEntity> entities = aiModelService.list(wrapper);
        List<AiModelVO> models = entities.stream()
                .map(AiModelConvert.INSTANCE::toVo)
                .collect(Collectors.toList());
        
        log.info("获取{}类型模型列表成功，共{}个模型", modelType, models.size());
        return models;
    }
}
