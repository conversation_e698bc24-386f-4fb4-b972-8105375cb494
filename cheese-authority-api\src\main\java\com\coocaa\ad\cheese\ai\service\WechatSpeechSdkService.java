package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.config.WechatMiniProgramConfig;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;
import me.chanjar.weixin.mp.enums.AiLangType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

/**
 * 微信语音识别服务 - 使用官方SDK实现
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WechatSpeechSdkService {

    private final WechatMiniProgramConfig config;
    private WxMpService wxMpService;

    @PostConstruct
    public void init() {
        if (config.getAppId() == null || config.getAppId().trim().isEmpty()) {
            log.warn("微信小程序AppID未配置，请设置wechat.mini.app-id配置项");
            return;
        }

        if (config.getAppSecret() == null || config.getAppSecret().trim().isEmpty()) {
            log.warn("微信小程序AppSecret未配置，请设置wechat.mini.app-secret配置项");
            return;
        }

        // 初始化微信MP服务
        WxMpDefaultConfigImpl wxMpConfig = new WxMpDefaultConfigImpl();
        wxMpConfig.setAppId(config.getAppId());
        wxMpConfig.setSecret(config.getAppSecret());

        this.wxMpService = new WxMpServiceImpl();
        this.wxMpService.setWxMpConfigStorage(wxMpConfig);

        log.info("微信小程序语音识别SDK服务初始化完成，AppID: {}", config.getAppId());
    }

    /**
     * 语音识别（使用官方SDK）
     *
     * @param file 语音文件
     * @return 识别结果
     * @throws Exception 识别失败时抛出异常
     */
    public String recognizeSpeech(MultipartFile file) throws Exception {
        if (wxMpService == null) {
            throw new RuntimeException("微信小程序语音识别SDK未初始化，请检查AppID和AppSecret配置");
        }

        // 验证文件
        if (file.isEmpty()) {
            throw new RuntimeException("上传的文件为空");
        }

        long fileSize = file.getSize();
        log.info("准备识别语音文件: {}, 大小: {} bytes ({} MB)",
                file.getOriginalFilename(), fileSize, fileSize / 1024.0 / 1024.0);

        if (fileSize > config.getMaxFileSize()) {
            throw new RuntimeException(String.format("文件过大，超过%.1fMB限制",
                    config.getMaxFileSize() / 1024.0 / 1024.0));
        }

        // 创建临时文件（SDK需要File对象）
        File tempFile = createTempFile(file);

        try {
            return recognizeSpeechWithRetry(tempFile);
        } finally {
            // 清理临时文件
            cleanupTempFile(tempFile);
        }
    }

    /**
     * 带重试的语音识别
     *
     * @param voiceFile 语音文件
     * @return 识别结果
     * @throws Exception 识别失败时抛出异常
     */
    private String recognizeSpeechWithRetry(File voiceFile) throws Exception {
        Exception lastException = null;

        for (int i = 0; i < config.getMaxRetries(); i++) {
            try {
                return performSpeechRecognition(voiceFile);
            } catch (Exception e) {
                lastException = e;
                log.warn("语音识别失败，剩余重试次数: {}", config.getMaxRetries() - i - 1, e);

                if (i < config.getMaxRetries() - 1) {
                    try {
                        Thread.sleep(config.getRetryInterval());
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("重试被中断", ie);
                    }
                }
            }
        }

        throw new RuntimeException("语音识别失败，已达到最大重试次数", lastException);
    }

    /**
     * 执行语音识别（使用官方SDK）
     *
     * @param voiceFile 语音文件
     * @return 识别结果
     * @throws Exception 识别失败时抛出异常
     */
    private String performSpeechRecognition(File voiceFile) throws Exception {
        String voiceId = String.valueOf(System.currentTimeMillis());
        AiLangType langType = "zh_CN".equals(config.getLanguage()) ? AiLangType.zh_CN : AiLangType.en_US;

        log.info("使用微信SDK进行语音识别，voiceId: {}, 语言: {}", voiceId, langType);

        try {
            // 使用SDK的一体化方法：上传 + 查询结果
            String result = wxMpService.getAiOpenService().recogniseVoice(voiceId, langType, voiceFile);

            if (result == null || result.trim().isEmpty()) {
                throw new RuntimeException("语音识别结果为空");
            }

            log.info("语音识别成功，结果: {}", result);
            return result;

        } catch (WxErrorException e) {
            log.error("微信SDK语音识别失败，错误码: {}, 错误信息: {}", e.getError().getErrorCode(), e.getError().getErrorMsg());
            throw new RuntimeException("微信语音识别失败: " + e.getError().getErrorMsg(), e);
        } catch (Exception e) {
            log.error("语音识别过程中发生错误", e);
            throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建临时文件
     *
     * @param file 上传的文件
     * @return 临时文件
     * @throws IOException 创建失败时抛出异常
     */
    private File createTempFile(MultipartFile file) throws IOException {
        String originalFilename = file.getOriginalFilename();
        String extension = originalFilename != null && originalFilename.contains(".")
                ? originalFilename.substring(originalFilename.lastIndexOf("."))
                : ".mp3";

        Path tempFile = Files.createTempFile("wechat-speech-", extension);
        Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

        log.info("创建临时文件: {}", tempFile);
        return tempFile.toFile();
    }

    /**
     * 清理临时文件
     *
     * @param tempFile 临时文件
     */
    private void cleanupTempFile(File tempFile) {
        if (tempFile != null && tempFile.exists()) {
            try {
                Files.deleteIfExists(tempFile.toPath());
                log.info("临时文件已清理: {}", tempFile.getPath());
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", tempFile.getPath(), e);
            }
        }
    }

}
