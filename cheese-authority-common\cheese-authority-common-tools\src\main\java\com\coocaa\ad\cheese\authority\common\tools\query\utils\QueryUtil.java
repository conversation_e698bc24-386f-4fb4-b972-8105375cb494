package com.coocaa.ad.cheese.authority.common.tools.query.utils;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coocaa.ad.cheese.authority.common.tools.query.enums.DeletedStatus;
import com.coocaa.ad.cheese.authority.common.tools.query.enums.ICondition;
import com.coocaa.ad.cheese.authority.common.tools.query.annotation.Column;
import com.coocaa.ad.cheese.authority.common.tools.query.enums.FilterType;
import com.coocaa.ad.cheese.authority.common.tools.query.enums.OrderType;
import com.coocaa.ad.cheese.authority.common.tools.query.exception.CommonException;
import com.coocaa.ad.cheese.authority.common.tools.query.exception.ErrorCode;
import com.coocaa.ad.cheese.authority.common.tools.query.params.IFilter;
import com.coocaa.ad.cheese.authority.common.tools.query.params.QueryOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 类QueryUtil实现描述:
 *
 * <AUTHOR>
 * @date 2022年05月08 13:04
 */
public class QueryUtil<T> {

    /**
     * 根据前端过滤条件转换成mybatis-plus查询条件
     *
     * @param query 　查询条件
     * @param c     泛型
     * @return 返回QueryWrapper
     */
    public QueryWrapper<T> getQueryWrapper(QueryOptions query, Class<?> c) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (query != null) {
            //指定显示字段
            if (query.getColumns() != null && query.getColumns().length > 0) {
                int columnSize = query.getColumns().length;
                String[] columns = new String[columnSize];
                for (int i = 0; i < columnSize; i++) {
                    columns[i] = getFieldName(query.getColumns()[i], c);
                }
                queryWrapper.select(columns);
            }
            //处理查询条件
            List<IFilter> filters = Optional.ofNullable(query.getFilters()).orElse(new ArrayList<>(16));
            for (int i = 0, size = filters.size(); i < size; i++) {
                IFilter filers = query.getFilters().get(i);
                //默认条件组合为AND
                if (filers.getFilterType() != null && filers.getFilterType().equals(FilterType.OR)) {
                    queryWrapper.or();
                }
                //字段名
                String fieldName = getFieldName(filers.getFilterName(), c);
                if (fieldName == null) {
                    continue;
                }
                //字段值
                Object filterValue;
                switch (filers.getCondition()) {
                    //等于
                    case EQUAL:
                        //获取值
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.eq(fieldName, filterValue);
                        break;
                    //不等于
                    case UN_EQUAL:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.ne(fieldName, filterValue);
                        break;
                    //关联
                    case IN:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue();
                        List<Object> list = filterValue == null ? new ArrayList<>() : JSONUtil.parseArray(filterValue);
                        queryWrapper.in(fieldName, list);
                        break;
                    //不关联
                    case NO_IN:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue();
                        List<Object> objectList = filterValue == null ? new ArrayList<>() : JSONUtil.parseArray(filterValue);
                        queryWrapper.notIn(fieldName, objectList);
                        break;
                    //模糊匹配
                    case LIKE:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.like(fieldName, filterValue);
                        break;
                    //模糊不匹配
                    case NO_LIKE:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.notLike(fieldName, filterValue);
                        break;
                    //大于等于
                    case GE:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.ge(fieldName, filterValue);
                        break;
                    //小于等于
                    case LE:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.le(fieldName, filterValue);
                        break;
                    //大于
                    case GT:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.gt(fieldName, filterValue);
                        break;
                    //小于
                    case LT:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.lt(fieldName, filterValue);
                        break;
                    //为空
                    case NULL:
                        queryWrapper.isNull(fieldName);
                        break;
                    //不为空
                    case NOT_NULL:
                        queryWrapper.isNotNull(fieldName);
                        break;
                    //两者之间
                    case BETWEEN:
                        if (filers.getFilterValue() == null || filers.getFilterValue().size() < 2) {
                            throw new CommonException(ErrorCode.PARAMS_GET_ERROR, "当前条件(" + ICondition.BETWEEN + ") 需两个参数值");
                        }
                        Object start = filers.getFilterValue().get(0);
                        Object end = filers.getFilterValue().get(1);
                        queryWrapper.between(fieldName, start, end);
                        break;
                    //非两者之间
                    case NOT_BETWEEN:
                        if (filers.getFilterValue() == null || filers.getFilterValue().size() < 2) {
                            throw new CommonException(ErrorCode.PARAMS_GET_ERROR, "当前条件(" + ICondition.BETWEEN + ") 需两个参数值");
                        }
                        Object start2 = filers.getFilterValue().get(0);
                        Object end2 = filers.getFilterValue().get(1);
                        queryWrapper.notBetween(fieldName, start2, end2);
                        break;
                    //左模糊匹配
                    case LEFT_LIKE:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.likeLeft(fieldName, filterValue);
                        break;
                    //右模糊匹配
                    case RIGHT_LIKE:
                        filterValue = Optional.ofNullable(filers.getFilterValue()).isEmpty() ? null : filers.getFilterValue().get(0);
                        queryWrapper.likeRight(fieldName, filterValue);
                        break;
                    default:
                }
            }
            //处理排序条件
            if (CollectionUtils.isNotEmpty(query.getOrderBys())) {
                List<String> ascList = new ArrayList<>();
                List<String> descList = new ArrayList<>();
                query.getOrderBys().forEach(orderBy -> {
                    //字段名
                    String fieldName = getFieldName(orderBy.getOrderByName(), c);
                    if (fieldName != null) {
                        if (orderBy.getOrderByType().equals(OrderType.ASC)) {
                            ascList.add(fieldName);
                        } else {
                            descList.add(fieldName);
                        }
                    }
                });
                queryWrapper.orderByAsc(ascList);
                queryWrapper.orderByDesc(descList);
            }
        }
        //针对删除标识的字段，自动过滤
        Field[] fields = ReflectUtil.getFields(c);
        Arrays.stream(fields).forEach(f -> {
            Column column = f.getAnnotation(Column.class);
            if (column != null && column.DeleteFlag()) {
                queryWrapper.ne(HumpLineUtil.humpToLine(f.getName()), DeletedStatus.DELETED.key);
            }
        });
        return queryWrapper;
    }

    /**
     * 获取数据库字段名
     *
     * @param columnName 列名，前端传值
     * @param c          　类
     * @return 表名
     */
    private String getFieldName(String columnName, Class<?> c) {
        //字段名
        String fieldName = columnName;
        Field field = ReflectUtil.getField(c, fieldName);
        if (field == null) {
            return null;
        }
        //优先使用注解中的表字段名
        TableId tableId = field.getAnnotation(TableId.class);
        if (tableId != null && StringUtils.isNotBlank(tableId.value())) {
            fieldName = tableId.value();
        }
        //字段名进行驼峰转下划线处理
        fieldName = HumpLineUtil.humpToLine(fieldName);
        return fieldName;
    }

}
