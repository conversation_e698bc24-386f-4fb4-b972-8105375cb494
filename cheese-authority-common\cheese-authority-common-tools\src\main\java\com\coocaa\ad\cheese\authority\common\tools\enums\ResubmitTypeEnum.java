package com.coocaa.ad.cheese.authority.common.tools.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025-05-28
 */
@Getter
@AllArgsConstructor
public enum ResubmitTypeEnum {
    SCRATCH_OLD("0154-1", "沿用原节点 & 从头再审"),
    CONTINUE_OLD("0154-2", "沿用原节点 & 接续审批"),
    SCRATCH_NEW("0154-3", "获取新节点 & 从头再审");

    private final String code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static ResubmitTypeEnum getByCode(String code) {
        for (ResubmitTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + code + "]");
    }
}
