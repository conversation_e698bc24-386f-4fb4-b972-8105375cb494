package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeDocumentEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeEntity;
import com.coocaa.ad.cheese.ai.common.db.entity.AiKnowledgeSegmentEntity;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeDocumentService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeSegmentService;
import com.coocaa.ad.cheese.ai.common.db.service.IAiKnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * AI知识库文档分块服务
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiKnowledgeSegmentService {

    private final IAiKnowledgeSegmentService segmentService;
    private final IAiKnowledgeDocumentService documentService;
    private final IAiKnowledgeService knowledgeService;

    /**
     * 统计知识库的分块数量
     *
     * @param knowledgeId 知识库ID
     * @return 分块数量
     */
    public Integer countByKnowledgeId(Long knowledgeId) {
        log.info("统计知识库分块数量，知识库ID: {}", knowledgeId);

        LambdaQueryWrapper<AiKnowledgeSegmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AiKnowledgeSegmentEntity::getKnowledgeId, knowledgeId)
               .eq(AiKnowledgeSegmentEntity::getDeleteFlag, 0);

        return Math.toIntExact(segmentService.count(wrapper));
    }

    /**
     * 对文档进行分块
     *
     * @param documentId 文档ID
     * @return 分块列表
     */
    public List<AiKnowledgeSegmentEntity> chunkDocument(Long documentId) {
        log.info("开始对文档进行分块，文档ID: {}", documentId);

        try {
            // 1. 获取文档信息
            AiKnowledgeDocumentEntity document = documentService.getById(documentId);
            if (document == null) {
                throw new RuntimeException("文档不存在，文档ID: " + documentId);
            }

            // 2. 获取知识库配置
            AiKnowledgeEntity knowledge = knowledgeService.getById(document.getKnowledgeId());
            if (knowledge == null) {
                throw new RuntimeException("知识库不存在，知识库ID: " + document.getKnowledgeId());
            }

            // 3. 根据配置进行分块
            List<String> chunks = splitTextIntoChunks(
                    document.getContent(),
                    knowledge.getChunkSize(),
                    knowledge.getChunkOverlap()
            );

            // 4. 保存分块到数据库
            List<AiKnowledgeSegmentEntity> segments = new ArrayList<>();
            for (int i = 0; i < chunks.size(); i++) {
                String chunkContent = chunks.get(i);

                AiKnowledgeSegmentEntity segment = new AiKnowledgeSegmentEntity();
                segment.setKnowledgeId(document.getKnowledgeId());
                segment.setDocumentId(documentId);
                segment.setContent(chunkContent);
                segment.setWordCount(chunkContent.length());
                segment.setPosition(i);
                segment.setStatus("pending");
                segment.setDeleteFlag(0);

                segmentService.save(segment);
                segments.add(segment);

                log.debug("保存分块，文档ID: {}, 位置: {}, 内容长度: {}",
                        documentId, i, chunkContent.length());
            }

            log.info("文档分块完成，文档ID: {}, 分块数量: {}", documentId, segments.size());
            return segments;

        } catch (Exception e) {
            log.error("文档分块失败，文档ID: {}", documentId, e);
            throw new RuntimeException("文档分块失败: " + e.getMessage());
        }
    }

    /**
     * 根据向量ID列表获取分块
     *
     * @param vectorIds 向量ID列表
     * @return 分块列表
     */
    public List<AiKnowledgeSegmentEntity> getSegmentsByVectorIds(List<String> vectorIds) {
        log.info("根据向量ID获取分块，向量ID数量: {}", vectorIds.size());

        if (vectorIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AiKnowledgeSegmentEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(AiKnowledgeSegmentEntity::getVectorId, vectorIds)
               .eq(AiKnowledgeSegmentEntity::getDeleteFlag, 0);

        return segmentService.list(wrapper);
    }

    // ========== 私有方法 ==========

    /**
     * 将文本分割成块
     *
     * @param text 原始文本
     * @param chunkSize 分块大小
     * @param chunkOverlap 重叠大小
     * @return 分块列表
     */
    private List<String> splitTextIntoChunks(String text, Integer chunkSize, Integer chunkOverlap) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> chunks = new ArrayList<>();

        // 首先按段落分割
        String[] paragraphs = text.split("\n\n+");

        StringBuilder currentChunk = new StringBuilder();

        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty()) {
                continue;
            }

            // 如果当前段落加上现有内容超过分块大小
            if (currentChunk.length() + paragraph.length() > chunkSize) {
                // 保存当前分块
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());

                    // 处理重叠
                    if (chunkOverlap > 0 && currentChunk.length() > chunkOverlap) {
                        String overlap = currentChunk.substring(currentChunk.length() - chunkOverlap);
                        currentChunk = new StringBuilder(overlap);
                    } else {
                        currentChunk = new StringBuilder();
                    }
                }

                // 如果单个段落就超过分块大小，需要进一步分割
                if (paragraph.length() > chunkSize) {
                    List<String> subChunks = splitLongParagraph(paragraph, chunkSize, chunkOverlap);
                    chunks.addAll(subChunks);
                } else {
                    currentChunk.append(paragraph).append("\n\n");
                }
            } else {
                currentChunk.append(paragraph).append("\n\n");
            }
        }

        // 添加最后一个分块
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * 分割长段落
     */
    private List<String> splitLongParagraph(String paragraph, Integer chunkSize, Integer chunkOverlap) {
        List<String> chunks = new ArrayList<>();

        // 按句子分割
        String[] sentences = paragraph.split("[。！？.!?]+");

        StringBuilder currentChunk = new StringBuilder();

        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty()) {
                continue;
            }

            if (currentChunk.length() + sentence.length() > chunkSize) {
                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());

                    // 处理重叠
                    if (chunkOverlap > 0 && currentChunk.length() > chunkOverlap) {
                        String overlap = currentChunk.substring(currentChunk.length() - chunkOverlap);
                        currentChunk = new StringBuilder(overlap);
                    } else {
                        currentChunk = new StringBuilder();
                    }
                }

                // 如果单个句子还是太长，按字符强制分割
                if (sentence.length() > chunkSize) {
                    List<String> subChunks = splitByCharacters(sentence, chunkSize, chunkOverlap);
                    chunks.addAll(subChunks);
                } else {
                    currentChunk.append(sentence);
                }
            } else {
                currentChunk.append(sentence);
            }
        }

        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }

        return chunks;
    }

    /**
     * 按字符强制分割
     */
    private List<String> splitByCharacters(String text, Integer chunkSize, Integer chunkOverlap) {
        List<String> chunks = new ArrayList<>();

        int start = 0;
        while (start < text.length()) {
            int end = Math.min(start + chunkSize, text.length());
            String chunk = text.substring(start, end);
            chunks.add(chunk);

            start = end - chunkOverlap;
            if (start >= end) {
                break;
            }
        }

        return chunks;
    }
}
