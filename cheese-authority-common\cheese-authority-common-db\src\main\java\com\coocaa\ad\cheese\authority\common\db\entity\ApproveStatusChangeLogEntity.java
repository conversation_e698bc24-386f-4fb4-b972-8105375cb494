package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-03-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "approve_status_change_log")
public class ApproveStatusChangeLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息topic
     */
    private String topic;

    /**
     * 审批类型，销售合同：saleContract
     */
    private String approveType;

    /**
     * 审批事件key，审批实例：approval_instance
     */
    private String type;

    /**
     * 审批定义 Code
     */
    private String approvalCode;

    /**
     * 审批实例 Code
     */
    private String processCode;

    /**
     * 实例状态
     * PENDING - 审批中
     * APPROVED - 已通过
     * REJECTED - 已拒绝
     * CANCELED - 已撤回
     * DELETED - 已删除
     * REVERTED - 已撤销
     */
    private String status;

    /**
     * 	事件发生时间
     */
    private LocalDateTime instanceOperateTime;

    /**
     * 审批实例自定义唯一ID，接口创建审批时候传入。
     */
    private String uuid;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 任务节点名称
     */
    private String nodeName;

    /**
     * 任务节点ID
     */
    private String nodeCode;
}
