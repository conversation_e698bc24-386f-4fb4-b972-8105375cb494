package com.coocaa.ad.cheese.authority.common.db.service.approve.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalTaskRecordEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.approve.ApproveInternalTaskRecordMapper;
import com.coocaa.ad.cheese.authority.common.db.service.approve.IApproveInternalTaskRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class ApproveInternalTaskRecordServiceImpl extends ServiceImpl<ApproveInternalTaskRecordMapper, ApproveInternalTaskRecordEntity> implements IApproveInternalTaskRecordService {
}
