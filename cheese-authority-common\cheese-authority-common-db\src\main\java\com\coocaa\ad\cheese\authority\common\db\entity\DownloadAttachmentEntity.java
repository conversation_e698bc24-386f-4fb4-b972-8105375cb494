
package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 下载附件
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Data
@TableName("download_attachment")
public class DownloadAttachmentEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 下载任务ID
     */
    private Long taskId;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 附件全路径
     */
    private String url;

    /**
     * 大小(字节)
     */
    private String size;

    /**
     * 文件类型 (pdf, doc,...)
     */
    private String fileType;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}