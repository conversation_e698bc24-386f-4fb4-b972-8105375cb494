package com.coocaa.ad.cheese.ai.controller;

import com.coocaa.ad.cheese.ai.service.WechatLoginService;
import com.coocaa.ad.cheese.ai.vo.*;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信小程序登录控制器
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Slf4j
@RestController
@RequestMapping("/sys/wechat")
@Tag(name = "微信小程序登录", description = "微信小程序用户登录相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WechatLoginController {

    private final WechatLoginService wechatLoginService;

    /**
     * 微信小程序登录
     */
    @Operation(
            summary = "微信小程序登录",
            description = "通过微信小程序的临时登录凭证code进行用户登录，返回用户信息和自定义登录态",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "登录成功",
                            content = @Content(schema = @Schema(implementation = WechatLoginRespVO.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "请求参数错误"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "服务器内部错误"
                    )
            }
    )
    @PostMapping("/login")
    public ResultTemplate<WechatLoginRespVO> login(
            @Parameter(description = "微信小程序登录请求", required = true)
            @RequestBody @Validated WechatLoginReqVO loginReq) {

        log.info("收到微信小程序登录请求，code: {}", loginReq.getCode());

        try {
            WechatLoginRespVO loginResp = wechatLoginService.login(loginReq);

            log.info("微信小程序登录成功，openId: {}, userId: {}, isNewUser: {}",
                    loginResp.getOpenId(), loginResp.getUserId(), loginResp.getIsNewUser());

            return ResultTemplate.success(loginResp);

        } catch (Exception e) {
            log.error("微信小程序登录失败", e);
            return ResultTemplate.fail("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户详细信息
     */
    @Operation(
            summary = "获取用户详细信息",
            description = "通过解密微信返回的加密数据获取用户详细信息",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "获取成功",
                            content = @Content(schema = @Schema(implementation = WechatUserInfoRespVO.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "请求参数错误"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "服务器内部错误"
                    )
            }
    )
    @PostMapping("/getUserInfo")
    public ResultTemplate<WechatUserInfoRespVO> getUserInfo(
            @Parameter(description = "用户信息解密请求", required = true)
            @RequestBody @Validated WechatUserInfoReqVO userInfoReq) {

        log.info("收到获取用户详细信息请求，OpenID: {}", userInfoReq.getOpenId());

        try {
            // 调用服务层方法，包含用户信息解密和AI用户创建逻辑
            WechatUserInfoRespVO respVO = wechatLoginService.getUserInfoAndCreateAiUser(userInfoReq);
            return ResultTemplate.success(respVO);

        } catch (Exception e) {
            log.error("获取用户详细信息失败，OpenID: {}", userInfoReq.getOpenId(), e);
            return ResultTemplate.fail("获取用户详细信息失败: " + e.getMessage());
        }
    }
}
