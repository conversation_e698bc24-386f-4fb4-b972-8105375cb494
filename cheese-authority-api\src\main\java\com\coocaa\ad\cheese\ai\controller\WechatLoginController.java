package com.coocaa.ad.cheese.ai.controller;

import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.coocaa.ad.cheese.ai.bean.AiUserParam;
import com.coocaa.ad.cheese.ai.service.AiUserService;
import com.coocaa.ad.cheese.ai.service.WechatLoginService;
import com.coocaa.ad.cheese.ai.vo.*;
import com.coocaa.ad.cheese.authority.common.tools.result.ResultTemplate;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 微信小程序登录控制器
 *
 * <AUTHOR>
 * @since 2025-7-9
 */
@Slf4j
@RestController
@RequestMapping("/sys/wechat")
@Tag(name = "微信小程序登录", description = "微信小程序用户登录相关接口")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class WechatLoginController {

    private final WechatLoginService wechatLoginService;
    private final AiUserService aiUserService;

    /**
     * 微信小程序登录
     */
    @Operation(
            summary = "微信小程序登录",
            description = "通过微信小程序的临时登录凭证code进行用户登录，返回用户信息和自定义登录态",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "登录成功",
                            content = @Content(schema = @Schema(implementation = WechatLoginRespVO.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "请求参数错误"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "服务器内部错误"
                    )
            }
    )
    @PostMapping("/login")
    public ResultTemplate<WechatLoginRespVO> login(
            @Parameter(description = "微信小程序登录请求", required = true)
            @RequestBody @Validated WechatLoginReqVO loginReq) {

        log.info("收到微信小程序登录请求，code: {}", loginReq.getCode());

        try {
            WechatLoginRespVO loginResp = wechatLoginService.login(loginReq);

            log.info("微信小程序登录成功，openId: {}, userId: {}, isNewUser: {}",
                    loginResp.getOpenId(), loginResp.getUserId(), loginResp.getIsNewUser());

            return ResultTemplate.success(loginResp);

        } catch (Exception e) {
            log.error("微信小程序登录失败", e);
            return ResultTemplate.fail("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户详细信息
     */
    @Operation(
            summary = "获取用户详细信息",
            description = "通过解密微信返回的加密数据获取用户详细信息",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "获取成功",
                            content = @Content(schema = @Schema(implementation = WechatUserInfoRespVO.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "请求参数错误"
                    ),
                    @ApiResponse(
                            responseCode = "500",
                            description = "服务器内部错误"
                    )
            }
    )
    @PostMapping("/getUserInfo")
    public ResultTemplate<WechatUserInfoRespVO> getUserInfo(
            @Parameter(description = "用户信息解密请求", required = true)
            @RequestBody @Validated WechatUserInfoReqVO userInfoReq) {

        log.info("收到获取用户详细信息请求");

        try {
            WxMaUserInfo wxUserInfo = wechatLoginService.getUserInfo(
                    userInfoReq.getSessionKey(),
                    userInfoReq.getEncryptedData(),
                    userInfoReq.getIv()
            );

            // 转换为响应VO
            WechatUserInfoRespVO.WatermarkInfo watermark = null;
            if (wxUserInfo.getWatermark() != null) {
                // 处理timestamp类型转换
                Long timestamp = null;
                try {
                    if (wxUserInfo.getWatermark().getTimestamp() != null) {
                        timestamp = Long.parseLong(wxUserInfo.getWatermark().getTimestamp());
                    }
                } catch (NumberFormatException e) {
                    log.warn("时间戳格式转换失败: {}", wxUserInfo.getWatermark().getTimestamp());
                }

                watermark = WechatUserInfoRespVO.WatermarkInfo.builder()
                        .appid(wxUserInfo.getWatermark().getAppid())
                        .timestamp(timestamp)
                        .build();
            }

            // 处理gender类型转换
            Integer gender = null;
            try {
                if (wxUserInfo.getGender() != null) {
                    gender = Integer.parseInt(wxUserInfo.getGender());
                }
            } catch (NumberFormatException e) {
                log.warn("性别格式转换失败: {}", wxUserInfo.getGender());
            }

            WechatUserInfoRespVO respVO = WechatUserInfoRespVO.builder()
                    .nickName(wxUserInfo.getNickName())
                    .avatarUrl(wxUserInfo.getAvatarUrl())
                    .gender(gender)
                    .country(wxUserInfo.getCountry())
                    .province(wxUserInfo.getProvince())
                    .city(wxUserInfo.getCity())
                    .language(wxUserInfo.getLanguage())
                    // WxMaUserInfo中没有openId字段
                    .openId(null)
                    .unionId(wxUserInfo.getUnionId())
//                    .watermark(watermark)
                    .build();

            log.info("获取用户详细信息成功，昵称: {}, 城市: {}",
                    wxUserInfo.getNickName(), wxUserInfo.getCity());

            return ResultTemplate.success(respVO);

        } catch (Exception e) {
            log.error("获取用户详细信息失败", e);
            return ResultTemplate.fail("获取用户详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI用户信息（根据OpenID，不存在则创建）
     */
    @Operation(
            summary = "获取AI用户信息",
            description = "根据OpenID获取AI用户信息，如果用户不存在则自动创建"
    )
    @PostMapping("/user/info")
    public ResultTemplate<AiUserInfoVO> getAiUserInfo(
            @Parameter(description = "用户信息请求", required = true)
            @RequestBody @Validated AiUserInfoReqVO userInfoReq) {

        log.info("获取AI用户信息，OpenID: {}", userInfoReq.getOpenId());

        try {
            // 调用服务层方法查找或创建用户
            AiUserInfoVO userInfoVO = aiUserService.findOrCreateUser(userInfoReq);
            return ResultTemplate.success(userInfoVO);

        } catch (Exception e) {
            log.error("获取AI用户信息失败，OpenID: {}", userInfoReq.getOpenId(), e);
            return ResultTemplate.fail("获取AI用户信息失败: " + e.getMessage());
        }
    }



    /**
     * 更新用户信息
     */
    @Operation(
            summary = "更新用户信息",
            description = "更新AI用户信息"
    )
    @PutMapping("/user")
    public ResultTemplate<Boolean> updateUserInfo(
            @Parameter(description = "用户信息更新请求", required = true)
            @RequestBody @Validated AiUserUpdateReqVO updateReq) {

        log.info("更新用户信息，用户ID: {}", updateReq.getId());

        try {
            AiUserVO existingUser = aiUserService.getDetail(updateReq.getId());
            if (existingUser == null) {
                return ResultTemplate.fail("用户不存在");
            }

            // 构建更新参数
            AiUserParam updateParam = new AiUserParam();
            updateParam.setId(updateReq.getId());
            updateParam.setOpenId(existingUser.getOpenId());
            updateParam.setName(updateReq.getNickName() != null ? updateReq.getNickName() : existingUser.getName());
            updateParam.setAvatar(updateReq.getAvatarUrl() != null ? updateReq.getAvatarUrl() : existingUser.getAvatar());
            // 保持原有mobile不变
            updateParam.setMobile(existingUser.getMobile());
            updateParam.setCreateTime(existingUser.getCreateTime());
            updateParam.setCreator(existingUser.getCreator());
            updateParam.setUpdateTime(existingUser.getUpdateTime());
            updateParam.setOperator(existingUser.getOperator());
            updateParam.setDeleteFlag(existingUser.getDeleteFlag());

            boolean updated = aiUserService.update(updateReq.getId(), updateParam);

            log.info("用户信息更新{}, 用户ID: {}", updated ? "成功" : "失败", updateReq.getId());
            return ResultTemplate.success(updated);

        } catch (Exception e) {
            log.error("更新用户信息失败，用户ID: {}", updateReq.getId(), e);
            return ResultTemplate.fail("更新用户信息失败: " + e.getMessage());
        }
    }
}
