package com.coocaa.ad.cheese.authority.common.db.mapper;

import com.coocaa.ad.cheese.authority.common.db.bean.CityDetailParam;
import com.coocaa.ad.cheese.authority.common.db.bean.CityDetailVO;
import com.coocaa.ad.cheese.authority.common.db.bean.ProvinceCityDistrictSelectVO;
import com.coocaa.ad.cheese.authority.common.db.entity.CityEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 城市 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface CityMapper extends BaseMapper<CityEntity> {

    List<CityDetailVO> getDetailByNames(@Param("params") List<CityDetailParam> params);

    List<ProvinceCityDistrictSelectVO> getProvinceCity(@Param("districts") Set<Integer> districts);
}
