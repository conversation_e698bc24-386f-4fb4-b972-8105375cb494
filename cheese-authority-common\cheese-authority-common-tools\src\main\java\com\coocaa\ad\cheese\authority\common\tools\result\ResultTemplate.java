package com.coocaa.ad.cheese.authority.common.tools.result;


import com.alibaba.fastjson2.annotation.JSONField;

import java.io.Serializable;

/**
 * 类ResultTemplate实现描述:
 *
 * <AUTHOR>
 * @date 2021年09月18 11:06 上午
 */
public class ResultTemplate<T> implements Serializable {
    /**
     * 默认操作成功
     */
    private static final IReturnCode DEFAULT_SUCCESS = IReturnCode.Default.SUCCESS;

    /**
     * 默认操作失败
     */
    private static final IReturnCode DEFAULT_ERROR = IReturnCode.Default.ERROR;

    /**
     * 响应码
     */
    private String code;

    /**
     * 错误码
     */
    private String errCode = "";

    /**
     * 描述
     */
    private String msg;

    /**
     * 响应结果状态，{@code true} 表示成功；{@code false} 表示失败
     */
    private Boolean success = Boolean.FALSE;

    /**
     * 响应头：可以存放请求相关的信息
     */
    @JSONField(serialize = false)
    private ResultHeader header;

    /**
     * 响应体
     */
    private T data;

    /**
     * 构造方法：无参数
     */
    public ResultTemplate() {
    }

    /**
     * 操作成功
     *
     * @param <T> {@link Void}
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> success() {
        return new ResultTemplate<T>()
                .setSuccess(Boolean.TRUE)
                .setCode(DEFAULT_SUCCESS.getErrCode())
                .setMsg(DEFAULT_SUCCESS.getMsg());
    }

    /**
     * 操作成功
     *
     * @param data 响应体
     * @param <T>  响应体类型
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> success(T data) {
        return new ResultTemplate<T>()
                .setSuccess(Boolean.TRUE)
                .setCode(DEFAULT_SUCCESS.getErrCode())
                .setMsg(DEFAULT_SUCCESS.getMsg())
                .setData(data);
    }

    /**
     * 操作失败
     *
     * @param <T> {@link Void}
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> fail() {
        return new ResultTemplate<T>()
                .setCode(DEFAULT_ERROR.getErrCode())
                .setErrCode(DEFAULT_ERROR.getErrCode())
                .setMsg(DEFAULT_ERROR.getMsg());
    }


    /**
     * 操作失败
     *
     * @param msg 响应信息
     * @param <T> {@link Void}
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> fail(String msg) {
        return new ResultTemplate<T>()
                .setCode(DEFAULT_ERROR.getErrCode())
                .setErrCode(DEFAULT_ERROR.getErrCode())
                .setMsg(msg);
    }


    /**
     * 操作失败
     *
     * @param returnCode {@link IReturnCode}
     * @param <T>        {@link Void}
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> fail(IReturnCode returnCode) {
        return new ResultTemplate<T>()
                .setCode(DEFAULT_ERROR.getErrCode())
                .setErrCode(returnCode.getErrCode())
                .setMsg(returnCode.getMsg());
    }

    /**
     * 操作失败
     *
     * @param returnCode {@link IReturnCode}
     * @param msg        描述信息
     * @param <T>        {@link Void}
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> fail(IReturnCode returnCode, String msg) {
        return new ResultTemplate<T>()
                .setCode(DEFAULT_ERROR.getErrCode())
                .setErrCode(returnCode.getErrCode())
                .setMsg(msg);
    }


    /**
     * 操作失败
     *
     * @param errCode 错误码
     * @param msg     响应信息
     * @param <T>     {@link Void}
     * @return 响应封装类 {@link ResultTemplate}
     */
    public static <T> ResultTemplate<T> fail(String errCode, String msg) {
        return new ResultTemplate<T>()
                .setCode(DEFAULT_ERROR.getErrCode())
                .setErrCode(errCode)
                .setMsg(msg);
    }

    /**
     * {@code code} 的get方法
     *
     * @return {@code code} 的值
     */
    public String getCode() {
        return code;
    }

    /**
     * {@code code} 的get方法
     *
     * @param code 响应码
     * @return {@link ResultTemplate}
     */
    private ResultTemplate<T> setCode(String code) {
        this.code = code;
        return this;
    }

    /**
     * {@code msg} 的get方法
     *
     * @return {@code msg} 的值
     */
    public String getMsg() {
        return msg;
    }

    /**
     * {@code msg} 的get方法
     *
     * @param msg 响应信息
     * @return {@link ResultTemplate}
     */
    public ResultTemplate<T> setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    /**
     * {@code success} 的get方法
     *
     * @return {@code success} 的值
     */
    public Boolean getSuccess() {
        return success;
    }

    /**
     * {@code success} 的get方法
     *
     * @param success 操作结果
     * @return {@link ResultTemplate}
     */
    public ResultTemplate<T> setSuccess(Boolean success) {
        this.success = success;
        return this;
    }

    /**
     * {@code header} 的get方法
     *
     * @return {@code header} 的值
     */
    public ResultHeader getHeader() {
        return header;
    }

    /**
     * {@code header} 的get方法
     *
     * @param header 响应头
     * @return {@link ResultTemplate}
     */
    public ResultTemplate<T> setHeader(ResultHeader header) {
        this.header = header;
        return this;
    }

    /**
     * {@code data} 的get方法
     *
     * @return {@code data} 的值
     */
    public T getData() {
        return data;
    }

    /**
     * {@code data} 的set方法
     *
     * @param data 响应体
     * @return {@link ResultTemplate}
     */
    public ResultTemplate<T> setData(T data) {
        this.data = data;
        return this;
    }

    /**
     * {@code errCode} 的get方法
     *
     * @return {@code errCode} 的值
     */
    public String getErrCode() {
        return errCode;
    }

    /**
     * {@code errCode} 的set方法
     *
     * @param errCode 错误码
     * @return {@link ResultTemplate}
     */
    public ResultTemplate<T> setErrCode(String errCode) {
        this.errCode = errCode;
        return this;
    }

    @Override
    public String toString() {
        return "ResultTemplate{" +
                "code='" + code + '\'' +
                ", errCode='" + errCode + '\'' +
                ", msg='" + msg + '\'' +
                ", success=" + success +
                ", header=" + header +
                ", data=" + data +
                '}';
    }
}
