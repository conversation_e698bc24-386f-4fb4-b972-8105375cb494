package com.coocaa.ad.cheese.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.ai.entity.AiConversationEntity;

/**
 * AI对话服务接口
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
public interface AiConversationEntityService extends IService<AiConversationEntity> {

    /**
     * 为用户创建新的对话
     *
     * @param userId 用户ID
     * @return 对话实体
     */
    AiConversationEntity createConversationForUser(Long userId);

    /**
     * 根据用户ID获取最新的对话
     *
     * @param userId 用户ID
     * @return 对话实体，如果不存在则创建新的
     */
    AiConversationEntity getOrCreateConversationByUserId(Long userId);

    /**
     * 验证对话是否属于指定用户
     *
     * @param conversationId 对话ID
     * @param userId 用户ID
     * @return 是否属于该用户
     */
    boolean validateConversationOwnership(Long conversationId, Long userId);
}
