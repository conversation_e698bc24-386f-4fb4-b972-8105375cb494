package com.coocaa.ad.cheese.authority.vo.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
public class ApproveInternalRuleVO {

    private Integer id;

    /** 规则名称 */
    @Schema(description = "规则名称")
    private String name;

    /** 规则描述 */
    @Schema(description = "规则描述")
    private String ruleDesc;

    /** 规则编号 */
    @Schema(description = "规则编号")
    private Long code;

    /** 审批任务名称 */
    @Schema(description = "审批任务名称")
    private String approvalName;

    /** 审批任务url */
    @Schema(description = "审批任务url")
    private String approvalUrl;

    /** 审批通过提示 */
    @Schema(description = "审批通过提示")
    private String passPrompt;

    /** 审批通过url */
    @Schema(description = "审批通过url")
    private String passUrl;

    /** 审批拒绝提示 */
    @Schema(description = "审批拒绝提示")
    private String rejectPrompt;

    /** 审批拒绝url */
    @Schema(description = "审批拒绝url")
    private String rejectUrl;

    @Schema(description = "审批驳回提示")
    private String rollbackPrompt;

    @Schema(description = "审批驳回url")
    private String rollbackUrl;

    @Schema(description = "抄送提示")
    private String copyPrompt;

    @Schema(description = "抄送url")
    private String copyUrl;

    @Schema(description = "应用类型编码，字典0124")
    private String appCode;

    @Schema(description = "模块类型编码，字典0125")
    private String moduleCode;

    @Schema(description = "抄送人ID数组")
    private Set<Integer> copyUserIds;

    /** 是否启用。1：启用，0：未启用 */
    @Schema(description = "是否启用。1：启用，0：未启用")
    private Integer flag;

    @Schema(description = "自定义入参")
    private List<InternalRuleArgumentVO> ruleArguments = Collections.emptyList();
}
