package com.coocaa.ad.cheese.authority.common.tools.utils.annotaion;

import com.coocaa.ad.cheese.authority.common.tools.utils.ChangeFormatter;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 变更字段提取器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-24
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ChangeExtract {
    /**
     * 对字段进行分组
     */
    String readableName() default "";

    /**
     * 比较的目标字段名，默认与源字段相同
     */
    String targetFieldName() default "";

    /**
     * 强制比较
     */
    boolean force() default false;

    /**
     * 格式化器
     */
    Class<? extends ChangeFormatter> formatter() default ChangeFormatter.DefaultFormatter.class;

}
