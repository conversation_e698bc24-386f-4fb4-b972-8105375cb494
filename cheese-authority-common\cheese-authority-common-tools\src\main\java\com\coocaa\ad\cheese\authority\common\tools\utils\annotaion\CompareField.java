package com.coocaa.ad.cheese.authority.common.tools.utils.annotaion;

import com.coocaa.ad.cheese.authority.common.tools.translate.DefaultTranslate;
import com.coocaa.ad.cheese.authority.common.tools.translate.Translate;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2024/10/27
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CompareField {

    String description() default "";

    Class<? extends Translate> translateType() default DefaultTranslate.class;

}
