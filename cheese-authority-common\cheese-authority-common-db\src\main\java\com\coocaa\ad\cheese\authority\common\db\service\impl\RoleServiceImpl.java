package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.coocaa.ad.cheese.authority.common.db.entity.RoleEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.RoleMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, RoleEntity> implements IRoleService {

}
