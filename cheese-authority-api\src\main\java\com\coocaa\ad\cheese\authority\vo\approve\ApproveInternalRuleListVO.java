package com.coocaa.ad.cheese.authority.vo.approve;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-04-30
 */
@Data
public class ApproveInternalRuleListVO {

    private Integer id;

    /** 规则名称 */
    @Schema(description = "规则名称")
    private String name;

    /** 规则描述 */
    @Schema(description = "规则描述")
    private String ruleDesc;

    /** 规则编号 */
    @Schema(description = "规则编号")
    private Long code;

    /** 审批任务名称 */
    @Schema(description = "审批任务名称")
    private String approvalName;

    /** 是否启用。1：启用，0：未启用 */
    @Schema(description = "是否启用。1：启用，0：未启用")
    private Integer flag;
}
