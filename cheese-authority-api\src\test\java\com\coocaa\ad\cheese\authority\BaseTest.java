package com.coocaa.ad.cheese.authority;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 测试基类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-28
 */
@ActiveProfiles("dev")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseTest {
    protected static final int USER_ID = ThreadLocalRandom.current().nextInt(0, Integer.MAX_VALUE);
    protected static final Date NOW = new Date();


}
