package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.bean.ResourceDO;
import com.coocaa.ad.cheese.authority.common.db.entity.UserDepartmentEntity;
import com.coocaa.ad.cheese.authority.common.db.entity.UserEntity;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IUserService extends IService<UserEntity> {

    /**
     * 用户登陆
     *
     * @param loginName 登陆名， 支持：登陆名、工号、手机、邮箱
     * @param password  登录密码
     * @return 用户信息
     */
    UserEntity login(String loginName, String password);

    /**
     * 根据条件查询用户列表
     *
     * @param page         分页信息
     * @param userId       用户ID
     * @param keyword      查询关键字
     * @param roleIds      角色IDs
     * @param cityGroupIds 城市组IDs
     * @return 用户列表
     */
    Page<UserEntity> listByCondition(Page<UserEntity> page, Integer userId, String keyword,
                                     List<Integer> roleIds, List<Integer> cityGroupIds, List<Integer> channelIds);

    List<ResourceDO> listUserRoleResources3Type(Integer userId,String platform);

    /**
     * 根据用户ID查询部门信息
     * @param userId
     * @return
     */
    List<UserDepartmentEntity> listUserDepartment(Integer userId,String departmentName,List<Integer> departmentId);
    /**
     * 根据飞书openId查询用户信息
     * @param openId
     * @return
     */
    UserEntity getUserByFsOpenId(String openId);

    /**
     *根据用户ID批量查询部门信息
     * @param userIds
     * @return
     */
     List<UserDepartmentEntity> listDepartmentByUserId(Set<Integer> userIds);

    /**
     *根据机构ID分页查询用户信息
     * @param agencyId
     * @return
     */
    Page<UserEntity> listByAgencyId(Page<UserEntity> page, Integer agencyId);
}
