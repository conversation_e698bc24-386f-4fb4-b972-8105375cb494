package com.coocaa.ad.cheese.authority.common.tools.query.enums;

/**
 * 类FilterType实现描述:
 *
 * <AUTHOR>
 * @date 2022年05月08 12:58
 */
public enum FilterType {

    /**
     * 默认and
     */
    DEFAULT("默认and", 0),
    /**
     * 并且
     */
    AND("并且", 1),
    /**
     * 或者
     */
    OR("或者", 2);

    private String name;
    private Integer type;


    FilterType(String name, Integer type) {

        this.name = name;
        this.type = type;
    }


    public String getName() {

        return name;
    }


    public void setName(String name) {

        this.name = name;
    }


    public Integer getType() {

        return type;
    }


    public void setType(Integer type) {

        this.type = type;
    }

}
