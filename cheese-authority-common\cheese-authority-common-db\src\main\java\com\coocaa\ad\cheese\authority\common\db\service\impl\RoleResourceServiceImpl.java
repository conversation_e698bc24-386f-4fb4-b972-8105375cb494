package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.coocaa.ad.cheese.authority.common.db.entity.RoleResourceEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.RoleResourceMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IRoleResourceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 角色资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class RoleResourceServiceImpl extends ServiceImpl<RoleResourceMapper, RoleResourceEntity> implements IRoleResourceService {

}
