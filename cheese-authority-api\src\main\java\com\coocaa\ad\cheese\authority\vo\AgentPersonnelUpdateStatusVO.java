package com.coocaa.ad.cheese.authority.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;
import org.checkerframework.checker.units.qual.Acceleration;


@Data
@Accessors(chain = true)
public class AgentPersonnelUpdateStatusVO {


    @NotBlank(message = "员工编码不能为空")
    private String empCode;


    @NotBlank(message = "状态不能为空")
    private Integer status;
}
