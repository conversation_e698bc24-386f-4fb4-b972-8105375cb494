package com.coocaa.ad.cheese.authority.common.tools.encrypt;

import com.coocaa.ad.cheese.authority.common.tools.annotation.EncryptField;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 加解密工具类
 * 
 * <AUTHOR>
 * @since 2025-07-03
 */
@Slf4j
@Component
public class EncryptUtils implements ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    private static final Map<EncryptField.EncryptType, EncryptHandler> handlerCache = new ConcurrentHashMap<>();
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        EncryptUtils.applicationContext = applicationContext;
    }
    
    /**
     * 对对象中标记了@EncryptField注解的字段进行加密
     * 
     * @param obj 要加密的对象
     */
    public static void encryptFields(Object obj) {
        if (obj == null) {
            return;
        }
        
        processFields(obj, true);
    }
    
    /**
     * 对对象中标记了@EncryptField注解的字段进行解密
     * 
     * @param obj 要解密的对象
     */
    public static void decryptFields(Object obj) {
        if (obj == null) {
            return;
        }
        
        processFields(obj, false);
    }
    
    /**
     * 对列表中的对象进行加密
     * 
     * @param list 对象列表
     */
    public static void encryptFieldsList(List<?> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        
        list.forEach(EncryptUtils::encryptFields);
    }
    
    /**
     * 对列表中的对象进行解密
     * 
     * @param list 对象列表
     */
    public static void decryptFieldsList(List<?> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        
        list.forEach(EncryptUtils::decryptFields);
    }
    
    /**
     * 处理字段加解密
     *
     * @param obj 对象
     * @param encrypt true-加密，false-解密
     */
    private static void processFields(Object obj, boolean encrypt) {
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            EncryptField encryptField = field.getAnnotation(EncryptField.class);
            if (encryptField == null || !encryptField.enabled()) {
                continue;
            }

            // 只处理String类型的字段
            if (!String.class.equals(field.getType())) {
                log.warn("字段 {} 不是String类型，跳过加解密处理", field.getName());
                continue;
            }

            try {
                field.setAccessible(true);
                String value = (String) field.get(obj);

                if (!StringUtils.hasText(value)) {
                    log.info("字段 {} 的值为空，跳过处理", field.getName());
                    continue;
                }

                log.info("字段 {} 原始值: {}", field.getName(), value);

                EncryptHandler handler = getEncryptHandler(encryptField.value());
                if (handler == null) {
                    log.warn("未找到支持加密类型 {} 的处理器", encryptField.value());
                    continue;
                }

                String processedValue;
                if (encrypt) {
                    processedValue = handler.encrypt(value, encryptField.value());
                    log.info("字段 {} 加密后值: {}", field.getName(), processedValue);
                } else {
                    processedValue = handler.decrypt(value, encryptField.value());
                    log.info("字段 {} 解密后值: {}", field.getName(), processedValue);
                }

                field.set(obj, processedValue);

            } catch (Exception e) {
                log.error("处理字段 {} 的加解密时发生异常", field.getName(), e);
            }
        }
    }
    
    /**
     * 获取加解密处理器
     * 
     * @param encryptType 加密类型
     * @return 处理器
     */
    private static EncryptHandler getEncryptHandler(EncryptField.EncryptType encryptType) {
        return handlerCache.computeIfAbsent(encryptType, type -> {
            if (applicationContext == null) {
                log.warn("ApplicationContext未初始化，使用默认处理器");
                return new DefaultEncryptHandler();
            }
            
            Map<String, EncryptHandler> handlers = applicationContext.getBeansOfType(EncryptHandler.class);
            for (EncryptHandler handler : handlers.values()) {
                if (handler.supports(type)) {
                    return handler;
                }
            }
            
            log.warn("未找到支持加密类型 {} 的处理器，使用默认处理器", type);
            return new DefaultEncryptHandler();
        });
    }
}
