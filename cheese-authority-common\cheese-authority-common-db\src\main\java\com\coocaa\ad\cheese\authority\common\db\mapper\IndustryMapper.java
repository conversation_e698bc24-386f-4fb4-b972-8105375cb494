package com.coocaa.ad.cheese.authority.common.db.mapper;

import com.coocaa.ad.cheese.authority.common.db.entity.IndustryEntity;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * <p>
 * 行业 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface IndustryMapper extends BaseMapper<IndustryEntity> {

    /**
     * 获取所有二级行业
     * @return 二级行业列表
     */
    List<IndustryEntity> listAllSecondWithParent();
}
