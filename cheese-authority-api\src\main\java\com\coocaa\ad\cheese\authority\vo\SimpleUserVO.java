package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-13
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SimpleUserVO {
    @Schema(description = "ID", type = "Integer", example = "1")
    private Integer id;

    @Schema(description = "姓名", type = "String", example = "张三")
    private String name;

    @Schema(description = "工号", type = "String", example = "123")
    private String wno;
}
