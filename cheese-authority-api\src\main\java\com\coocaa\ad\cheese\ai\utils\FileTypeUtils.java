package com.coocaa.ad.cheese.ai.utils;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.mime.MimeTypeException;
import org.apache.tika.mime.MimeTypes;

/**
 * 文件类型工具类
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Slf4j
public class FileTypeUtils {

    private static final Tika TIKA = new Tika();

    /**
     * 获得文件的 mineType，对于 doc，jar 等文件会有误差
     *
     * @param data 文件内容
     * @return mineType 无法识别时会返回"application/octet-stream"
     */
    @SneakyThrows
    public static String getMineType(byte[] data) {
        return TIKA.detect(data);
    }

    /**
     * 已知文件名，获取文件类型，在某些情况下比通过字节数组准确，例如使用 jar 文件时，通过名字更为准确
     *
     * @param name 文件名
     * @return mineType 无法识别时会返回"application/octet-stream"
     */
    public static String getMineType(String name) {
        return TIKA.detect(name);
    }

    /**
     * 在拥有文件和数据的情况下，最好使用此方法，最为准确
     *
     * @param data 文件内容
     * @param name 文件名
     * @return mineType 无法识别时会返回"application/octet-stream"
     */
    public static String getMineType(byte[] data, String name) {
        return TIKA.detect(data, name);
    }

    /**
     * 根据 mineType 获得文件后缀
     *
     * 注意：如果获取不到，或者发生异常，都返回 null
     *
     * @param mineType 类型
     * @return 后缀，例如说 .pdf
     */
    public static String getExtension(String mineType) {
        try {
            return MimeTypes.getDefaultMimeTypes().forName(mineType).getExtension();
        } catch (MimeTypeException e) {
            log.warn("[getExtension][获取文件后缀({}) 失败]", mineType, e);
            return null;
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名，不包含点号
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 判断是否为文本文件
     *
     * @param fileName 文件名
     * @return 是否为文本文件
     */
    public static boolean isTextFile(String fileName) {
        String extension = getFileExtension(fileName);
        return "txt".equals(extension) || "md".equals(extension) || 
               "csv".equals(extension) || "json".equals(extension) ||
               "xml".equals(extension) || "html".equals(extension) ||
               "htm".equals(extension);
    }

    /**
     * 判断是否为PDF文件
     *
     * @param fileName 文件名
     * @return 是否为PDF文件
     */
    public static boolean isPdfFile(String fileName) {
        return "pdf".equals(getFileExtension(fileName));
    }

    /**
     * 判断是否为Word文档
     *
     * @param fileName 文件名
     * @return 是否为Word文档
     */
    public static boolean isWordFile(String fileName) {
        String extension = getFileExtension(fileName);
        return "doc".equals(extension) || "docx".equals(extension);
    }

    /**
     * 判断是否为Excel文件
     *
     * @param fileName 文件名
     * @return 是否为Excel文件
     */
    public static boolean isExcelFile(String fileName) {
        String extension = getFileExtension(fileName);
        return "xls".equals(extension) || "xlsx".equals(extension);
    }

    /**
     * 判断是否为PowerPoint文件
     *
     * @param fileName 文件名
     * @return 是否为PowerPoint文件
     */
    public static boolean isPowerPointFile(String fileName) {
        String extension = getFileExtension(fileName);
        return "ppt".equals(extension) || "pptx".equals(extension);
    }

    /**
     * 判断是否为支持的文档类型
     *
     * @param fileName 文件名
     * @return 是否为支持的文档类型
     */
    public static boolean isSupportedDocument(String fileName) {
        return isTextFile(fileName) || isPdfFile(fileName) || 
               isWordFile(fileName) || isExcelFile(fileName) || 
               isPowerPointFile(fileName);
    }
}
