-- AI对话表添加角色字段
-- 执行时间：2025-07-17

-- 1. 添加角色ID字段
ALTER TABLE `ai_conversation` 
ADD COLUMN `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID（关联ai_role表的id）' AFTER `user_id`;

-- 2. 创建角色ID索引
CREATE INDEX `idx_role_id` ON `ai_conversation` (`role_id`);

-- 3. 查看表结构（验证字段是否添加成功）
DESC `ai_conversation`;

-- 4. 如果需要为现有对话设置默认角色，可以执行以下语句（可选）
-- 假设默认角色ID为1（默认助手）
-- UPDATE `ai_conversation` SET `role_id` = 1 WHERE `role_id` IS NULL;

-- 表结构说明：
-- id: 主键ID，自增
-- user_id: 用户ID，关联ai_user表
-- role_id: 角色ID，关联ai_role表（新增字段）
-- create_time: 创建时间
-- update_time: 更新时间
-- create_by: 创建人
-- update_by: 更新人
-- delete_flag: 删除标记，0-正常，1-已删除

-- 字段使用说明：
-- role_id: 存储对话使用的AI角色ID
-- 
-- 使用场景：
-- 1. 创建对话时指定角色
-- 2. 发送消息时根据对话的角色ID获取角色提示词
-- 3. 实现多角色多轮对话

-- 查询示例：
-- 1. 查询用户的所有对话及其角色：
-- SELECT c.id, c.user_id, c.role_id, r.role_name, r.role_prompt 
-- FROM ai_conversation c 
-- LEFT JOIN ai_role r ON c.role_id = r.id 
-- WHERE c.user_id = ? AND c.delete_flag = 0;

-- 2. 查询某个角色的所有对话：
-- SELECT * FROM ai_conversation WHERE role_id = ? AND delete_flag = 0;

-- 3. 统计各角色的对话数量：
-- SELECT r.role_name, COUNT(c.id) as conversation_count 
-- FROM ai_role r 
-- LEFT JOIN ai_conversation c ON r.id = c.role_id AND c.delete_flag = 0 
-- WHERE r.delete_flag = 0 
-- GROUP BY r.id, r.role_name;

-- 回滚语句（如果需要删除字段）：
-- DROP INDEX `idx_role_id` ON `ai_conversation`;
-- ALTER TABLE `ai_conversation` DROP COLUMN `role_id`;
