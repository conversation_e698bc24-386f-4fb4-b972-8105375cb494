package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.channel.ChannelEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.channel.ChannelMapper;
import com.coocaa.ad.cheese.authority.common.db.service.IChannelService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class ChannelServiceImpl extends ServiceImpl<ChannelMapper, ChannelEntity> implements IChannelService {

    @Override
    public List<ChannelEntity> getUserChannel(Integer userId, List<Integer> ids) {
        return this.baseMapper.getUserChannel(userId, ids);
    }
}
