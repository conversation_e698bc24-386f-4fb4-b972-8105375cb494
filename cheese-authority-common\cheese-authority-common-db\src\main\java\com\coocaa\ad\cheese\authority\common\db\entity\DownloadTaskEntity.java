
package com.coocaa.ad.cheese.authority.common.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 下载任务表
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
@Data
@TableName("download_task")
public class DownloadTaskEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 系统类型
     */
    private String sysType;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类别
     */
    private String category;

    /**
     * 状态
     */
    private String status;

    /**
     * 执行时的参数
     */
    private String executeParams;

    /**
     * 失败原因
     */
    private String failMsg;

    /**
     * 删除标记  [0:否, 1:是]
     */
    private Integer deleteFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 操作人
     */
    private Integer operator;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 下载次数
     */
    private Integer count;
}