# AI聊天流式接口V2功能总结

## 概述

基于现有的 `sendChatMessageStream` 接口，创建了一个新的流式聊天接口 `sendChatMessageStreamV2`，支持动态模型配置、最大上下文限制，并使用新的 `getChatCompletionChunk` 方法直接返回豆包的 Flowable 响应。

## 主要功能特性

### 1. 动态模型配置
- ✅ 支持根据角色配置动态选择AI模型
- ✅ 不再依赖 @PostConstruct 初始化，支持运行时模型切换
- ✅ 自动获取角色关联的模型配置
- ✅ 提供默认模型兜底机制

### 2. 最大上下文限制
- ✅ 根据模型的 maxTokens 限制上下文长度
- ✅ 智能Token估算（中文字符*2 + 英文单词*1）
- ✅ 优先保留最近的对话历史
- ✅ 预留30%Token给系统提示词和当前消息

### 3. 流式响应优化
- ✅ 直接返回豆包SDK的原生Flowable
- ✅ 减少中间处理层，提高性能
- ✅ 支持增量内容和完整内容返回
- ✅ 完善的错误处理和日志记录

## 核心实现

### 1. DoubaoAiService 新增方法

#### getChatCompletionChunk (动态配置版本)
```java
public Flowable<BotChatCompletionChunk> getChatCompletionChunk(
    List<ChatMessage> messages, 
    String modelKey, 
    String apiKey, 
    String baseUrl
) {
    // 动态创建ArkService实例
    ArkService dynamicArkService = ArkService.builder()
            .baseUrl(baseUrl != null ? baseUrl : this.baseUrl)
            .apiKey(apiKey != null ? apiKey : this.apiKey)
            .build();

    BotChatCompletionRequest request = BotChatCompletionRequest.builder()
            .model(modelKey)
            .messages(messages)
            .maxTokens(1000)
            .temperature(0.7)
            .build();

    // 直接返回豆包的Flowable
    return dynamicArkService.streamBotChatCompletion(request);
}
```

#### getChatCompletionChunk (简化版本)
```java
public Flowable<BotChatCompletionChunk> getChatCompletionChunk(
    List<ChatMessage> messages, 
    String modelKey
) {
    return getChatCompletionChunk(messages, modelKey, null, null);
}
```

### 2. AiConversationService 新增方法

#### sendChatMessageStreamV2
```java
public Flowable<AiChatMessageSendRespVO> sendChatMessageStreamV2(AiChatMessageSendReqVO sendReqVO) {
    // 1. 验证对话和用户
    // 2. 获取角色和模型配置
    // 3. 创建用户消息和助手消息
    // 4. 获取带限制的上下文消息
    // 5. 构建消息列表
    // 6. 调用AI服务
    // 7. 处理流式响应
}
```

#### getContextMessagesWithLimit
```java
private List<ChatMessage> getContextMessagesWithLimit(
    Long conversationId, 
    Boolean useContext, 
    Integer maxTokens
) {
    // 获取历史消息
    // 根据maxTokens限制上下文
    // Token估算和消息筛选
    // 优先保留最近的对话
}
```

#### estimateTokens
```java
private int estimateTokens(String text) {
    // 中文字符计数 * 2
    // 英文单词计数 * 1
    // 返回估算的Token数量
}
```

### 3. 模型配置管理

#### getModelByRoleId
```java
private AiModelVO getModelByRoleId(Long roleId) {
    // 根据角色ID获取模型配置
    // 支持默认模型兜底
    // 模型验证和错误处理
}
```

#### getDefaultModel
```java
private AiModelVO getDefaultModel() {
    // 获取第一个可用的聊天模型
    // 如果没有，创建默认配置
}
```

## API接口

### 新增流式聊天接口
```bash
POST /sys/ai/chat/stream/send
Content-Type: application/json
Accept: application/x-ndjson

{
  "conversationId": 1,
  "content": "你好，请介绍一下你自己",
  "roleId": 2,
  "useContext": true
}
```

### 响应格式
```json
{
  "userMessageId": 123,
  "assistantMessageId": 124,
  "content": "你好！",
  "fullContent": "你好！我是",
  "conversationId": 1,
  "modelName": "豆包-4o",
  "finished": false
}
```

## 技术优势

### 1. 性能优化
- **直接流式传输**：减少中间处理层，直接返回SDK原生Flowable
- **智能上下文管理**：根据模型能力动态调整上下文长度
- **Token估算**：避免超出模型限制，提高响应质量

### 2. 灵活性
- **动态模型配置**：支持运行时切换不同AI模型
- **角色模型绑定**：不同角色可以使用不同的模型
- **配置热更新**：无需重启服务即可更新模型配置

### 3. 可靠性
- **错误处理**：完善的异常处理和错误恢复机制
- **日志记录**：详细的操作日志，便于问题排查
- **兜底机制**：默认模型配置，确保服务可用性

### 4. 扩展性
- **模块化设计**：各功能模块独立，易于扩展
- **接口标准化**：统一的请求响应格式
- **配置化管理**：通过数据库配置管理模型和角色

## 使用场景

### 1. 多角色对话
```javascript
// 编程助手对话
{
  "conversationId": 1,
  "content": "帮我写一个Java排序算法",
  "roleId": 2, // 编程助手角色，使用代码专用模型
  "useContext": true
}

// 写作助手对话
{
  "conversationId": 2,
  "content": "帮我写一篇关于AI的文章",
  "roleId": 3, // 写作助手角色，使用文本生成模型
  "useContext": true
}
```

### 2. 长对话管理
```javascript
// 启用上下文限制，避免超出模型限制
{
  "conversationId": 1,
  "content": "继续刚才的话题",
  "roleId": 1,
  "useContext": true // 自动根据模型maxTokens限制上下文
}
```

### 3. 模型切换
```javascript
// 不同角色使用不同模型
// 角色1: 使用豆包-4o (快速响应)
// 角色2: 使用豆包-128k (长文本处理)
// 角色3: 使用GPT-4 (高质量输出)
```

## 配置示例

### 1. 角色模型配置
```sql
-- 编程助手使用豆包-4o
UPDATE ai_role SET model_id = 1 WHERE id = 2;

-- 写作助手使用豆包-128k
UPDATE ai_role SET model_id = 3 WHERE id = 3;

-- 翻译助手使用GPT-4
UPDATE ai_role SET model_id = 4 WHERE id = 4;
```

### 2. 模型配置
```sql
-- 豆包-4o: 快速响应，32k上下文
-- 豆包-128k: 长文本处理，128k上下文
-- GPT-4: 高质量输出，8k上下文
```

## 监控和日志

### 1. 关键日志
- 对话创建和验证
- 模型选择和配置
- 上下文限制和Token估算
- 流式响应状态
- 错误和异常处理

### 2. 性能指标
- 响应时间
- Token使用量
- 模型切换频率
- 错误率统计

## 总结

新的流式聊天接口V2实现了：

🎯 **动态模型配置**：支持运行时模型切换和角色绑定  
🎯 **智能上下文管理**：根据模型能力自动调整上下文长度  
🎯 **性能优化**：直接返回SDK原生流式响应  
🎯 **完善的错误处理**：确保服务稳定性和可靠性  
🎯 **灵活的配置管理**：支持多种使用场景和需求  

这为构建更智能、更高效的AI对话系统提供了强大的技术基础！
