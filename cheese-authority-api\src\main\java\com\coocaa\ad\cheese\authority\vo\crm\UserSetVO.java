package com.coocaa.ad.cheese.authority.vo.crm;

import groovyjarjarantlr4.v4.runtime.misc.NotNull;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月21 10:30
 */
@Data
@Accessors(chain = true)
public class UserSetVO {

    @Schema(description = "部门ID")
    private Long deptId;


    @Schema(description = "姓名")
    private String realname;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "用户ID", required = true)
    private Long userId;

    @Schema(description = "性别，0 未选择 1、男 2、女", required = true, allowableValues = "0,1,2")
    private Integer sex;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "状态,0禁用,1正常,2未激活")
    private Integer status;

    @Schema(description = "岗位")
    private String post;

    @Schema(description = "上级ID")
    private Long parentId;

    @Schema(description = "上级名称")
    private String parentName;

    @Schema(description = "角色ID")
    private String roleId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "用户头像")
    private String img;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否超级管理员")
    private Boolean isAdmin;

    @Schema(description = "用户标识,0 主账号 1 部门负责人 2 普通用户")
    private Integer userIdentity = 2;

    @Schema(description = "软呼标识")
    private Integer hisTable;


    @Schema(description = "是否绑定企业微信")
    private Integer isBindingWork;

    @Schema(description = "企业微信绑定的手机号")
    private String wxMobile;

    @Schema(description = "登录类型")
    private Integer loginType;

    @Schema(description = "附属部门id列表")
    private List<Long> subsidiaryDeptIds;

    @Schema(description = "企业名称")
    private String companyName;


    @Schema(description = "附属部门")
    private String subsidiaryDeptName;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "第三方关联userid")
    private String wxUserId;


    //----------------------------------------

    @Schema(description = "是否绑定飞书 默认0未绑定 1已绑定")
    private Integer bindFeiShuFlag;


    @Schema(description = "empCode")
    private String empCode;

}
