package com.coocaa.ad.cheese.authority.common.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coocaa.ad.cheese.authority.common.db.entity.CityGroupEntity;
import com.coocaa.ad.cheese.authority.common.db.mapper.CityGroupMapper;
import com.coocaa.ad.cheese.authority.common.db.service.ICityGroupService;
import com.coocaa.ad.cheese.authority.common.tools.utils.MathUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 城市组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class CityGroupServiceImpl extends ServiceImpl<CityGroupMapper, CityGroupEntity> implements ICityGroupService {

    @Override
    public int groupCount() {
        Long count = lambdaQuery().count();
        return MathUtils.unBox(count);
    }

    @Override
    public CityGroupEntity queryByName(String name) {
        return lambdaQuery().eq(CityGroupEntity::getName,name).one();
    }

    @Override
    public List<CityGroupEntity> listByParentId(Integer parentId) {
        return lambdaQuery().eq(CityGroupEntity::getParentId,parentId).
                orderByAsc(CityGroupEntity::getRank).list();

    }

    @Override
    public List<CityGroupEntity> listEnableCityGroup() {
        return lambdaQuery().eq(CityGroupEntity::getStatus,true).orderByAsc(CityGroupEntity::getRank).list();
    }

    @Override
    public List<CityGroupEntity> listByIdsEnable(Set<Integer> groupIds) {
        return lambdaQuery().eq(CityGroupEntity::getStatus,true)
                .in(CityGroupEntity::getId,groupIds).list();
    }

    @Override
    public List<CityGroupEntity> listByParentIds(List<Integer> list) {
         if(CollectionUtils.isNotEmpty(list)){
             return lambdaQuery().in(CityGroupEntity::getParentId,list)
                     .list();
         }
         return new ArrayList<>();
    }
}
