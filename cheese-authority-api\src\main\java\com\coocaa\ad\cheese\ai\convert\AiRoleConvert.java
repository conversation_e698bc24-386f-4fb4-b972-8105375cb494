package com.coocaa.ad.cheese.ai.convert;

import com.coocaa.ad.cheese.ai.common.db.entity.AiRoleEntity;
import com.coocaa.ad.cheese.ai.vo.AiRoleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * AI角色转换器
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Mapper
public interface AiRoleConvert {

    AiRoleConvert INSTANCE = Mappers.getMapper(AiRoleConvert.class);

    /**
     * 实体转VO
     *
     * @param entity 实体
     * @return VO
     */
    AiRoleVO toVo(AiRoleEntity entity);
}
