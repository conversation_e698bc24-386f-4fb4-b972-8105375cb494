package com.coocaa.ad.cheese.authority.common.db.service.approve;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.bean.InstanceSearchDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalInstanceEntity;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IApproveInternalInstanceService extends IService<ApproveInternalInstanceEntity> {

    /**
     * 分页查询审批单
     */
    IPage<ApproveInternalInstanceEntity> listForTask(IPage<ApproveInternalInstanceEntity> page, InstanceSearchDTO dto);
}
