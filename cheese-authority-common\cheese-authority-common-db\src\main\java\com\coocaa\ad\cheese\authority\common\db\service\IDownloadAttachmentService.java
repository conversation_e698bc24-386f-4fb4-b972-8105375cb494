package com.coocaa.ad.cheese.authority.common.db.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.bean.DownloadAttachmentDTO;
import com.coocaa.ad.cheese.authority.common.db.entity.DownloadAttachmentEntity;

/**
 * 下载附件服务接口
 *
 * <AUTHOR>
 * @since 2025-5-26
 */
public interface IDownloadAttachmentService extends IService<DownloadAttachmentEntity> {

    /**
     * 分页查询
     *
     * @param downloadAttachment 查询条件
     * @param page               分页对象
     * @return 分页结果
     */
    IPage<DownloadAttachmentEntity> pageList(IPage<DownloadAttachmentEntity> page, DownloadAttachmentDTO downloadAttachment);
}