package com.coocaa.ad.cheese.authority.common.db.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @program: cheese-authority
 * @ClassName CityDetailVO
 * @description:
 * @author: z<PERSON><PERSON><PERSON>n
 * @create: 2025-04-23 10:31
 * @Version 1.0
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class CityDetailVO {

    private String cityName;

    private Integer cityId;

    private String districtName;

    private Integer districtId;

}
