package com.coocaa.ad.cheese.authority.vo.crm;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年12月25 14:18
 */
@Data
@Accessors(chain = true)
public class UserDetailResultVO extends CrmResultVO{


    private UserDetailVO data;


    @Data
    @Accessors(chain = true)
    public static class UserDetailVO {

        /**
         * 用户ID
         */
        private Long userId;

        /**
         * 用户名
         */
        private String username;

        /**
         * 头像
         */
        private String img;

        /**
         * 手机
         */
        private String phone;

        /**
         * 岗位
         */
        private String post;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 用户昵称
         */
        private String realname;

        /**
         * 部门ID
         */
        private Long deptId;

        /**
         * 部门名称
         */
        private String deptName;

        /**
         * 用户状态
         */
        private Integer status;

        /**
         * 用户上级名称
         */
        private String parentName;

        /**
         * 上级用户ID
         */
        private Long parentId;

        /**
         * 企业ID
         */


        /**
         * 企业名称
         */
        private String companyName;

        /**
         * 登录类型
         */
        private Integer loginType = 1;

        private Integer type = 1;

        /**
         * 访问类型
         */
        private Integer accessType;


        /**
         * 当前用户角色列表
         */
        private List<Long> roles;

        /**
         * 当前企业创建人用户ID
         */
        private Long superUserId;

        /**
         * 当前企业超管角色ID
         */
        private Long superRoleId;

        /**
         * 微信(飞书、钉钉)用户ID
         */
        private String wxUserId;

        /**
         * 微信OpenId（钉钉unionId）
         */
        private String wxOpenId;

        //---------------------------------
        /**
         * 飞书openid
         */
        private String feiShuOpenId;

        private String feiShuUserId;

        private String empCode;

    }



}
