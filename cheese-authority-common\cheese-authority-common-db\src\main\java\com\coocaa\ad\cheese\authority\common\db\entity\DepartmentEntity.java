package com.coocaa.ad.cheese.authority.common.db.entity;

import cn.hutool.json.JSON;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 部门信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("department")
public class DepartmentEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 部门ID
     */
    private String departmentId;

    /**
     * 父部门ID
     */
    private String parentDepartmentId;

    /**
     * 部门的 open_department_id
     */
    private String openDepartmentId;

    /**
     * 所有部门信息
     */
    private String parentDepartmentList;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门主管的用户 ID
     */
    private String leaderUserId;

    /**
     * 部门的排序,取值越小排序越靠前。
     */
    private String rank;

    /**
     * 是否被删除:true-是；false-否
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private Integer operator;


}
