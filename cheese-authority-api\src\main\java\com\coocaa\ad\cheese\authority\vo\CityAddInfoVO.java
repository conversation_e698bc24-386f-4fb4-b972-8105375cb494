package com.coocaa.ad.cheese.authority.vo;

import com.coocaa.ad.cheese.authority.vo.common.DictCodeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/24
 */
@Data
public class CityAddInfoVO {
    @Schema(description = "上级组名称，一级的时候这个值为空")
    private String parentGroupName = "";
    private List<DictCodeVO> cityCodes;
}
