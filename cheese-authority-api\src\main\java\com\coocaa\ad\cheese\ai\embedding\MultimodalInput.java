package com.coocaa.ad.cheese.ai.embedding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

/**
 * 多模态输入数据类
 * 
 * 支持文本、图像、视频的混合输入
 * 参考豆包AI图像向量化API文档
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@Builder
@Jacksonized
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultimodalInput {

    /**
     * 输入类型：text, image_url, video_url
     */
    @JsonProperty("type")
    private String type;

    /**
     * 文本内容（当type为text时使用）
     */
    @JsonProperty("text")
    private String text;

    /**
     * 图像URL配置（当type为image_url时使用）
     */
    @JsonProperty("image_url")
    private ImageUrl imageUrl;

    /**
     * 视频URL配置（当type为video_url时使用）
     */
    @JsonProperty("video_url")
    private VideoUrl videoUrl;

    /**
     * 图像URL配置
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ImageUrl {
        /**
         * 图像URL或Base64编码
         */
        @JsonProperty("url")
        private String url;

        /**
         * 图像质量设置
         */
        @JsonProperty("detail")
        private String detail;
    }

    /**
     * 视频URL配置
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class VideoUrl {
        /**
         * 视频URL或Base64编码
         */
        @JsonProperty("url")
        private String url;

        /**
         * 视频帧率设置
         */
        @JsonProperty("fps")
        private Integer fps;

        /**
         * 视频时长限制（秒）
         */
        @JsonProperty("max_duration")
        private Integer maxDuration;
    }

    /**
     * 创建文本输入
     */
    public static MultimodalInput text(String text) {
        return MultimodalInput.builder()
                .type("text")
                .text(text)
                .build();
    }

    /**
     * 创建图像URL输入
     */
    public static MultimodalInput imageUrl(String url) {
        return MultimodalInput.builder()
                .type("image_url")
                .imageUrl(ImageUrl.builder().url(url).build())
                .build();
    }

    /**
     * 创建图像URL输入（带质量设置）
     */
    public static MultimodalInput imageUrl(String url, String detail) {
        return MultimodalInput.builder()
                .type("image_url")
                .imageUrl(ImageUrl.builder().url(url).detail(detail).build())
                .build();
    }

    /**
     * 创建Base64图像输入
     */
    public static MultimodalInput imageBase64(String base64Data, String format) {
        String dataUrl = String.format("data:image/%s;base64,%s", format, base64Data);
        return MultimodalInput.builder()
                .type("image_url")
                .imageUrl(ImageUrl.builder().url(dataUrl).build())
                .build();
    }

    /**
     * 创建视频URL输入
     */
    public static MultimodalInput videoUrl(String url) {
        return MultimodalInput.builder()
                .type("video_url")
                .videoUrl(VideoUrl.builder().url(url).build())
                .build();
    }

    /**
     * 创建视频URL输入（带配置）
     */
    public static MultimodalInput videoUrl(String url, Integer fps, Integer maxDuration) {
        return MultimodalInput.builder()
                .type("video_url")
                .videoUrl(VideoUrl.builder()
                        .url(url)
                        .fps(fps)
                        .maxDuration(maxDuration)
                        .build())
                .build();
    }

    /**
     * 创建Base64视频输入
     */
    public static MultimodalInput videoBase64(String base64Data, String format) {
        String dataUrl = String.format("data:video/%s;base64,%s", format, base64Data);
        return MultimodalInput.builder()
                .type("video_url")
                .videoUrl(VideoUrl.builder().url(dataUrl).build())
                .build();
    }

    /**
     * 验证输入数据的有效性
     */
    public boolean isValid() {
        if (type == null) {
            return false;
        }

        switch (type) {
            case "text":
                return text != null && !text.trim().isEmpty();
            case "image_url":
                return imageUrl != null && imageUrl.getUrl() != null && !imageUrl.getUrl().trim().isEmpty();
            case "video_url":
                return videoUrl != null && videoUrl.getUrl() != null && !videoUrl.getUrl().trim().isEmpty();
            default:
                return false;
        }
    }

    /**
     * 获取输入内容的描述
     */
    public String getDescription() {
        switch (type) {
            case "text":
                return "文本: " + (text.length() > 50 ? text.substring(0, 50) + "..." : text);
            case "image_url":
                return "图像: " + imageUrl.getUrl();
            case "video_url":
                return "视频: " + videoUrl.getUrl();
            default:
                return "未知类型: " + type;
        }
    }
}
