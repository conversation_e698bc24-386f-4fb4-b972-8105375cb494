package com.coocaa.ad.cheese.authority.common.tools.utils.loginLog;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.function.BiConsumer;

/**
 *
 * 登录日志工具类
 *
 * <AUTHOR>
 * @date 2024年11月25 17:51
 */
@Slf4j
public final class LoginLogUtils {
    private static final ObjectMapper JSON_MAPPER = new ObjectMapper();
    private volatile KafkaProducer<String, String> kafkaProducer;
    private static String topicName = "cheese-login-log";
    private static Properties queueConfig = null;


    /**
     * 记录登录日志
     *
     * @param loginLog 登录日志
     * @return true:调用成功
     */
    public static boolean log(LoginLogDTO loginLog) {
        return log(loginLog, null);
    }

    /**
     * 记录登录登录日志
     *
     * @param loginLog 登录日志
     * @param callback   回调函数
     * @return true:调用成功
     */
    public static boolean log(LoginLogDTO loginLog, BiConsumer<RecordMetadata, Exception> callback) {
        if (Objects.isNull(loginLog) || !isInvalid(loginLog)) {
            return false;
        }

        try {
            ProducerRecord<String, String> record = new ProducerRecord<>(topicName, loginLog.getSystemCode(), JSON_MAPPER.writeValueAsString(loginLog));
            Optional.ofNullable(instance().getKafkaProducer()).ifPresent((producer) -> {
                log.info("记录登录日志: {}", record.value());
                if (Objects.isNull(callback)) {
                    producer.send(record);
                } else {
                    producer.send(record, callback::accept);
                }
            });
            return true;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 设置队列配置
     */
    static void setQueueConfig(Properties queueConfig, String topicName) {
        LoginLogUtils.queueConfig = queueConfig;
        LoginLogUtils.topicName = topicName;
    }

    private LoginLogUtils() {
    }

    private static LoginLogUtils instance() {
        return InstanceHolder.INSTANCE;
    }

    private KafkaProducer<String, String> getKafkaProducer() {
        if (Objects.isNull(this.kafkaProducer)) {
            synchronized (this) {
                if (Objects.isNull(this.kafkaProducer)) {
                    if (Objects.isNull(queueConfig) || !queueConfig.containsKey("bootstrap.servers")) {
                        log.error("[登录日志] - 读取消息队列配置失败！");
                        return null;
                    }
                    this.kafkaProducer = new KafkaProducer<>(queueConfig);
                }
            }
        }

        return this.kafkaProducer;
    }

    /**
     * 检查登录日志是否有效
     */
    private static boolean isInvalid(LoginLog loginLog) {
        if (ObjectUtil.isNull(loginLog.getUserId()) || StringUtils.isBlank(loginLog.getUserName())) {
            log.warn("[登录日志] - 用户ID/用户名 不能为空！ 详情: {}", loginLog);
            return false;
        }

        return true;
    }

    /**
     * 静态内部类实现单例
     */
    private static class InstanceHolder {
        public static final LoginLogUtils INSTANCE = new LoginLogUtils();

        private InstanceHolder() {
        }
    }
}
