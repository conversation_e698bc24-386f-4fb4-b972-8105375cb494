package com.coocaa.ad.cheese.ai.service;

import com.coocaa.ad.cheese.ai.embedding.DoubaoEmbeddingModel;
import com.coocaa.ad.cheese.ai.embedding.MultimodalInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 多模态嵌入服务
 * 
 * 支持文本、图像、视频的向量化处理
 * 参考豆包AI图像向量化API
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MultimodalEmbeddingService {

    private final EmbeddingModel embeddingModel;

    /**
     * 文本向量化
     */
    public List<Double> embedText(String text) {
        log.info("文本向量化，文本长度: {}", text.length());
        
        List<MultimodalInput> inputs = List.of(MultimodalInput.text(text));
        return embedMultimodal(inputs);
    }

    /**
     * 图像URL向量化
     */
    public List<Double> embedImageUrl(String imageUrl) {
        log.info("图像URL向量化: {}", imageUrl);
        
        List<MultimodalInput> inputs = List.of(MultimodalInput.imageUrl(imageUrl));
        return embedMultimodal(inputs);
    }

    /**
     * 图像文件向量化
     */
    public List<Double> embedImageFile(MultipartFile imageFile) throws IOException {
        log.info("图像文件向量化，文件名: {}, 大小: {} bytes", 
                imageFile.getOriginalFilename(), imageFile.getSize());

        // 验证文件类型
        String contentType = imageFile.getContentType();
        if (!isValidImageType(contentType)) {
            throw new IllegalArgumentException("不支持的图像格式: " + contentType);
        }

        // 转换为Base64
        byte[] imageBytes = imageFile.getBytes();
        String base64Data = Base64.getEncoder().encodeToString(imageBytes);
        String format = extractImageFormat(contentType);

        List<MultimodalInput> inputs = List.of(MultimodalInput.imageBase64(base64Data, format));
        return embedMultimodal(inputs);
    }

    /**
     * 视频URL向量化
     */
    public List<Double> embedVideoUrl(String videoUrl) {
        log.info("视频URL向量化: {}", videoUrl);
        
        List<MultimodalInput> inputs = List.of(MultimodalInput.videoUrl(videoUrl));
        return embedMultimodal(inputs);
    }

    /**
     * 视频文件向量化
     */
    public List<Double> embedVideoFile(MultipartFile videoFile) throws IOException {
        log.info("视频文件向量化，文件名: {}, 大小: {} bytes", 
                videoFile.getOriginalFilename(), videoFile.getSize());

        // 验证文件类型和大小
        String contentType = videoFile.getContentType();
        if (!isValidVideoType(contentType)) {
            throw new IllegalArgumentException("不支持的视频格式: " + contentType);
        }

        if (videoFile.getSize() > 50 * 1024 * 1024) { // 50MB限制
            throw new IllegalArgumentException("视频文件大小超过50MB限制");
        }

        // 转换为Base64
        byte[] videoBytes = videoFile.getBytes();
        String base64Data = Base64.getEncoder().encodeToString(videoBytes);
        String format = extractVideoFormat(contentType);

        List<MultimodalInput> inputs = List.of(MultimodalInput.videoBase64(base64Data, format));
        return embedMultimodal(inputs);
    }

    /**
     * 图文混合向量化
     */
    public List<Double> embedTextAndImage(String text, String imageUrl) {
        log.info("图文混合向量化，文本长度: {}, 图像URL: {}", text.length(), imageUrl);
        
        List<MultimodalInput> inputs = List.of(
                MultimodalInput.text(text),
                MultimodalInput.imageUrl(imageUrl)
        );
        return embedMultimodal(inputs);
    }

    /**
     * 视频、图像、文本混合向量化
     */
    public List<Double> embedMultimodal(String text, String imageUrl, String videoUrl) {
        log.info("多模态混合向量化，文本长度: {}, 图像URL: {}, 视频URL: {}", 
                text != null ? text.length() : 0, imageUrl, videoUrl);
        
        List<MultimodalInput> inputs = new ArrayList<>();
        
        if (StringUtils.hasText(text)) {
            inputs.add(MultimodalInput.text(text));
        }
        if (StringUtils.hasText(imageUrl)) {
            inputs.add(MultimodalInput.imageUrl(imageUrl));
        }
        if (StringUtils.hasText(videoUrl)) {
            inputs.add(MultimodalInput.videoUrl(videoUrl));
        }

        if (inputs.isEmpty()) {
            throw new IllegalArgumentException("至少需要提供一种模态的输入");
        }

        return embedMultimodal(inputs);
    }

    /**
     * 通用多模态向量化
     */
    public List<Double> embedMultimodal(List<MultimodalInput> inputs) {
        log.info("多模态向量化，输入数量: {}", inputs.size());

        try {
            // 检查是否为DoubaoEmbeddingModel
            if (embeddingModel instanceof DoubaoEmbeddingModel) {
                DoubaoEmbeddingModel doubaoModel = (DoubaoEmbeddingModel) embeddingModel;
                
                // 检查多模态支持
                if (!doubaoModel.isMultimodalSupported()) {
                    log.warn("当前模型不支持多模态，仅处理文本内容");
                }
                
                return doubaoModel.embedMultimodal(inputs);
            } else {
                // 回退到文本处理
                log.warn("当前嵌入模型不支持多模态，回退到文本处理");
                return embedTextFallback(inputs);
            }

        } catch (Exception e) {
            log.error("多模态向量化失败", e);
            throw new RuntimeException("多模态向量化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 文本回退处理
     */
    private List<Double> embedTextFallback(List<MultimodalInput> inputs) {
        StringBuilder textContent = new StringBuilder();
        
        for (MultimodalInput input : inputs) {
            if ("text".equals(input.getType())) {
                textContent.append(input.getText()).append(" ");
            } else {
                textContent.append("[").append(input.getType()).append("] ");
            }
        }

        if (textContent.length() == 0) {
            throw new IllegalArgumentException("没有找到可处理的内容");
        }

        // 使用标准的Spring AI接口
        org.springframework.ai.document.Document document = 
                new org.springframework.ai.document.Document(textContent.toString().trim());
        
        return embeddingModel.embed(document);
    }

    /**
     * 验证图像类型
     */
    private boolean isValidImageType(String contentType) {
        if (contentType == null) return false;
        
        return contentType.equals("image/jpeg") ||
               contentType.equals("image/png") ||
               contentType.equals("image/gif") ||
               contentType.equals("image/webp") ||
               contentType.equals("image/bmp") ||
               contentType.equals("image/tiff") ||
               contentType.equals("image/x-icon");
    }

    /**
     * 验证视频类型
     */
    private boolean isValidVideoType(String contentType) {
        if (contentType == null) return false;
        
        return contentType.equals("video/mp4") ||
               contentType.equals("video/avi") ||
               contentType.equals("video/quicktime");
    }

    /**
     * 提取图像格式
     */
    private String extractImageFormat(String contentType) {
        if (contentType == null) return "jpeg";
        
        switch (contentType) {
            case "image/jpeg": return "jpeg";
            case "image/png": return "png";
            case "image/gif": return "gif";
            case "image/webp": return "webp";
            case "image/bmp": return "bmp";
            case "image/tiff": return "tiff";
            case "image/x-icon": return "ico";
            default: return "jpeg";
        }
    }

    /**
     * 提取视频格式
     */
    private String extractVideoFormat(String contentType) {
        if (contentType == null) return "mp4";
        
        switch (contentType) {
            case "video/mp4": return "mp4";
            case "video/avi": return "avi";
            case "video/quicktime": return "mov";
            default: return "mp4";
        }
    }

    /**
     * 获取支持的模态类型
     */
    public List<String> getSupportedModalities() {
        if (embeddingModel instanceof DoubaoEmbeddingModel) {
            return ((DoubaoEmbeddingModel) embeddingModel).getSupportedModalities();
        } else {
            return List.of("text");
        }
    }

    /**
     * 检查是否支持多模态
     */
    public boolean isMultimodalSupported() {
        if (embeddingModel instanceof DoubaoEmbeddingModel) {
            return ((DoubaoEmbeddingModel) embeddingModel).isMultimodalSupported();
        } else {
            return false;
        }
    }
}
