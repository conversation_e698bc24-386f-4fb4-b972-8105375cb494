package com.coocaa.ad.cheese.authority.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/23
 */
@Data
public class CityVO {

    private Integer parentId;

    private Integer id;

    @Schema(description = "城市或者区县名称")
    private String name;

    @Schema(description = "国际编码")
    private String gbCode;

    @Schema(description = "业务编码")
    private String bzCode;

    @Schema(description = "是否开通")
    private Boolean enable;

    @Schema(description = "城市下属区县 ")
    private List<CityVO> subCounty;


}
