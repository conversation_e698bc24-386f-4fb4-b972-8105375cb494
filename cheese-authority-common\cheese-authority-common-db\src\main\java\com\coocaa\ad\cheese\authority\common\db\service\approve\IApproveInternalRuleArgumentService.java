package com.coocaa.ad.cheese.authority.common.db.service.approve;

import com.baomidou.mybatisplus.extension.service.IService;
import com.coocaa.ad.cheese.authority.common.db.entity.approve.ApproveInternalRuleArgumentEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface IApproveInternalRuleArgumentService extends IService<ApproveInternalRuleArgumentEntity> {

    /**
     * 根据规则编号获取规则字段
     */
    List<ApproveInternalRuleArgumentEntity> getArgByRuleNo(Integer ruleNo);
}
