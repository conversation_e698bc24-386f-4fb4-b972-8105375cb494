package com.coocaa.ad.cheese.authority.vo.crm;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.lark.oapi.service.security_and_compliance.v1.model.SimpleUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 部门查询对象
 */
@Data
@Accessors(chain = true)
public class AdminDeptVO {

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "上级部门ID，0为最上级")
    private Long parentId;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "部门负责人")
    private Long ownerUserId;

    @Schema(description = "部门类型 1 公司 2 部门")
    private Integer type;

    @Schema(description = "下级部门列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AdminDeptVO> children;

    @Schema(description = "部门下员工列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SimpleUser> userList;

    @Schema(description = "加密KEY，查询其他企业部门时使用")
    private String secureKey;

    @Schema(description = "当前部门在职人数")
    private Integer currentNum;

    public Integer getType() {
        //默认是部门
        return type != null ? type : 2;
    }
}
