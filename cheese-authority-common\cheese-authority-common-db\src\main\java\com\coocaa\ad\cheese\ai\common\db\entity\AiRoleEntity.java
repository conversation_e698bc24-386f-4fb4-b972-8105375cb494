package com.coocaa.ad.cheese.ai.common.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * AI角色实体
 *
 * <AUTHOR>
 * @since 2025-7-17
 */
@Data
@TableName("ai_role")
public class AiRoleEntity implements Serializable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色提示词
     */
    private String rolePrompt;

    /**
     * 模型编号
     *
     * 关联 ai_model 表的 id 字段
     */
    @TableField("model_id")
    private Long modelId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer creator;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Integer operator;

    /**
     * 删除标记（0-正常，1-删除）
     */
    private Integer deleteFlag;
}
