package com.coocaa.ad.cheese.authority.common.db.entity;

import lombok.Data;


@Data
public class UserCompanyDepartmentEntity {

    /**
     * ID
     */
    private Integer userDepartmentRelationId;


    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 部门ID
     */
    private String departmentOpenId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司ID
     */
    private String companyOpenId;
    /**
     * 父级公司openID列表
     */
    private String parentDepartmentList;
}
